---
description:
globs:
alwaysApply: true
---
- 该项目是基于Cool-Admin-Node二次开发的项目，所有我提到的问题优先从本地代码中寻找参考范例并提供解决方案
- 该项目遵循Cool-Admin相关的规则，我说提到的问题如本地没有合适的解决方案，请参考官方文档：https://node.cool-admin.com/src/guide/quick.html
- 对于我说提到的问题，请在类似的模块寻找参考代码。例如：当我需要修改controller/admin文件夹的内容时，请参考其它模块同名文件代码给我提供解决方案
- 该项目各模块/页面的代码都放在src/modules/moduleXXX下面，其中controller：接口入口，负责请求分发和响应；entity：数据结构定义，负责数据库表与对象的映射；service：业务逻辑实现，负责具体的功能处理。
- src/utils/：自定义工具函数，通用逻辑应沉淀于此，避免在页面/模块内重复实现。
- 页面主入口为 views/xxx.vue，只负责布局、模块组合和少量页面级逻辑。
- 页面内如需用到工具函数，优先从 src/utils/ 引入。
- 只需要对功能进行增加、修改，无需创建任何的测试代码、文档

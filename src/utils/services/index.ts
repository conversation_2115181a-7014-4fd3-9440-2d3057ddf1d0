import Request from './request'

export const whmcsCdAxios = new Request({
	baseURL: process.env.WHMCS_CD_API_URL,
	timeout: 60000, // 60秒超时
	interceptors: {
		requestSuccess(config) {
			config.params = {
				...config.params,
				identifier: process.env.WHMCS_CD_API_IDENTIFIER,
				secret: process.env.WHMCS_CD_API_SECRET,
				responsetype: 'json',
			}

			return config
		},
	},
})

export const discordAxios = new Request({
	baseURL: process.env.DISCORD_API_URL,
	timeout: 10000,
	interceptors: {
		requestSuccess(config) {
			return config
		},
		responseFailure: (err) => {
			throw new Error(err.message)
		},
	},
})

export const discordBotAxios = new Request({
	baseURL: process.env.DISCORD_API_URL,
	timeout: 10000,
	headers: {
		Authorization: `Bot ${process.env.DISCORD_BOT_TOKEN}`,
	},
	interceptors: {
		requestSuccess(config) {
			return config
		},
		responseFailure: (err) => {
			throw new Error(err.message)
		},
	},
})

import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { Not, QueryRunner, Repository } from 'typeorm'

// entity
import { OrderEntity } from '/$/order/entity/order'
import { OrderReplyEntity } from '/$/order/entity/reply'
import { OrderLogEntity } from '/$/order/entity/order_log'
import { ServiceEntity } from '/$/service/entity/service'

// service
import { ServiceService } from '/$/service/service/service'
import { InjectEntityModel } from '@midwayjs/orm'
import { BaseSysUserEntity } from '/$/base/entity/sys/user'
import { SendMessageService } from '../send-message'
import { InvoiceService } from '/@/modules/invoice/service/invoice'

// env
import { FRONTEND_ADDRESS_ALL } from '/@/global/env'
import { handleDiscordReplyMessage } from '../../../utils'
import { whmcsCdApi } from '/@/vendor/whmcs-cd'
import { isExistValue } from '/@/utils'

import { OrderBillingCorrectFirstInvoiceService } from './correct-first-invoice'
import { OrderBillingRevisedInvoiceService } from './revised-invoice'

import * as _ from 'lodash'
import { OrderBillingCancelInvoiceService } from './cancel-invoice'
import ResponseMessage from '/@/utils/message'
import { TextEnum } from '../../../enums/textEnum'
import { AdminOrderProvisionService } from '../../provision'
import { ServiceItem } from '/@/modules/service/entity/service_item'
import { OrderItem } from '/$/order/entity/order_item'
import { OrderTypeEnum } from '../../../enums'
import { OrderItemStatusEnum, OrderItemStatusTagEnum } from '/$/order-v2/enums'
import { OrderItemService } from '../../order-item'
import { SyncService } from '../sync'

/**
 * 描述
 */
@Provide()
export class BillingService extends BaseService {
	@InjectEntityModel(BaseSysUserEntity)
	userEntity: Repository<BaseSysUserEntity>

	@Inject()
	serviceService: ServiceService

	@Inject()
	sendMessageService: SendMessageService

	@Inject()
	invoiceService: InvoiceService

	@Inject()
	cancelInvoiceService: OrderBillingCancelInvoiceService

	@Inject()
	correntFirstInvoiceService: OrderBillingCorrectFirstInvoiceService

	@Inject()
	revisedInvoiceService: OrderBillingRevisedInvoiceService

	@Inject()
	orderProvisionService: AdminOrderProvisionService

	@Inject()
	orderItemService: OrderItemService

	@Inject()
	syncService: SyncService

	@Inject()
	ctx

	@CoolTransaction()
	// billStart / 启动计费操作
	async handleBillStart(param, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const { username, userId } = this.ctx.admin

		const {
			orderId,
			orderItemId,
			assignedStaffId,
			assignedRoleId,
			replyMessage,
			billStartDate,
			billAmount,
			billCycle,
			billCurrency,
			billType,
			burstPrice,
			isRevised,
			orderStatus = OrderItemStatusEnum.CompletePending,
		} = param

		const { service, subservice, orderItem, ...order } = await this.orderProvisionService.detail(
			orderId,
			orderItemId,
			runner
		)

		// 1. 更新订单、更新当前订单项目状态
		try {
			await runner.manager.update(OrderEntity, order.id, { assignedRoleId, assignedStaffId, updateBy: username })

			await runner.manager
				.createQueryBuilder()
				.update(OrderItem)
				.set({ status: orderStatus })
				.where('id = :id OR parentItemId = :id', { id: orderItem.id })
				.execute()

			await this.orderItemService.addStatusTag(orderItem.id, [OrderItemStatusTagEnum.Billed], runner)

			message.success(TextEnum.ORDER_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error(TextEnum.ORDER_UPDATE_FAIL, e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 2. 更新服务主表，以及服务子表信息，只有在订单类型为New或者Change时才更新
		try {
			if (order.orderType === 'New' || order.orderType === 'Change') {
				await runner.manager.update(ServiceEntity, service.id, {
					// 只有订单类型为New时才更新firstBillDate、dueDate和nextInvoiceDate
					...(order.orderType === 'New' && {
						firstBillDate: billStartDate,
						dueDate: billStartDate,
						nextInvoiceDate: billStartDate,
					}),
					billCycle,
					billType,
					billAmount,
					billCurrency,
					burstPrice,
					billStatus: 'Active',
				})
			}
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error(TextEnum.SERVICE_UPDATE_FAIL, e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 3. 终止当前服务的上一个已计费的子服务
		try {
			await runner.manager.update(
				ServiceItem,
				{ serviceId: service.id, id: Not(subservice.id), billStatus: 'Active' },
				{ billStatus: 'Terminated', billStopDate: billStartDate }
			)

			message.success('已更新上一个OPS服务选项信息。')
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error('更新上一个OPS服务选项信息失败。', e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 4. 启用当前子服务的计费
		try {
			await runner.manager.update(ServiceItem, [{ id: subservice.id }, { parentItemId: subservice.id }], {
				billStartDate,
				billCycle,
				billType,
				billAmount,
				billCurrency,
				burstPrice,
				billStatus: 'Active',
			})

			message.success(TextEnum.SERVICE_OPTION_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error(TextEnum.SERVICE_OPTION_UPDATE_FAIL, e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 5. /order-detail/billStart 同步更新WHMCS-CD关联服务的计费信息
		try {
			const syncWhmcsOpts = {
				nextduedate: order.orderType === 'New' ? billStartDate : undefined, // 只有订单类型为New时才更新nextduedate
				firstpaymentamount: order.orderType === 'New' ? billAmount : undefined, // 只有订单类型为New时才更新firstpaymentamount
				recurringamount: billAmount,
				billingcycle: billCycle,
			}

			// 用户选择同步WHMCS-CD
			if (param.useSyncWHMCS) {
				try {
					await this.syncService.syncOrderToWhmcs({ orderId, orderItemId, ...syncWhmcsOpts }, message, runner)
					message.success('已同步WHMCS-CD服务信息。')
				} catch (e) {
					message.warning('未同步WHMCS-CD服务信息。', e.message)
				}
			} else if (isExistValue(service.cdWhServiceId)) {
				await whmcsCdApi.update('UpdateClientProduct', syncWhmcsOpts)
				await this.correntFirstInvoiceService.correctFirstInvoice(param, message)
				message.success('已更新WHMCS-CD服务信息。')
			}
		} catch (e) {
			runner.rollbackTransaction()
			message.fail().error('更新WHMCS-CD服务信息失败。', e.message)
			message.error('Rollback transaction.')
			return message.value()
		}

		// 6. 自动生成revised invoice
		if (
			isRevised &&
			order.orderType === OrderTypeEnum.CHANGE &&
			(billAmount !== service.billAmount || billCycle !== service.billCycle || billCurrency !== service.billCurrency)
		) {
			await this.revisedInvoiceService.revisedInvoice({ ...param, order, service, subservice }, message, runner)
		}

		// 7. 更新订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId,
				orderItemId,
				replyMessage,
				currentAction: 'Bill Start',
				currentOrderStatus: orderItem.status,
				newOrderStatus: orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success(TextEnum.ORDER_REPLY_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error(TextEnum.ORDER_REPLY_INSERT_FAIL, e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 8. 更新日志
		try {
			await runner.manager.insert(OrderLogEntity, {
				orderId,
				orderItemId,
				serviceId: service.id,
				actionType: 'Bill Start',
				actionBy: username,
				paramsJson: JSON.stringify(param),
				actionDetails: JSON.stringify({
					assignedStaffId,
					assignedRoleId,
					orderStatus,
				}),
			})

			message.success(TextEnum.ORDER_LOG_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error(TextEnum.ORDER_LOG_INSERT_FAIL, e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 9. 发送discord消息
		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, order.assignedStaffId)
			await this.sendMessageService.sendDiscord({
				blockquote: `${username}: <@${assignedUser?.discordId}> ${handleDiscordReplyMessage(replyMessage)}`,
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Billing start.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
			})
			message.success(TextEnum.DISCORD_MESSAGE_SUCCESS)
		} catch (e) {
			message.warning(TextEnum.DISCORD_MESSAGE_FAIL, e.message)
		}

		return message.ok('计费已开启').success('计费已开启').value()
	}

	@CoolTransaction()
	async handleBillStop(body, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const { username, userId } = this.ctx.admin

		const {
			orderId,
			orderItemId,
			assignedStaffId,
			assignedRoleId,
			billStopDate,
			replyMessage,
			orderStatus = OrderItemStatusEnum.CompletePending,
		} = body
		const { service, subservice, orderItem, ...order } = await this.orderProvisionService.detail(orderId, orderItemId)

		// 1. 更新订单、更新当前订单项目状态
		try {
			await runner.manager.update(
				OrderEntity,
				{ id: order.id },
				{ assignedRoleId, assignedStaffId, updateBy: username }
			)

			await runner.manager
				.createQueryBuilder()
				.update(OrderItem)
				.set({ status: orderStatus })
				.where('id = :id OR parentItemId = :id', { id: orderItem.id })
				.execute()

			await this.orderItemService.addStatusTag(orderItem.id, [OrderItemStatusTagEnum.Billed], runner)

			message.success(TextEnum.ORDER_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error(TextEnum.ORDER_UPDATE_FAIL, e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 4. 更新服务主表
		try {
			await runner.manager.update(ServiceEntity, { id: service.id }, { billStatus: 'Terminated', billStopDate })
			message.success('已更新关联服务信息。')
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message)
			message.error('更新关联服务信息失败。', e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 5. 更新服务子表
		try {
			await runner.manager.update(
				ServiceItem,
				{ serviceId: service.id, id: subservice.id },
				{ billStatus: 'Terminated', billStopDate }
			)
			message.success('已更新关联服务选项信息。')
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message).error('更新关联服务选项信息失败。', e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 6. /order-detail/billStop 同步更新WHMCS-CD关联服务的计费信息
		try {
			if (service.cdWhServiceId) {
				const result = await this.cancelInvoiceService.cancelInvalidInvoice({
					cdWhServiceId: service.cdWhServiceId,
					billStopDate,
				})

				message.success(result.resultMessage)
			}
		} catch (e) {
			runner.rollbackTransaction()
			message.fail(e.message).error('更新WHMCS-CD服务信息失败。', e.message)
			message.error(TextEnum.TRANSACTION_ROLLBACK)
			return message.value()
		}

		// 2. 更新订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId,
				orderItemId,
				replyMessage,
				currentAction: 'Bill Stop',
				currentOrderStatus: orderItem.status,
				newOrderStatus: orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success('已记录订单回复。')
		} catch (e) {
			message.warning(TextEnum.ORDER_REPLY_INSERT_FAIL, e.message)
		}

		// 3. 更新日志
		try {
			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Bill Stop',
				actionBy: username,
				paramsJson: JSON.stringify(body),
				actionDetails: JSON.stringify({
					assignedStaffId,
					assignedRoleId,
					orderStatus,
				}),
			})
			message.success(TextEnum.ORDER_LOG_INSERT_SUCCESS)
		} catch (e) {
			message.warning(TextEnum.ORDER_LOG_INSERT_FAIL, e.message)
		}

		// 6. Send discord message.
		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, order.assignedStaffId)
			await this.sendMessageService.sendDiscord({
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Billing stop.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
				blockquote: `${username}: <@${assignedUser?.discordId}> ${handleDiscordReplyMessage(replyMessage)}`,
			})
			message.success(TextEnum.DISCORD_MESSAGE_SUCCESS)
		} catch (e) {
			message.warning(TextEnum.DISCORD_MESSAGE_FAIL, e.message)
		}

		return message.ok('计费已停止。').success('计费已停止').value()
	}
}

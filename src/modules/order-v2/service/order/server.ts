import { InjectEntityModel } from '@midwayjs/orm'
import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { In, <PERSON>Null, Not, QueryRunner, Repository } from 'typeorm'
import { isArray, isString } from 'lodash'

// entity
import { OrderReplyEntity } from '/$/order/entity/reply'
import { OrderLogEntity } from '/$/order/entity/order_log'
import { TecServerEntity } from '/$/tec/entity/server'
import { TecProvisionLogEntity } from '/$/tec/entity/provision_log'
import { SendMessageService } from './send-message'

// env
import { FRONTEND_ADDRESS_ALL } from '../../../../global/env'
import { ServiceEntity } from '/@/modules/service/entity/service'
import { whmcsCdSql } from '/@/vendor/whmcs-cd'
import { ResponseMessage } from '/@/utils/message'
import { dcimServer } from '/@/vendor/dcim'
import { AdminOrderProvisionService } from '../../service/provision'
import { BaseSysUserEntity } from '/$/base/entity/sys/user'
/**
 * 描述
 */
@Provide()
export class ServerService extends BaseService {
	@Inject()
	sendMessageService: SendMessageService

	@InjectEntityModel(TecServerEntity)
	tecServerEntity: Repository<TecServerEntity>

	@Inject()
	orderProvisionService: AdminOrderProvisionService

	@Inject()
	ctx

	@CoolTransaction()
	async handleServerAssign(param, runner?: QueryRunner) {
		const { username, userId } = this.ctx.admin
		const { orderId, orderItemId, serverId, assignDetails } = param
		const { service, ...order } = await this.orderProvisionService.detail(orderId, orderItemId)

		const message = new ResponseMessage()

		// 1. 更新服务器
		try {
			await runner.manager.update(
				TecServerEntity,
				{ id: serverId },
				{ customerId: order.customerId, serviceId: service.id, status: 'Assigned', updateBy: username }
			)
			message.success('服务器更新成功')
		} catch (e) {
			message.fail(e.message)
			message.error('服务器更新失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 2. 更新订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId,
				orderItemId,
				replyMessage: assignDetails,
				currentAction: 'Server Assign',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: order.orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success('订单回复成功')
		} catch (e) {
			message.warning('订单回复失败', e.message)
		}

		// 3. 更新日志
		try {
			const serverRes = await runner.manager.findOne(TecServerEntity, serverId)

			await runner.manager.insert(OrderLogEntity, {
				orderId,
				orderItemId,
				serviceId: service.id,
				referenceIds: serverRes.serverNo,
				actionType: 'Server Assign',
				paramsJson: JSON.stringify(param),
				actionDetails: assignDetails,
				actionBy: username,
			})

			message.success('更新订单日志成功')
		} catch (e) {
			message.warning('更新订单日志失败', e.message)
		}

		// 4. 更新serverlog
		try {
			await runner.manager.insert(TecProvisionLogEntity, {
				userId,
				serverId,
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Server Assign',
			})
			message.success('ServerLog更新成功')
		} catch (e) {
			message.warning('ServerLog更新失败', e.message)
		}

		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, order.assignedStaffId)
			await this.sendMessageService.sendDiscord({
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Service assign.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
				blockquote: `${username}: <@${assignedUser?.discordId}>`,
			})
		} catch (e) {
			message.warning('发送discord消息失败', e.message)
		}

		return message.ok('服务器分配成功').success('服务器分配成功').value()
	}

	@CoolTransaction()
	async handleServerRelease(param, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const { username, userId } = this.ctx.admin
		const { orderId, orderItemId, releaseMessage } = param
		const serverIds = isArray(param.serverIds)
			? param.serverIds
			: isString(param.serverIds)
			? param.serverIds.split(',').filter(Boolean).map(Number)
			: []

		if (!serverIds.length) {
			message.fail('请选择要释放的服务器')
			return message.value()
		}

		const { service, ...order } = await this.orderProvisionService.detail(orderId, orderItemId, runner)

		// 1. 更新服务器
		try {
			await runner.manager.update(
				TecServerEntity,
				{ id: In(serverIds) },
				{
					status: 'Available',
					customerId: null,
					serviceId: null,
					updateBy: username,
				}
			)
			message.success('OPS服务器释放成功')
		} catch (e) {
			message.fail(e.message)
			message.error('OPS服务器释放失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 2. 更新订单回复、日志、serverlog
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId: order.id,
				orderItemId,
				currentAction: 'Server Release',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: order.orderStatus,
				replyMessage: releaseMessage,
				createBy: username,
				updateBy: username,
			})

			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				orderItemId,
				serviceId: service.id,
				referenceIds: serverIds.join(','),
				actionType: 'Server Release',
				paramsJson: JSON.stringify(param),
				actionDetails: releaseMessage,
				actionBy: username,
			})

			const historyLogs = serverIds.map((id) => ({
				userId,
				orderItemId,
				orderId: order.id,
				serviceId: service.id,
				serverId: id,
				actionType: 'Server Release',
			}))

			await runner.manager.insert(TecProvisionLogEntity, historyLogs)
			message.success('写入订单回复、日志、serverlog成功')
		} catch (e) {
			message.fail(e.message)
			message.error('写入订单回复、日志、serverlog失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 3. 回收DCIM 服务器
		const releaseDcimServers = await runner.manager.find(TecServerEntity, {
			select: ['id', 'dcimId'],
			where: { id: In(serverIds), dcimId: Not(IsNull()) },
		})

		if (releaseDcimServers.length) {
			await this.handleDcimServerRelease(service.cdWhServiceId, releaseDcimServers, message)
		}

		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, order.assignedStaffId)
			await this.sendMessageService.sendDiscord({
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Service release.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
				blockquote: `${username}: <@${assignedUser?.discordId}>`,
			})
		} catch (e) {
			message.warning('发送discord消息失败', e.message)
		}

		return message.ok('服务器已回收').success('服务器已回收').value()
	}

	@CoolTransaction()
	async handleDcimServerRelease(cdWhServiceId: number, dcimServers: TecServerEntity[], message: ResponseMessage) {
		// 修改dcimServer.nbtag为可分配

		await Promise.all(
			dcimServers.map(async (server) => {
				try {
					await dcimServer.changeDcimServerNbtagById(+server.dcimId, '可分配')
					message.success('已修改DCIM服务器nbtag为可分配')
				} catch (e) {
					message.warning('修改DCIM服务器nbtag为可分配失败', e.message)
				}

				try {
					await dcimServer.recycleDcimServerIpById(+server.dcimId)
					message.success('已释放DCIM服务器IP')
				} catch (e) {
					message.warning('释放DCIM服务器IP失败', e.message)
				}

				if (cdWhServiceId) {
					try {
						await whmcsCdSql
							.builder()
							.delete()
							.from('mod_custom_ipmi')
							.where('serviceid = :cdWhServiceId', { cdWhServiceId })
							.andWhere('ipmi_id = :dcimId', { dcimId: server.dcimId })
							.execute()
						message.success('已删除关联的WH服务器记录')
					} catch (e) {
						message.warning('删除关联的WH服务器记录失败', e.message)
					}
				}
			})
		)
	}

	@CoolTransaction()
	async updateWHAndDcim(param, runner?: QueryRunner) {
		const { serviceId, serverId } = param

		// 获取服务
		const service = await runner.manager.findOne<ServiceEntity>(ServiceEntity, serviceId)

		if (!service.cdWhServiceId) throw new Error('该订单服务未关联WH')

		// 获取当前服务器
		const server = await runner.manager.findOne<TecServerEntity>(TecServerEntity, serverId)

		if (!server.dcimId) throw new Error('该服务器未关联DCIM')

		// 开始关联
		await whmcsCdSql
			.builder()
			.update('mod_custom_ipmi')
			.set({ ipmi_id: server.dcimId })
			.where('serviceid = :cdWhServiceId', {
				cdWhServiceId: service.cdWhServiceId,
			})
			.execute()

		return 'DCIM关联WH服务器成功'
	}
}

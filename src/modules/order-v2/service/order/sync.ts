import type { OrderDetail } from '../provision'
import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { <PERSON>Null, Not, QueryRunner } from 'typeorm'
import { pick } from 'lodash'

import { whmcsCdApi, whmcsCdSql } from '/@/vendor/whmcs-cd'
import { ServiceType } from '/@/global/constants'
import { FRONTEND_ADDRESS_ALL } from '/@/global/env'
import { whmcsCustomFieldsFormat, whmcsPidFormat } from '/@/vendor/whmcs-cd/utils/format'
import { BaseSysUserEntity } from '/$/base/entity/sys/user'
import { OrderReplyEntity } from '/$/order/entity/reply'
import { OrderEntity } from '/$/order/entity/order'
import { AdminOrderProvisionService } from '/$/order-v2/service/provision'
import { OrderLogEntity } from '/$/order/entity/order_log'
import { ServiceEntity } from '/$/service/entity/service'
import { SendMessageService } from './send-message'
import ResponseMessage from '/@/utils/message'
import { TextEnum } from '../../enums/textEnum'
import { ServiceItem } from '/$/service/entity/service_item'

import { isCompute } from '/$/service/utils/types'
import { TecServerEntity } from '/@/modules/tec/entity/server'
import { TecIpaddEntity } from '/@/modules/tec/entity/ipadd'
interface SyncParams {
	orderId: number
	orderItemId: number
	paymentmethod?: string
	noinvoice?: boolean
	priceoverride?: number
	hostname?: string
	billingcycle?: string
	serviceDetails?: string
	regdate?: string
	status?: string
	domain?: string
	dedicatedip?: string
	assignedips?: string
	nextduedate?: string
	firstpaymentamount?: number
	recurringamount?: number
}

/**
 * 同步订单信息到WHMCS
 */
@Provide()
export class SyncService extends BaseService {
	@Inject()
	sendMessageService: SendMessageService

	@Inject()
	orderProvisionService: AdminOrderProvisionService

	@Inject()
	ctx

	/**
	 * 同步订单信息到WHMCS，如果已经同步了，不会再执行 action: AddOrder
	 */
	@CoolTransaction()
	async syncOrderToWhmcs(params: SyncParams, message: ResponseMessage, runner?: QueryRunner) {
		const { username, userId } = this.ctx.admin

		// 1. 获取订单详情
		const orderDetail = await this.orderProvisionService.detail(params.orderId, params.orderItemId, runner)
		const ipAddressList = await runner.manager.find(TecIpaddEntity, {
			where: { serviceId: orderDetail.service.id },
		})

		orderDetail.service.ipAddressList = ipAddressList

		const { service, customer, subservice, ...order } = orderDetail

		// 2. 验证同步参数
		try {
			this.syncOrderToWhmcsValidate(orderDetail)
		} catch (error) {
			return message.error(error.message).fail().value()
		}

		// 3. 构建WHMCS参数
		const whmcsParams = this.formatSyncDefaultParams(orderDetail, params)

		// 4. 同步到WHMCS
		// 如果服务未关联WHMCS，先创建服务
		if (!service.cdWhServiceId) {
			const addWhmcsParams = pick(whmcsParams, [
				'clientid',
				'paymentmethod',
				'noinvoice',
				'priceoverride',
				'hostname',
				'billingcycle',
				'pid',
				'customfields',
			])

			let cdWhServiceId = null
			let cdWhOrderId = null

			try {
				const { result, orderid, serviceids, message: resultMessage } = await whmcsCdApi.add('AddOrder', addWhmcsParams)
				if (result !== 'success' || !orderid || !serviceids) {
					throw new Error(`WHMCS同步返回异常: ${JSON.stringify({ result, orderid, serviceids })}${resultMessage}`)
				}

				cdWhOrderId = orderid
				cdWhServiceId = serviceids?.split(',')[0]
				message.success(`成功创建WHMCS服务，服务ID：${cdWhServiceId}，订单ID：${cdWhOrderId}`)
			} catch (error) {
				return message.error(error.message).fail().value()
			}

			if (cdWhServiceId && cdWhOrderId) {
				// 更新服务和订单关联ID
				await runner.manager.update(ServiceEntity, { id: service.id }, { cdWhServiceId: cdWhServiceId })
				await runner.manager.update(OrderEntity, { id: order.id }, { cdWhOrderId: cdWhOrderId })

				service.cdWhServiceId = cdWhServiceId
				whmcsParams.serviceid = cdWhServiceId
				message.success(`已更新OPS关联WHMCS服务ID：${cdWhServiceId}，OPS关联WHMCS订单ID：${cdWhOrderId}`)
			}
		}

		// 5. 更新WHMCS服务信息及服务详情信息
		const updateWhmcsParams = pick(whmcsParams, [
			'serviceid',
			'regdate',
			'status',
			'domain',
			'dedicatedip',
			'assignedips',
			'nextduedate',
			'firstpaymentamount',
			'recurringamount',
			'billingcycle',
			'customfields',
		])

		try {
			const { result, message: resultMessage } = await whmcsCdApi.update('UpdateClientProduct', updateWhmcsParams)
			if (result !== 'success') {
				throw new Error(`WHMCS同步返回异常: ${JSON.stringify({ result, message: resultMessage })}`)
			}

			if (result === 'success') {
				message.success('成功更新WHMCS服务信息')
			} else {
				message.warning('更新WHMCS服务信息失败' + resultMessage)
			}

			if (params.serviceDetails?.trim().length > 0) {
				await runner.manager.update(ServiceItem, { id: subservice.id }, { serviceDetails: params.serviceDetails })
			}
		} catch (error) {
			message.fail('更新WHMCS服务信息失败' + error.message)
			throw error
		}

		//================================================
		// 5.2 如果当前服务是 Complete 或者 Server，并且存在 dcimServer.dcimId，更新 WH 服务服务器ID
		//================================================
		const dcimServer = await runner.manager.findOne(TecServerEntity, {
			where: { serviceId: service.id, dcimId: Not(IsNull()) },
			order: { id: 'ASC' }, // 确保获取第一个匹配的记录
		})

		if (dcimServer?.dcimId) {
			try {
				// 先检查 mod_custom_ipmi 表中是否存在对应的记录
				const existingRecord = await whmcsCdSql
					.builder()
					.select('serviceid')
					.from('mod_custom_ipmi', 'mci')
					.where('serviceid = :cdWhServiceId', { cdWhServiceId: service.cdWhServiceId })
					.getRawOne()

				if (!existingRecord) {
					// 如果记录不存在，则插入新记录
					await whmcsCdSql
						.builder()
						.insert()
						.into('mod_custom_ipmi')
						.values({
							serviceid: service.cdWhServiceId,
							ipmi_id: dcimServer.dcimId,
							value: '',
							value2: '',
						})
						.execute()

					message.success(`已创建mod_custom_ipmi记录并关联DCIM(${dcimServer.dcimId})`)
				} else {
					// 如果记录存在，则更新
					await whmcsCdSql
						.builder()
						.update('mod_custom_ipmi')
						.set({ ipmi_id: dcimServer.dcimId })
						.where('serviceid = :cdWhServiceId', { cdWhServiceId: service.cdWhServiceId })
						.execute()

					message.success(`DCIM(${dcimServer.dcimId})关联WH服务器成功`)
				}
			} catch (error) {
				message.warning(`DCIM(${dcimServer.dcimId})关联WH服务器失败: ${error.message}`)
			}
		} else {
			message.warning('当前服务未关联DCIM服务器或DCIM服务器ID为空', '')
		}

		// 6. 记录操作日志、发送通知
		await this.recordOperationLogs(order, service, username, userId, params, runner, message)
		await this.sendNotifications(order, service, username, params, runner, message)
		return message.ok('同步成功').value()
	}

	private syncOrderToWhmcsValidate(orderDetail: OrderDetail) {
		const { customer, service } = orderDetail

		// 验证订单、客户、服务是否存在
		if (!customer.id) throw new Error('当前客户不存在')
		if (!service.id) throw new Error('当前服务不存在')
		if (!orderDetail.id) throw new Error('当前订单不存在')

		// 验证客户平台是否为CD、客户是否关联WHMCS账号
		if (customer.platform !== 'CD') throw new Error('当前客户平台不是CD')
		if (!customer.cdWhClientId) throw new Error('当前客户未关联WHMCS账号')

		// 验证服务是否正在同步中
		if (service.syncStatus === 'Syncing') throw new Error('服务正在同步中，请稍后重试')
	}

	private formatSyncDefaultParams(orderDetail: OrderDetail, params?) {
		const { customer, service, subservice } = orderDetail

		const defaultParams = {
			paymentmethod: 'banktransfer',
			noinvoice: true,
			priceoverride: 0,
		}

		const dynamicFieldsFromOrder = {
			clientid: customer.cdWhClientId,
			serviceid: service.cdWhServiceId,
			hostname: `${customer.customerNo}#${service.primaryIp}`,
			billingcycle: service.billCycle,

			status: service?.serviceStatus === 'Active' ? 'Active' : 'Pending',
			regdate: orderDetail.orderType === 'New' ? subservice?.activeDate : undefined,
			nextduedate: orderDetail?.orderType === 'New' ? subservice?.billStartDate : undefined,
			dedicatedip: service?.primaryIp && service?.primaryIp !== 'NA' ? service?.primaryIp : subservice?.description,
			assignedips: service?.ipAddressList?.map((item) => item.ipStr).join(','),

			domain:
				service?.serviceNo +
				'#' +
				`${service?.primaryIp && service?.primaryIp !== 'NA' ? service?.primaryIp : subservice?.description}`,

			firstpaymentamount: orderDetail?.orderType === 'New' ? service?.billAmount : undefined,
			recurringamount: service?.billAmount,

			pid: whmcsPidFormat(service?.serviceType as ServiceType),
			customfields: whmcsCustomFieldsFormat(service?.serviceType as ServiceType, {
				location: service?.location,
				serviceDetails: params?.serviceDetails || subservice.serviceDetails,
			}),
		}

		const whmcsParams = Object.assign(
			{},
			defaultParams,
			dynamicFieldsFromOrder,
			pick(params, [
				'paymentmethod',
				'noinvoice',
				'priceoverride',
				'nextduedate',
				'firstpaymentamount',
				'recurringamount',
				'billingcycle',
				'serviceDetails',
				'dedicatedip',
				'assignedips',
				'regdate',
				'hostname',
				'domain',
			])
		)

		return whmcsParams
	}

	/**
	 * 记录操作日志
	 */
	private async recordOperationLogs(
		order: any,
		service: any,
		username: string,
		userId: number,
		params: SyncParams,
		runner: QueryRunner,
		message: ResponseMessage
	) {
		try {
			const replyMessage = `订单已成功同步到WHMCS。${!service.cdWhServiceId ? `新服务ID=${service.cdWhServiceId}` : ''}`

			// 记录订单回复
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId: params.orderId,
				replyMessage,
				currentAction: 'Sync WHMCS',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: order.orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success(TextEnum.ORDER_REPLY_INSERT_SUCCESS)

			// 记录操作日志
			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Sync WHMCS',
				actionBy: username,
				paramsJson: JSON.stringify(params),
				actionDetails: JSON.stringify({
					orderStatus: order.orderStatus,
					assignedStaffId: order.assignedStaffId,
					assignedRoleId: order.assignedRoleId,
				}),
			})
			message.success(TextEnum.ORDER_LOG_INSERT_SUCCESS)
		} catch (error) {
			message.warning('记录操作日志失败', error.message)
		}
	}

	/**
	 * 发送通知
	 */
	private async sendNotifications(
		order: any,
		service: any,
		username: string,
		params: SyncParams,
		runner: QueryRunner,
		message: ResponseMessage
	) {
		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, { id: order.assignedStaffId })
			await this.sendMessageService.sendDiscord({
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Sync WHMCS`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
				blockquote: `${username}: <@${assignedUser?.discordId}> 订单已同步到WHMCS`,
			})
			message.success(TextEnum.DISCORD_MESSAGE_SUCCESS)
		} catch (error) {
			message.warning(TextEnum.DISCORD_MESSAGE_FAIL, error.message)
		}
	}
}

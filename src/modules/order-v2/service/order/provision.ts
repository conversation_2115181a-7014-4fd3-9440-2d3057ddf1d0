import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { QueryRunner, Repository } from 'typeorm'

// entity
import { OrderEntity } from '/$/order/entity/order'
import { OrderReplyEntity } from '/$/order/entity/reply'
import { OrderLogEntity } from '/$/order/entity/order_log'
import { ServiceEntity } from '/$/service/entity/service'

// service
import { ServiceService } from '/$/service/service/service'

import { InjectEntityModel } from '@midwayjs/orm'
import { BaseSysUserEntity } from '/$/base/entity/sys/user'
import { SendMessageService } from './send-message'

// env
import { FRONTEND_ADDRESS_ALL } from '/@/global/env'
import { whmcsCdApi, whmcsCdSql } from '/@/vendor/whmcs-cd'
import { isExistValue } from '/@/utils'
import { whmcsCustomFieldsFormat } from '/@/vendor/whmcs-cd/utils/format'
import { TecIpaddEntity } from '/$/tec/entity/ipadd'
import * as _ from 'lodash'
import { TecServerEntity } from '/$/tec/entity/server'
import ResponseMessage from '/@/utils/message'
import { dcimServer } from '/@/vendor/dcim'
import { TextEnum } from '/$/order/enums/textEnum'
import { OrderItemService } from '../order-item'
import { ServiceItem } from '/$/service/entity/service_item'
import { AdminOrderProvisionService } from '../provision'
import { OrderItem } from '/$/order/entity/order_item'
import { ServiceTypeEnum } from '/$/service/enums'
import { isExist } from '/@/utils/types'
import { OrderItemStatusEnum, OrderItemStatusTagEnum } from '/$/order-v2/enums'

/**
 * 描述
 */
@Provide()
export class ProvisionService extends BaseService {
	@InjectEntityModel(BaseSysUserEntity)
	userEntity: Repository<BaseSysUserEntity>

	@InjectEntityModel(TecIpaddEntity)
	ipEntity: Repository<TecIpaddEntity>

	@InjectEntityModel(TecServerEntity)
	tecServerRepository: Repository<TecServerEntity>

	@Inject()
	serviceService: ServiceService

	@Inject()
	sendMessageService: SendMessageService

	@Inject()
	orderProvisionService: AdminOrderProvisionService

	@Inject()
	orderItemService: OrderItemService

	@Inject()
	ctx

	@CoolTransaction()
	async serviceDelivery(param, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const userId = this.ctx.admin?.userId
		const username = this.ctx.admin?.username

		const {
			orderId,
			orderItemId,
			assignedStaffId,
			assignedRoleId,
			activeDate,
			orderStatus = OrderItemStatusEnum.BillPending,
			primaryIp,
			description,
			replyMessage,
			serviceDetails,
			serviceDeliveryOpts,
		} = param

		const { service, ...order } = await this.orderProvisionService.detail(orderId, orderItemId, runner)
		const orderItem = order.orderItem
		let subservice: ServiceItem = null

		if (!order || !orderItem) throw new Error('不存在这个订单或者这个订单项目')

		// 更新 OPS 订单信息
		try {
			await runner.manager.update(
				OrderEntity,
				{ id: order.id },
				{ assignedRoleId, assignedStaffId, updateBy: username }
			)

			await runner.manager
				.createQueryBuilder()
				.update(OrderItem)
				.set({ status: orderStatus })
				.where('id = :id OR parentItemId = :id', { id: orderItem.id })
				.execute()

			await this.orderItemService.addStatusTag(orderItem.id, [OrderItemStatusTagEnum.Delivered], runner)
			message.success('订单已更新')
		} catch (e) {
			runner.rollbackTransaction()
			return message.fail(e.message).error('订单更新失败', e.message).error('所有更新已取消').value()
		}

		// 更新服务
		try {
			const updateServiceInfo: Record<string, any> = {
				serviceDesc: description,
				serviceStatus: 'Active',
				primaryIp: primaryIp && primaryIp !== 'NA' ? primaryIp : orderItem.description,
				updateBy: username,
			}
			if (order.orderType === 'New') updateServiceInfo.firstActiveDate = activeDate
			await runner.manager.update(ServiceEntity, service.id, updateServiceInfo)
			message.success('服务已更新')
		} catch (e) {
			runner.rollbackTransaction()
			return message.fail(e.message).error('服务更新失败', e.message).error('所有更新已取消').value()
		}

		// 更新子服务
		try {
			// 将当前正在Active的子服务改为Terminated
			await runner.manager.update(ServiceItem, { serviceId: service.id, status: 'Active' }, { status: 'Terminated' })

			// 新增/更新子服务

			const { id: subserviceId } = await this.orderItemService.saveServiceItem(orderItem, order, service, runner)

			// 修改当前交付的子服务
			await runner.manager.update(
				ServiceItem,
				{ id: subserviceId },
				{
					status: 'Active',
					activeDate,
					description,
					serviceDetails,
					serviceDeliveryOpts,
				}
			)

			subservice = await runner.manager.findOne(ServiceItem, { where: { id: subserviceId } })

			message.success(TextEnum.SERVICE_OPTION_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_OPTION_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 插入订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				replyMessage: `${replyMessage}\n${serviceDetails}`,
				orderId,
				orderItemId,
				currentAction: 'Service Delivery',
				currentOrderStatus: orderItem.status,
				newOrderStatus: orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success(TextEnum.ORDER_REPLY_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_REPLY_INSERT_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新订单日志
		try {
			await runner.manager.insert(OrderLogEntity, {
				orderId,
				orderItemId,
				serviceId: service.id,
				actionType: 'Service Provision',
				actionBy: username,
				paramsJson: JSON.stringify(param),
				actionDetails: JSON.stringify({
					assignedStaffId,
					assignedRoleId,
					orderStatus,
				}),
			})
			message.success(TextEnum.ORDER_LOG_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_LOG_INSERT_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		/**
		 * 更新DCIM服务器相关信息
		 * 前置条件:
		 *  1. 服务类型为 Server
		 *  2. 关联服务器存在 dcimId
		 */
		// 更新DCIM服务器
		// 条件：服务类型为 Server, 且关联服务器存在 dcimId
		if (service.serviceType === ServiceTypeEnum.COMPUTE) {
			try {
				const server = await this.tecServerRepository.findOne({ where: { serviceId: service.id } })
				if (server?.dcimId) {
					const param = { server, service, primaryIp }
					await this.handleDcimServerAssign(param, message, runner)
				}
			} catch (error) {
				message.warning('更新DCIM服务器失败。', error.message)
			}
		}

		// 同步更新WhmcsCd服务
		if (isExist(service.cdWhServiceId)) {
			try {
				// 获取当前服务的所有已分配IP地址
				const ips = await runner.manager.find(TecIpaddEntity, { serviceId: service.id, status: 'Assigned' })

				// 订单交付 获取主ip的详情
				// const ipDetails = await this.ipEntity.findOne(primaryIp)
				const updateClientProductInfo: Record<string, any> = {
					serviceid: service.cdWhServiceId,
					regdate: order.orderType === 'New' ? activeDate : undefined, // 只有订单类型为New时才更新regdate
					status: 'Active',
					domain: service.serviceNo + '#' + `${primaryIp && primaryIp !== 'NA' ? primaryIp : subservice.description}`,
					dedicatedip: primaryIp && primaryIp !== 'NA' ? primaryIp : subservice.description,
					assignedips: ips.map((item) => item.ipStr).join(','),
					customfields: whmcsCustomFieldsFormat(service.serviceType, {
						location: subservice.location,
						serviceDetails: subservice.serviceDetails,
					}),
				}

				await whmcsCdApi.update('UpdateClientProduct', updateClientProductInfo)
				message.success(TextEnum.SERVICE_WH_UPDATE_SUCCESS)
			} catch (e) {
				runner.rollbackTransaction()
				return message
					.fail(e.message)
					.error(TextEnum.SERVICE_WH_UPDATE_FAIL, e.message)
					.error(TextEnum.TRANSACTION_ROLLBACK)
					.value()
			}
		}

		// 发送discord消息, 发送消息错误不会回滚
		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, assignedStaffId)
			await this.sendMessageService.sendDiscord({
				blockquote: `${username}: <@${assignedUser?.discordId}> ${replyMessage}`,
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Service delivery.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
			})
			message.success(TextEnum.DISCORD_MESSAGE_SUCCESS)
		} catch (e) {
			message.warning(TextEnum.DISCORD_MESSAGE_FAIL, e.message)
		}

		return message.ok('服务已交付').success('服务已交付').value()
	}

	@CoolTransaction()
	async serviceTerminate(param, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const { username, userId } = this.ctx.admin
		const {
			orderId,
			orderItemId,
			assignedStaffId,
			assignedRoleId,
			terminateDate,
			replyMessage,
			orderStatus = OrderItemStatusEnum.BillPending,
		} = param
		const { service, subservice, orderItem, ...order } = await this.orderProvisionService.detail(orderId, orderItemId)

		// 1. 更新订单信息
		try {
			await runner.manager.update(
				OrderEntity,
				{ id: order.id },
				{ assignedRoleId, assignedStaffId, updateBy: username }
			)
			// 2. 更新订单状态信息
			await runner.manager
				.createQueryBuilder()
				.update(OrderItem)
				.set({ status: orderStatus })
				.where('id = :id OR parentItemId = :id', { id: orderItem.id })
				.execute()
			await this.orderItemService.addStatusTag(orderItem.id, [OrderItemStatusTagEnum.Delivered], runner)
			message.success('订单及订单项目已更新')
		} catch (e) {
			runner.rollbackTransaction()
			return message.fail(e.message).error('订单及订单项目更新失败', e.message).error('所有更新已取消').value()
		}

		// 3. 更新服务
		try {
			await runner.manager.update(ServiceEntity, { id: service.id }, { terminateDate, serviceStatus: 'Terminated' })
			message.success('服务已更新')
		} catch (e) {
			runner.rollbackTransaction()
			return message.fail(e.message).error('服务更新失败', e.message).error('所有更新已取消').value()
		}

		// 4. 更新当前子服务
		try {
			await runner.manager.update(ServiceItem, { serviceId: service.id }, { terminateDate, status: 'Terminated' })
			message.success('子服务已更新')
		} catch (e) {
			runner.rollbackTransaction()
			return message.fail(e.message).error('子服务更新失败', e.message).error('所有更新已取消').value()
		}

		// 5. 如果服务存在`cdWhServiceId`则同步更新 WH 服务
		try {
			if (isExistValue(service.cdWhServiceId)) {
				await whmcsCdApi.update('UpdateClientProduct', {
					serviceid: service.cdWhServiceId,
					terminationdate: terminateDate,
					status: 'Terminated',
				})

				message.success('WHMCS-CD服务已更新')
			}
		} catch (e) {
			runner.rollbackTransaction()
			return message.fail(e.message).error('WHMCS-CD服务更新失败', e.message).error('所有更新已取消').value()
		}

		// 6. 插入订单回复、订单日志、发送 discord 消息
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId,
				orderItemId,
				replyMessage,
				currentAction: 'Service Terminate',
				currentOrderStatus: orderItem.status,
				newOrderStatus: orderStatus,
				createBy: username,
				updateBy: username,
			})

			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Service Provision',
				actionBy: username,
				paramsJson: JSON.stringify(param),
				actionDetails: JSON.stringify({
					orderStatus,
					assignedStaffId,
					assignedRoleId,
				}),
			})

			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, assignedStaffId)
			await this.sendMessageService.sendDiscord({
				blockquote: `${username}: <@${assignedUser?.discordId}> ${replyMessage}`,
				link: {
					title: `#${service.serviceNo}/${order.orderNo} - Service terminate.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
				},
			})
			message.success('已增加订单回复、订单日志并发送 discord 消息')
		} catch (e) {
			message.warning('增加订单回复、订单日志、发送 discord 消息失败', e.message)
		}

		return message.ok('服务已停止').success('服务已停止').value()
	}

	@CoolTransaction()
	async handleDcimServerAssign(param, message: ResponseMessage, runner?: QueryRunner) {
		const { server, service, primaryIp } = param

		// 更新dcim服务器编号
		try {
			await dcimServer.changeDcimServerNbtagById(server.dcimId, service.serviceNo)
			message.success('已更新DCIM服务器nbtag', service.serviceNo)
		} catch (e) {
			message.warning('更新DCIM服务器nbtag失败。', e.message)
		}

		// 更新dcim服务器主IP
		try {
			const mainIp = await runner.manager.findOne(TecIpaddEntity, {
				where: { serviceId: service.id, serverId: server.id, ipStr: primaryIp },
			})

			if (mainIp) {
				await dcimServer.bindMainIpToDcimServer(server.dcimId, mainIp.ipStr)
				message.success('已更新DCIM服务器主IP', mainIp.ipStr)
			}
		} catch (e) {
			message.warning('更新DCIM服务器主IP失败。', e.message)
		}

		// 如果服务存在 cdWhServiceId，则更新whmcs.mod_custom_ipmi数据库
		// 更新内容：ipmi_id
		try {
			if (service.cdWhServiceId) {
				await whmcsCdSql
					.builder()
					.update('mod_custom_ipmi')
					.set({ ipmi_id: server.dcimId })
					.where('serviceid = :cdWhServiceId', {
						cdWhServiceId: service.cdWhServiceId,
					})
					.execute()
				message.success('已更新WHMCS-CD IPMI信息。')
			}
		} catch (e) {
			message.warning('更新WHMCS-CD IPMI信息失败。', e.message)
		}
	}
}

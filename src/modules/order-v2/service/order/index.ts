import { Provide, Inject } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { Brackets, QueryRunner, Repository } from 'typeorm'
import { isArray } from 'lodash'

import { ConfCustomNoService, Count } from '/$/conf/service/common'
import { OrderItemService } from '../order-item'
import { OrderEntity } from '/$/order/entity/order'
import { ReplyService } from './reply'
import { SyncService } from './sync'
import { SendMessageService } from './send-message'
import { OrderReplyEntity } from '/$/order/entity/reply'
import { FRONTEND_ADDRESS_ALL } from '/@/global/env'
import { BaseSysUserEntity } from '/@/modules/base/entity/sys/user'
import { OrderItem } from '/$/order/entity/order_item'
import { AdminOrderDetailService } from '../detail'
import ResponseMessage from '/@/utils/message'
export { PricingService } from './pricing'
export { ProvisionService } from './provision'
export { ConvertNewService } from './convert-new'
export { BillingService } from './billing'
export { CompletionReviewService } from './completion-review'
export { TerminateCloseService } from './terminate-close'
export { VoidService } from './void'
export { IPAddressService } from './ip_address'
export { ServerService } from './server'
export { ReopenService } from './reopen'
export { SyncService } from './sync'
export { SalesReviewService } from './sales-review'

/**
 * 描述
 */
@Provide()
export class OrderService extends BaseService {
	@InjectEntityModel(OrderEntity)
	orderRepository: Repository<OrderEntity>

	// 获取详情
	@Inject()
	replyService: ReplyService

	// 发送Mail
	@Inject()
	sendMessageService: SendMessageService

	@Inject()
	confCustomNoService: ConfCustomNoService

	@Inject()
	orderItemService: OrderItemService

	// 关联操作
	@Inject()
	syncService: SyncService

	@Inject()
	orderDetailService: AdminOrderDetailService

	@Inject()
	ctx

	// 订单新增
	@CoolTransaction()
	async add(param, runner?: QueryRunner) {
		const { userId, username } = this.ctx.admin
		const { orderItems, ...order } = param

		order.version = 2
		order.orderStatus = 'On-going'
		order.createBy = username
		order.updateBy = username

		const insertedOrder = await this.confCustomNoService.handler(order, Count.ORDER, OrderEntity, runner)

		await this.orderItemService.upsert(insertedOrder, orderItems, runner)
		const orderDetails = await this.updateDetails(insertedOrder.id, runner)

		// 5. 插入回复表
		const replyMessage = `Order Created.\n${orderDetails}`
		await runner.manager.insert(OrderReplyEntity, {
			userId,
			orderId: insertedOrder.id,
			replyMessage,
			currentAction: 'Created',
			currentOrderStatus: insertedOrder.orderStatus,
			newOrderStatus: insertedOrder.orderStatus,
			createBy: username,
			updateBy: username,
		})

		// 6. 发送 Discord msg.
		try {
			const assignedUser = await runner.manager.findOne(BaseSysUserEntity, order.assignedStaffId)
			await this.sendMessageService.sendDiscord({
				link: {
					title: `#${insertedOrder.orderNo} - New order.`,
					source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${insertedOrder.id}`,
				},
				blockquote: `${username}: <@${assignedUser?.discordId}> ${order.description}`,
			})
		} catch (error) {
			console.error(error.message)
		}

		return insertedOrder
	}

	@CoolTransaction()
	async update(param: any, runner?: QueryRunner): Promise<void> {
		if (!isArray(param.orderItems) || param.orderItems.length === 0) throw new Error('当前订单不存在订单项目') // 当前订单不存在订单项目
		if ((param.orderItems as any[]).find((item) => !item.productId && !item.productPlanId)) {
			throw new Error('当前订单存在空产品的项目')
		}

		const { username } = this.ctx.admin
		const { orderItems, ...order } = param

		order.updateBy = username
		await runner.manager.update(OrderEntity, order.id, order)
		await this.orderItemService.upsert(order, orderItems, runner)
		await this.updateDetails(order.id, runner)
	}

	@CoolTransaction()
	async updateDetails(orderId: number, runner?: QueryRunner) {
		const insertedOrderItems = await runner.manager.find(OrderItem, {
			where: { orderId, parentItemId: null },
			select: ['id', 'detail'],
		})

		const orderDetails = insertedOrderItems
			.map((item) => item.detail?.trim())
			.filter(Boolean)
			.join('\n\n')

		await runner.manager.update(OrderEntity, orderId, {
			orderDetails,
		})

		return orderDetails
	}

	async pageTotals(params) {
		const { roleIds, date } = params
		let queryBuilder = this.orderRepository.createQueryBuilder('a')

		queryBuilder
			.select([
				'COUNT(CASE WHEN a.orderStatus = "Completed" THEN 1 END) completed',
				'COUNT(CASE WHEN a.orderStatus = "Voided" THEN 1 END) voided',
				'COUNT(CASE WHEN a.orderStatus = "Review Pending" THEN 1 END) reviewPending',
				'COUNT(CASE WHEN a.orderStatus = "Bill Pending" THEN 1 END) billPending',
				'COUNT(CASE WHEN a.orderStatus = "Provision Pending" THEN 1 END) provisionPending',
				'COUNT(CASE WHEN a.orderStatus = "Price Approval" THEN 1 END) priceApproval',
				`COUNT(CASE WHEN a.orderStatus <> "Completed" AND a.orderStatus <> "Voided" AND find_in_set(a.assignedRoleId, '${roleIds.join(
					','
				)}') THEN 1 END) asCurrentRoleTotal`,
			])
			.where(
				new Brackets((qb) => {
					roleIds.map((rid, index) => {
						if (index === 0) {
							qb.where(`find_in_set(${rid}, a.visibleRoleId)`)
						} else {
							qb.orWhere(`find_in_set(${rid}, a.visibleRoleId)`)
						}
					})
					qb.orWhere(`find_in_set(a.assignedRoleId, '${roleIds.join(',')}')`)
				})
			)

		if (date && date.length === 2) {
			const [startDate, endDate] = date
			queryBuilder = queryBuilder.andWhere((subQuery) => {
				subQuery.andWhere(
					`CAST(a.createTime AS DATE) >= CAST(:startDate AS DATE)
        AND CAST(a.createTime AS DATE) <= CAST(:endDate AS DATE)`,
					{
						startDate,
						endDate,
					}
				)
				return '1 = 1' // Ensures that the subquery doesn't affect the main query's logic
			})
		}

		const result = await queryBuilder.getRawOne()

		return result
	}

	// 附件
	async updateAttachments(params) {
		const { id, attachments, updateBy } = params
		return await this.orderRepository.update(id, { updateBy, attachments })
	}

	// 订单详情
	async info(id: any): Promise<any> {
		if (!id) return
		const order = await this.orderRepository.findOne(id)
		order.orderItems = await this.orderDetailService.getOrderItems(id)
		return order
	}

	// 回复
	async replyMessageHandler(body) {
		return await this.replyService.execute(body)
	}

	// 关联订单
	@CoolTransaction()
	async syncHandler(body, runner?: QueryRunner) {
		return await this.syncService.syncOrderToWhmcs(body, new ResponseMessage(), runner)
	}

	// Send massage
	async sendMessageHandler(body) {
		return await this.sendMessageService.sendZohoEmailMessage(body)
	}
}

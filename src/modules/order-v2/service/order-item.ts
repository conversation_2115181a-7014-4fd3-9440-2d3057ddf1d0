import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { QueryRunner, Repository, In } from 'typeorm'
import { chain, difference, isArray } from 'lodash'
import * as Big from 'big.js'
import { isExist } from '/@/utils/types'
import { OrderItem } from '/$/order/entity/order_item'
import { OrderEntity } from '/$/order/entity/order'
import { ServiceItem } from '/$/service/entity/service_item'
import { ServiceEntity } from '/$/service/entity/service'
import { ServiceService } from '/$/service/service/service'
import { formatOrderItemDetail } from '/$/order-v2/utils'
import { ProductEntity } from '/$/product/entity/product'
import { ProductPlan } from '/$/product/entity/product_plan'
import { OrderItemStatusEnum } from '/$/order-v2/enums'

type CustomOrderItem = OrderItem & { children?: OrderItem[] }

@Provide()
export class OrderItemService extends BaseService {
	@InjectEntityModel(OrderItem)
	orderItemRepository: Repository<OrderItem>

	@Inject()
	serviceService: ServiceService

	@Inject()
	ctx

	/**
	 * 描述
	 */
	@CoolTransaction()
	async upsert(order: OrderEntity, newOrderItems: CustomOrderItem[], runner?: QueryRunner) {
		newOrderItems = isArray(newOrderItems) ? newOrderItems : [newOrderItems]
		const orderId = order.id

		// 获取现有订单项目
		const oldOrderItems = await runner.manager.find(OrderItem, { where: { orderId } })
		const oldOrderItemIds = oldOrderItems.map((i) => i.id)
		const newOrderItemIds = chain(newOrderItems)
			.map((item) => [item.id, ...(item.children?.map(({ id }) => id) || [])])
			.flatten()
			.filter((id) => !!id)
			.value()

		// 删除不再需要的订单项目
		const deleteOrderItemIds = difference(oldOrderItemIds, newOrderItemIds)
		if (deleteOrderItemIds.length > 0) {
			await runner.manager.delete(OrderItem, deleteOrderItemIds)
		}

		// 批量获取产品和产品计划
		const productIds = chain(newOrderItems)
			.map((item) => [item.productId, ...(item.children?.map((child) => child.productId) || [])])
			.flatten()
			.filter((id) => !!id)
			.value()

		const productPlanIds = chain(newOrderItems)
			.map((item) => [item.productPlanId, ...(item.children?.map((child) => child.productPlanId) || [])])
			.flatten()
			.filter((id) => !!id)
			.value()

		const [products, productPlans] = await Promise.all([
			productIds.length ? runner.manager.findByIds(ProductEntity, productIds) : [],
			productPlanIds.length
				? runner.manager.find(ProductPlan, {
						where: { id: In(productPlanIds) },
						relations: ['productPlanPriceMaps', 'productPlanPriceMaps.product'],
				  })
				: [],
		])

		// 创建产品和产品计划的查找映射
		const productMap = new Map<string, ProductEntity>(
			products.map((p) => [p.id.toString(), p] as [string, ProductEntity])
		)
		const productPlanMap = new Map<string, ProductPlan>(
			productPlans.map((p) => [p.id.toString(), p] as [string, ProductPlan])
		)

		// 处理订单项目
		await runner.manager.transaction(async (subRunner) => {
			const processOrderItem = async (orderItem: CustomOrderItem, parentId?: number, parent?: any) => {
				// 处理子项目
				if (isArray(orderItem.children) && isExist(orderItem.children)) {
					// 先处理子项目，获取子项目信息
					const childrenItems = await Promise.all(
						orderItem.children.map(async (childItem) => {
							// 为子项目设置产品和产品计划
							if (childItem.productId) {
								childItem.product = productMap.get(childItem.productId.toString()) || null
							} else if (childItem.productPlanId) {
								childItem.productPlan = productPlanMap.get(childItem.productPlanId.toString()) || null
							}
							childItem.location = childItem.confOpts?.location || childItem.location
							childItem.detail = formatOrderItemDetail(childItem)
							return childItem
						})
					)

					// 更新父项目的描述，包含子项目信息
					orderItem.children = childrenItems
				}

				// 处理当前项目
				orderItem.orderId = orderId
				orderItem.status = parent?.status || orderItem.status || OrderItemStatusEnum.Pending
				orderItem.statusTags = parent?.statusTags || orderItem.statusTags || []
				orderItem.orderPrice = orderItem.orderPrice || orderItem.tierPrice
				orderItem.location = orderItem.confOpts?.location || orderItem.location

				if (orderItem.productId) {
					orderItem.product = productMap.get(orderItem.productId.toString()) || null
				} else if (orderItem.productPlanId) {
					orderItem.productPlan = productPlanMap.get(orderItem.productPlanId.toString()) || null
				}

				orderItem.detail = formatOrderItemDetail(orderItem)
				if (parentId) orderItem.parentItemId = parentId

				const { identifiers } = await subRunner.upsert(OrderItem, orderItem, ['id'])
				return identifiers[0].id
			}

			// 处理所有订单项目
			await Promise.all(
				newOrderItems.map(async (orderItem) => {
					// 判断是否需要将状态更新为 Provision Pending
					if (isExist(orderItem.id) && isExist(orderItem.serviceId) && isExist(orderItem.serviceItemId)) {
						const oldOrderItem = oldOrderItems.find((i) => i.id === orderItem.id)
						if (oldOrderItem) {
							const hasMainItemChanges = this.checkMainItemChanges(orderItem, oldOrderItem)
							const hasChildrenChanges = this.checkChildrenChanges(
								orderItem.children || [],
								oldOrderItems.filter((i) => i.parentItemId === oldOrderItem.id)
							)

							if (hasMainItemChanges || hasChildrenChanges) {
								orderItem.status = OrderItemStatusEnum.ProvisionPending
								orderItem.statusTags = []
							}
						}
					}

					const parentId = await processOrderItem(orderItem)

					if (isArray(orderItem.children) && isExist(orderItem.children)) {
						await Promise.all(orderItem.children.map((childItem) => processOrderItem(childItem, parentId, orderItem)))
						orderItem.children = await runner.manager.find(OrderItem, {
							where: { parentItemId: parentId },
						})
					}

					if (orderItem.serviceId) {
						const service = await this.saveService(orderItem, order, runner)
						await this.saveServiceItem(orderItem, order, service, runner)
					}
				})
			)
		})
	}

	@CoolTransaction()
	async saveService(orderItem: CustomOrderItem, order: OrderEntity, runner?: QueryRunner) {
		const username = this.ctx.admin?.username

		const { customerId, salesManagerId } = order

		// 新增/更新服务
		const service = await runner.manager.save(ServiceEntity, {
			...(!orderItem.serviceId ? await this.serviceService.getServiceNo(order.customerId) : {}),
			id: orderItem.serviceId,
			customerId,
			salesManagerId,
			billCurrency: orderItem.currency,
			billType: orderItem.billType,
			billAmount: orderItem.totalOrderItemPrice,
			billCycle: orderItem.billCycle,
			location: orderItem.location,
			serviceType: orderItem.mainCategory,
			serviceDesc: orderItem.description,
			serviceStatus: 'Provision Pending',
			createBy: username,
			updateBy: username,
		})

		await runner.manager.update(OrderItem, orderItem.id, { serviceId: service.id })
		await runner.manager.update(OrderEntity, order.id, { serviceId: service.id })
		return service
	}

	@CoolTransaction()
	async saveServiceItem(
		orderItem: OrderItem & { serviceDeliveryOpts?: any },
		order: OrderEntity,
		service: ServiceEntity,
		runner?: QueryRunner
	) {
		const username = this.ctx.admin?.username
		const orderId = order.id

		// 新增/更新子服务
		const serviceItem = await runner.manager.save(ServiceItem, {
			id: orderItem.serviceItemId,
			orderId,
			serviceId: service.id,
			orderItemId: orderItem.id,
			productId: orderItem.productId || null,
			productType: orderItem.productType,
			productPlanId: orderItem.productPlanId || null,
			billCurrency: orderItem.currency,
			billType: orderItem.billType,
			billAmount: service.billAmount,
			billCycle: service.billCycle,
			location: orderItem.location,
			tier: orderItem.tier,
			price: orderItem.orderPrice,
			qty: orderItem.qty,
			currency: orderItem.currency,
			description: orderItem.description,
			orderDetails: orderItem.detail,
			type: orderItem.subCategory,
			serviceDeliveryOpts: orderItem.serviceDeliveryOpts,
			status: 'Active',
			createBy: username,
			updateBy: username,
		} as ServiceItem)

		await runner.manager.update(OrderItem, orderItem.id, { serviceId: service.id, serviceItemId: serviceItem.id })

		// 新增/更新子服务项目
		let childrenServiceItems: ServiceItem[] = null

		if (orderItem.children?.length > 0) {
			childrenServiceItems = await runner.manager.save(
				ServiceItem,
				orderItem.children.map(
					(orderItem) =>
						({
							id: orderItem.serviceItemId,
							orderId,
							serviceId: service.id,
							orderItemId: orderItem.id,
							parentItemId: serviceItem.id,
							productId: orderItem.productId || null,
							productType: orderItem.productType,
							productPlanId: orderItem.productPlanId || null,
							billCurrency: orderItem.currency,
							billType: orderItem.billType,
							billAmount: orderItem.totalOrderItemPrice,
							billStatus: '',
							location: orderItem.location,
							tier: orderItem.tier,
							price: orderItem.orderPrice,
							qty: orderItem.qty,
							currency: orderItem.currency,
							description: orderItem.description,
							orderDetails: orderItem.detail,
							type: orderItem.subCategory,
							createBy: username,
							updateBy: username,
						} as ServiceItem)
				)
			)

			await runner.manager.save(
				OrderItem,
				childrenServiceItems.map((item) => ({
					id: item.orderItemId,
					serviceId: service.id,
					serviceItemId: item.id,
				}))
			)

			serviceItem.children = childrenServiceItems
		}

		return serviceItem
	}

	// 根据服务ID获取订单项目
	async getOrderItemByServiceId(query) {
		const orderItem = await this.orderItemRepository.findOne({
			where: { serviceId: query.serviceId },
			relations: ['product', 'productPlan', 'service'],
		})

		const orderChildrenItems = await this.orderItemRepository.find({
			where: { parentItemId: orderItem.id },
			relations: ['product', 'productPlan', 'service'],
		})

		return { ...orderItem, children: orderChildrenItems }
	}

	calcItemPrice(price: number | string, qty: string | number = 1) {
		const priceBig = new Big(price)
		return priceBig.mul(qty).toFixed(2)
	}

	calcItemTotalPrice(orderItem: OrderItem) {
		const orderItemPrice = this.calcItemPrice(orderItem.orderPrice, orderItem.qty)
		const childrenOrderItems = orderItem.children || []

		return childrenOrderItems.reduce((total, item) => {
			return new Big(total).add(this.calcItemPrice(item.orderPrice, item.qty)).toFixed(2)
		}, orderItemPrice)
	}

	/**
	 * 检查主订单项目是否有变更
	 */
	private checkMainItemChanges(newItem: OrderItem, oldItem: OrderItem): boolean {
		const changes = {
			productPriceId: newItem.productPriceId !== oldItem.productPriceId,
			productPlanPriceId: newItem.productPlanPriceId !== oldItem.productPlanPriceId,
			qty: newItem.qty !== oldItem.qty,
			orderPrice: +newItem.orderPrice !== +oldItem.orderPrice,
			totalOrderItemPrice: newItem.totalOrderItemPrice !== oldItem.totalOrderItemPrice,
			billType: newItem.billType !== oldItem.billType,
			billCycle: newItem.billCycle !== oldItem.billCycle,
		}

		const hasChanges = Object.entries(changes).some(([key, changed]) => {
			if (changed) {
				console.log(`Order item ${newItem.id} ${key} changed from ${oldItem[key]} to ${newItem[key]}`)
			}
			return changed
		})

		return hasChanges
	}

	/**
	 * 检查子订单项目是否有变更
	 */
	private checkChildrenChanges(newChildren: OrderItem[], oldChildren: OrderItem[]): boolean {
		if (newChildren.length !== oldChildren.length) {
			console.log(`Children items count changed from ${oldChildren.length} to ${newChildren.length}`)
			return true
		}

		// 按ID排序以确保比较相同位置的项目
		const sortedNewChildren = [...newChildren].sort((a, b) => a.id - b.id)
		const sortedOldChildren = [...oldChildren].sort((a, b) => a.id - b.id)

		return sortedNewChildren.some((newChild, index) => {
			const oldChild = sortedOldChildren[index]
			const changes = this.checkMainItemChanges(newChild, oldChild)

			if (changes) {
				console.log(`Child item ${newChild.id} has changes`)
			}
			return changes
		})
	}

	/**
	 * 添加状态标签
	 * @param orderItemId 订单项目ID
	 * @param tags 要添加的标签或标签数组
	 */
	@CoolTransaction()
	async addStatusTag(orderItemId: number, tags: string | string[], runner?: QueryRunner) {
		const orderItem = await runner.manager.findOne(OrderItem, orderItemId)
		if (!orderItem) {
			throw new Error('订单项目不存在')
		}

		let statusTags: string[] = orderItem.statusTags || []
		if (typeof statusTags === 'string') {
			statusTags = JSON.parse(statusTags)
		}

		const tagsToAdd = Array.isArray(tags) ? tags : [tags]
		let hasChanges = false

		for (const tag of tagsToAdd) {
			if (!statusTags.includes(tag)) {
				statusTags.push(tag)
				hasChanges = true
			}
		}

		if (hasChanges) {
			await runner.manager
				.createQueryBuilder()
				.update(OrderItem)
				.set({ statusTags })
				.where('id = :id OR parentItemId = :id', { id: orderItemId })
				.execute()
		}

		return statusTags
	}

	/**
	 * 移除状态标签
	 * @param orderItemId 订单项目ID
	 * @param tags 要移除的标签或标签数组
	 */
	@CoolTransaction()
	async removeStatusTag(orderItemId: number, tags: string | string[], runner?: QueryRunner) {
		const orderItem = await runner.manager.findOne(OrderItem, orderItemId)
		if (!orderItem) {
			throw new Error('订单项目不存在')
		}

		let statusTags: string[] = orderItem.statusTags || []
		if (typeof statusTags === 'string') {
			statusTags = JSON.parse(statusTags)
		}

		const tagsToRemove = Array.isArray(tags) ? tags : [tags]
		let hasChanges = false

		for (const tag of tagsToRemove) {
			const index = statusTags.indexOf(tag)
			if (index > -1) {
				statusTags.splice(index, 1)
				hasChanges = true
			}
		}

		if (hasChanges) {
			await runner.manager
				.createQueryBuilder()
				.update(OrderItem)
				.set({ statusTags })
				.where('id = :id OR parentItemId = :id', { id: orderItemId })
				.execute()
		}

		return statusTags
	}
}

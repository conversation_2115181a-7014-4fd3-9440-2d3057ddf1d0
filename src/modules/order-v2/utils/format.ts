import { chain } from 'lodash'
import { isExist, isArray } from '/@/utils/types'

export function formatServerDetails(servers: any[], separator = '\n---\n') {
	return chain(servers)
		.map(({ id, serverNo, chassisId, nodeId, status, cpuModel, memorySize, hdds }) => {
			return [
				`ID: ${id}`,
				`Server No: ${serverNo}`,
				`Chassis ID: ${chassisId}`,
				`Node ID: ${nodeId}`,
				`Status: ${status}`,
				`CPU: ${cpuModel}`,
				`Memory: ${memorySize}G`,
				`HDDs: ${hdds}`,
			].join('\n')
		})
		.join(separator)
		.value()
}

export function formatIPDetails(ips: any[], separator = '\n') {
	return chain(ips)
		.map((ip) => ip.ipStr)
		.join(separator)
		.value()
}

export function formatProductDetail(detail: string) {
	return (detail as string)
		?.split('\n') // 按换行符分割
		.map((line) => line.trim()) // 清理每行首尾空格
		.filter((line) => line.length > 0) // 过滤空行
		.map((line) => line.replace(/,\s*$/, '')) // 清除行尾逗号
		.join(', ') // 用逗号连接
		.replace(/,{2,}/g, ',') // 合并连续逗号
}

function formatLocDetail(orderItem: any) {
	const description = orderItem.description
	const location = orderItem.confOpts?.location

	if (location) return `${location}/${description}`
	return description
}

function formatPropertyNames(val: string) {
	return (
		val.replace(/(^|[ \n])([a-zA-Z][a-zA-Z0-9]*):/g, (_, sep, prop) => {
			const formatted = prop
				.replace(/([A-Z])/g, ' $1')
				.replace(/^./, (s) => s.toUpperCase())
				.trim()
			return `${sep}   - ${formatted}:`
		}) + '\n'
	)
}

export function formatOrderItemDetail(orderItem: any) {
	const isProductPlan = !!orderItem.productPlanId

	let detail = ''

	if (isProductPlan) {
		detail += `- ${orderItem.qty} x ${formatLocDetail(orderItem)} - [${orderItem.productPlan?.planNo}]`
		detail += isArray(orderItem.productPlan?.productPlanPriceMaps)
			? `\n${orderItem.productPlan?.productPlanPriceMaps
					?.map((map) => {
						if (!map.product) return ''
						return `- ${map.productQty} x ${map.product.name}: ${formatProductDetail(map.product.detail)} - [${
							map.product.productNo
						}]`
					})
					.join('\n')}`
			: ''
	} else {
		detail += `- ${orderItem.qty} x ${formatLocDetail(orderItem)}: ${
			formatProductDetail(orderItem.productDetail) || ''
		} - [${orderItem.product?.productNo}]`
	}

	if (orderItem.confOptsDetail?.trim()) {
		detail += `\n### Config Options\n${formatPropertyNames(orderItem.confOptsDetail)}`
	}

	if (isArray(orderItem.children) && isExist(orderItem.children)) {
		detail += `\n**Addon**\n${orderItem.children
			.map((childrenItem) => {
				let childDetail = `- ${childrenItem.qty} x ${formatLocDetail(childrenItem)}: ${
					formatProductDetail(childrenItem.productDetail) || ''
				} - [${childrenItem.product?.productNo}]`

				if (childrenItem.confOptsDetail?.trim()) {
					childDetail += `\n### Config Options\n${formatPropertyNames(childrenItem.confOptsDetail)}`
				}

				return childDetail
			})
			.join('\n')}`
	}

	return detail
}

import { Provide } from '@midwayjs/decorator'
import { BaseService } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { In, IsNull, Not, Repository } from 'typeorm'
import { isEmpty } from 'lodash'

import { AssetEntity } from '/$/asset/entity/asset'
import { TecServerEntity } from '/$/tec/entity/server'

/**
 * 描述
 */
@Provide()
export class SyncServerService extends BaseService {
	@InjectEntityModel(AssetEntity)
	assetEntity: Repository<AssetEntity>

	@InjectEntityModel(TecServerEntity)
	tecServerEntity: Repository<TecServerEntity>

	/**
	 * 获取 asset 信息
	 */
	async getAsset(ids: Array<string | number>) {
		return this.assetEntity.find({
			where: {
				id: In(ids),
				serverId: Not(IsNull()),
			},
		})
	}

	/**
	 * 获取对应的字段名
	 */
	getServerField(category: string) {
		switch (category) {
			case 'CPU':
				return 'cpuAssetId'
			case 'Memory':
				return 'memoryAssetId'
			case 'Storage':
				return 'storagesAssetId'
		}
	}

	/**
	 * 获取 CPU 配置
	 */
	getCpu(assets: Array<AssetEntity>) {
		return assets.reduce(
			(acc, asset) => {
				const { model, specs } = asset
				acc.cpuCores += +specs?.match(/\d+/g)?.[0] || 0
				acc.cpuNo += 1
				acc.cpuModel = model
				return acc
			},
			{
				cpuCores: 0,
				cpuNo: 0,
				cpuModel: '',
			}
		)
	}

	/**
	 * 获取 Memory 配置
	 */
	getMemory(assets: Array<AssetEntity>) {
		const memory = assets.reduce(
			(acc, asset) => {
				const { model } = asset
				acc.memoryInstalledNo += 1
				acc.memorySize += +model.match(/-(\d+)G/)?.[1] || 0
				acc.memoryModel.push(model)
				return acc
			},
			{
				memoryInstalledNo: 0,
				memorySize: 0,
				memoryModel: [],
			}
		)
		return {
			...memory,
			memoryModel: memory.memoryModel.join(','),
		}
	}

	/**
	 * 获取 Storage 配置
	 */
	getStorage(assets: Array<AssetEntity>) {
		return { hdds: assets.map(({ model }) => model).join(',') }
	}

	/**
	 * 获取服务器更新内容
	 */
	getServerEditData(assets: Array<AssetEntity>, field: string) {
		switch (field) {
			case 'cpuAssetId':
				return this.getCpu(assets)
			case 'memoryAssetId':
				return this.getMemory(assets)
			case 'storagesAssetId':
				return this.getStorage(assets)
			default:
				return {}
		}
	}

	/**
	 * 同步服务器配件信息
	 */
	async syncServerByAsset(assetIds: Array<string | number>) {
		const assets = await this.getAsset(assetIds)
		if (isEmpty(assets)) return
		const field = this.getServerField(assets[0].assetCategory)
		const serverIds = assets.map((asset) => asset.serverId)
		const serverList = await this.tecServerEntity.findByIds(serverIds)

		const promises = serverList.map(async (server) => {
			const curAssetIds = server[field].split(',')
			if (isEmpty(curAssetIds)) return
			const curAssets = await this.getAsset(curAssetIds)
			const editData = this.getServerEditData(curAssets, field)
			return this.tecServerEntity.update(server.id, editData)
		})

		return Promise.all(promises)
	}
}

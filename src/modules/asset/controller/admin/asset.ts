import { Body, Inject, Post, Provide } from '@midwayjs/decorator'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core'
import { SelectQueryBuilder } from 'typeorm'

import { AssetEntity } from '/$/asset/entity/asset'
import { VendorEntity } from '/$/vendor/entity/vendor'
import { TecRackUnitEntity } from '/$/tec/entity/rack_unit'
import { TecRackEntity } from '/$/tec/entity/rack'
import { TecServerEntity } from '/$/tec/entity/server'
import { OrderPurchaseEntity } from '/$/order/entity/purchase'

import { AssetService } from '/$/asset/service/asset'
import { ServerService } from '/$/asset/service/server'
import { AdminDcimService } from '/$/asset/service/dcim'

import { isEmpty } from '/@/utils/types'
import { Utils } from '/@/comm/utils'
/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['delete', 'update', 'info', 'list', 'page'],
	entity: AssetEntity,
	service: AssetService,
	pageQueryOp: {
		fieldEq: [
			{ column: 'a.assetType', requestParam: 'assetType' },
			{ column: 'a.assetCategory', requestParam: 'assetCategory' },
			{ column: 'a.isComponent', requestParam: 'isComponent' },
			{ column: 'a.model', requestParam: 'model' },
			{ column: 'a.location', requestParam: 'location' },
			{ column: 'a.brand', requestParam: 'brand' },
			{ column: 'a.status', requestParam: 'status' },
			{ column: 'a.owner', requestParam: 'owner' },
			{ column: 'a.poId', requestParam: 'poId' },
			{ column: 'a.position', requestParam: 'position' },
			{ column: 'a.rack', requestParam: 'rack' },
			{ column: 'a.parentAssetId', requestParam: 'parentAssetId' },
			{ column: 'a.role', requestParam: 'role' },
		],
		keyWordLikeFields: ['a.assetNo', 'a.model', 'a.serialNumber', 'a.brand', 'a.location', 'po.orderNo', 'f.label'],
		addOrderBy: {
			'a.id': 'DESC',
		},
		select: [
			'a.*',
			'b.vendorNo vendorNo',
			'c.assetNo parentAssetNo',
			'c.label parentLabel',
			`JSON_ARRAYAGG(
        JSON_OBJECT(
          'unitPosition', d.unitPosition,
          'vendorRackNo', e.vendorRackNo
        )
      ) AS unitList
      `,
			'f.label serverLabel',
			'po.orderNo poNo',
		],
		join: [
			{
				type: 'leftJoin',
				alias: 'b',
				entity: VendorEntity,
				condition: 'a.vendorId = b.id',
			},
			{
				type: 'leftJoin',
				alias: 'c',
				entity: AssetEntity,
				condition: 'a.parentAssetId = c.id',
			},
			{
				type: 'leftJoin',
				entity: TecRackUnitEntity,
				alias: 'd',
				condition: 'a.id = d.assetId',
			},
			{
				type: 'leftJoin',
				entity: TecRackEntity,
				alias: 'e',
				condition: 'e.id = d.rackId',
			},
			{
				type: 'leftJoin',
				entity: TecServerEntity,
				alias: 'f',
				condition: 'f.id = a.serverId',
			},
			{
				type: 'leftJoin',
				entity: OrderPurchaseEntity,
				alias: 'po',
				condition: 'po.id = a.poId',
			},
		],
		where: async (ctx) => {
			const { assetId, relatedId = 0, serverId, advKeyWord } = ctx.request.body
			const whereList = []
			if (assetId) {
				whereList.push([`(a.id = ${relatedId} OR a.parentAssetId = ${assetId})`, {}])
			}

			// server 模块下的过滤配置
			if (serverId) {
				const advAndWhere = []
				// 多选精确匹配
				const advFieldEq = [
					{ column: 'a.assetType', requestParam: 'advAssetType' },
					{ column: 'a.assetCategory', requestParam: 'advAssetCategory' },
					{ column: 'a.brand', requestParam: 'advBrand' },
					{ column: 'a.model', requestParam: 'advModel' },
					{ column: 'a.status', requestParam: 'advStatus' },
					{ column: 'a.location', requestParam: 'advLocation' },
				]
				const fill = {}
				advFieldEq.forEach((item) => {
					const value = ctx.request.body[item.requestParam]
					if (isEmpty(value)) return
					fill[item.requestParam] = value
					advAndWhere.push(`${item.column} IN (:${item.requestParam})`)
				})
				// keyword
				if (advKeyWord) {
					advAndWhere.push(`(a.assetNo LIKE '%${advKeyWord}%' OR a.serialNumber LIKE '%${advKeyWord}%')`)
				}
				// 最终查询条件
				if (advAndWhere.length > 0) {
					advAndWhere.push('a.serverId IS NULL')
					whereList.push([`((${advAndWhere.join(' AND ')}) OR a.serverId = :serverId)`, { ...fill, serverId }])
				} else {
					whereList.push(['a.serverId = :serverId', { serverId }])
				}
			}

			return whereList
		},
		extend: async (find: SelectQueryBuilder<AssetEntity>) => {
			find.groupBy('a.id')
		},
	},
})
export class AdminAssetController extends BaseController {
	@Inject()
	assetService: AssetService

	@Inject()
	serverService: ServerService

	@Inject()
	dcimService: AdminDcimService

	@Inject()
	utils: Utils

	@Post('/add')
	async handleAdd(@Body() body) {
		return this.assetService.add(body)
	}

	@Post('/ops/update/selected')
	async handelUpdateSelected(@Body() body) {
		if (isEmpty(body.ids)) throw new Error('id 缺失')
		return this.ok(await this.assetService.handelUpdateSelected(body))
	}

	@Post('/relevance/server')
	async handleRelevanceToServer(@Body() body) {
		return this.ok(await this.serverService.handleRelevanceRelation(body))
	}

	@Post('/Release/server')
	async handleReleaseToServer(@Body() body) {
		return this.ok(await this.serverService.handleRelevanceRelation(body, false))
	}

	@Post('/dcim/purchase/select')
	async purchaseListing(@Body() body) {
		return this.ok(await this.dcimService.purchaseListing(body))
	}

	@Post('/select')
	async getSelectData(@Body() body) {
		return this.ok(await this.assetService.handleSelect(body))
	}

	@Post('/ops/select/distinct')
	async getSelectDistinct(@Body('column') column: string, @Body() body: Record<string, any>) {
		return this.utils.getSelectDistinct({
			table: 'op_asset',
			column,
			query: body,
			optionalColumns: ['brand', 'model', 'owner', 'role', 'position', 'rack'],
		})
	}
}

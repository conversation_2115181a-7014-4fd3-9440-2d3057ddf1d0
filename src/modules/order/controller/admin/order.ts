import { Provide, Inject, Get, Post, Query, Body } from '@midwayjs/decorator'
import { CoolController, BaseController } from '@cool-midway/core'
import { Brackets, SelectQueryBuilder } from 'typeorm'

import { isEmpty } from '/@/utils/types'
import { OrderEntity } from '/$/order/entity/order'
import { CustomerEntity } from '/$/customer/entity/customer'
import { ServiceEntity } from '/$/service/entity/service'
import { BaseSysUserEntity } from '/$/base/entity/sys/user'

import {
	OrderService,
	PricingService,
	ProvisionService,
	ConvertNewService,
	BillingService,
	CompletionReviewService,
	TerminateCloseService,
	VoidService,
	ReopenService,
	ServerService,
	IPAddressService,
} from '/$/order/service/order'
import { OrderItem } from '../../entity/order_item'

/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: OrderEntity,
	service: OrderService,
	pageQueryOp: async (ctx) => ({
		keyWordLikeFields: ['orderNo', 'serviceNo', 'a.description', 'd.alias'],
		// fieldEq: ['orderType', 'a.customerId', 'orderStatus'],
		fieldEq: [
			{ column: 'a.id', requestParam: 'id' },
			{ column: 'a.orderType', requestParam: 'orderType' },
			{ column: 'a.customerId', requestParam: 'customerId' },
			{ column: 'a.assignedRoleId', requestParam: 'assignedRoleId' },
			{ column: 'a.serviceId', requestParam: 'serviceId' },
			{ column: 'a.orderStatus', requestParam: 'orderStatus' },
			{ column: 'b.status', requestParam: 'orderItemStatus' },
			{ column: 'b.id', requestParam: 'orderItemId' },
			{ column: 'c.serviceType', requestParam: 'serviceType' },
		],
		select: [
			'a.*',
			'b.id orderItemId',
			'b.status orderItemStatus',
			'c.serviceNo serviceNo',
			'c.serviceStatus serviceStatus',
			'c.serviceType serviceType',
			'd.alias customerAlias',
			'e.name salesManagerName',
		],
		join: [
			{
				type: 'leftJoin',
				entity: OrderItem,
				alias: 'b',
				condition: 'a.id = b.orderId AND b.parentItemId IS NULL',
			},
			{
				type: 'leftJoin',
				entity: ServiceEntity,
				alias: 'c',
				// condition: 'b.serviceId = c.id OR a.serviceId = c.id', // 兼容V1订单
				condition: 'b.serviceId = c.id',
			},
			{
				type: 'leftJoin',
				entity: CustomerEntity,
				alias: 'd',
				condition: 'a.customerId = d.id',
			},
			{
				type: 'leftJoin',
				entity: BaseSysUserEntity,
				alias: 'e',
				condition: 'a.salesManagerId = e.id',
			},
		],

		extend: async function (find: SelectQueryBuilder<OrderEntity>) {
			if (ctx.admin && ctx.admin.roleIds) {
				const roleIds: any[] = ctx.admin?.roleIds ?? []
				// if (roleIds.includes('1')) return

				find?.andWhere(
					new Brackets((queryb) => {
						queryb.orWhere(`find_in_set(a.assignedRoleId, '${roleIds.join(',')}')`)
						roleIds.forEach((rid) => {
							queryb.orWhere(`find_in_set(${rid}, a.visibleRoleId)`)
						})
					})
				)
			}
		},

		where: async (ctx) => {
			const { date } = ctx.request.body ?? {}
			if (date && Array.isArray(date) && date.length === 2) {
				const [startDate, endDate] = date

				return [
					[
						`CAST(a.createTime AS DATE) >= CAST(:startDate AS DATE)
					AND CAST(a.createTime AS DATE) <= CAST(:endDate AS DATE)`,
						{ startDate, endDate },
					],
				]
			}
		},
	}),
	listQueryOp: {
		select: [
			'a.*',
			'b.lastName customerLastName',
			'b.firstName customerFirstName',
			'b.alias customerAlias',
			'b.company customerCompany',
		],
		fieldEq: ['orderType', 'a.customerId', 'orderStatus'],
		join: [
			{
				type: 'leftJoin',
				entity: CustomerEntity,
				alias: 'b',
				condition: 'a.customerId = b.id',
			},
		],
	},
})
export class AdminOrderController extends BaseController {
	// 订单基本操作
	@Inject()
	orderService: OrderService

	// 价格审计操作
	@Inject()
	pricingService: PricingService

	// 服务交付操作
	@Inject()
	provisionService: ProvisionService

	// 计费操作
	@Inject()
	billingService: BillingService

	// 完工审核
	@Inject()
	completionReviewService: CompletionReviewService

	// 服务终止并关闭订单
	@Inject()
	terminateCloseService: TerminateCloseService

	// 作废操作
	@Inject()
	voidService: VoidService

	// 重新开启操作
	@Inject()
	reopenService: ReopenService

	// IP地址操作
	@Inject()
	ipService: IPAddressService

	// 服务器操作
	@Inject()
	serverService: ServerService

	// 转正操作
	@Inject()
	convertNew: ConvertNewService

	@Get('/ops/info/detail', { summary: '获取订单详情' })
	async infoDetail(@Query('id') id) {
		return this.ok(await this.orderService.infoDetail(id))
	}

	@Get('/ops/info/related/service')
	async infoRelatedService(@Query('serviceId') serviceId) {
		return this.ok(await this.orderService.infoRelatedService(serviceId))
	}

	@Post('/ops/update/pricing/approve', { summary: '价格审核通过' })
	async priceAuditApproveHandler(@Body() body) {
		return this.pricingService.handleApprove(body)
	}

	@Post('/ops/update/pricing/reject', { summary: '价格审核失败' })
	async priceAuditRejectHandler(@Body() body) {
		return this.pricingService.handleReject(body)
	}

	@Post('/ops/update/provision/delivery', { summary: '服务交付' })
	async serviceDeliveryHandler2(@Body() body) {
		return this.provisionService.serviceDelivery(body)
	}

	@Post('/ops/update/provision/terminate', { summary: '服务终止' })
	async serviceTerminateHandler(@Body() body) {
		return this.provisionService.serviceTerminate(body)
	}

	@Post('/ops/update/billing/start', { summary: '计费启动' })
	async billStartHandler(@Body() body) {
		return this.billingService.handleBillStart(body)
	}

	@Post('/ops/update/billing/stop', { summary: '计费终止' })
	async billStopHandler(@Body() body) {
		return this.billingService.handleBillStop(body)
	}

	@Post('/ops/update/completion/review', { summary: '完工审核' })
	async completionReviewHandler(@Body() body) {
		return this.completionReviewService.handleCompletionReview(body)
	}

	@Post('/ops/update/terminate/close', { summary: '终止服务并关闭订单' })
	async terminateCloseHandler(@Body() body) {
		return this.terminateCloseService.handleTerminateClose(body)
	}

	@Post('/ops/update/void', { summary: '作废订单' })
	async voidHandler(@Body() body) {
		return this.voidService.handleVoid(body)
	}

	@Post('/ops/update/reopen', { summary: '重新开启' })
	async reopenHandler(@Body() body) {
		return this.reopenService.handleReopen(body)
	}

	@Post('/ops/update/reply', { summary: '回复' })
	async replyMessageHandler(@Body() body) {
		return this.orderService.replyMessageHandler(body)
	}

	@Post('/ops/update/syncDcimToWhmcs', {
		summary: '同步DCIM服务器ID到WHMCS',
	})
	async updateWHAndDcim(@Body() body) {
		return this.ok(await this.serverService.updateWHAndDcim(body))
	}

	// 测试订单转正接口
	@Post('/ops/update/convertnew', { summary: '转正' })
	async convertNewHandler(@Body() body) {
		return this.convertNew.handleConvertNew(body)
	}

	@Post('/ops/update/server/assign', { summary: '服务器分配' })
	async serverAssignHandler(@Body() body) {
		return this.serverService.handleServerAssign(body)
	}

	@Post('/ops/update/server/release', { summary: '服务器回收' })
	async serverReleaseHandler(@Body() body) {
		return this.serverService.handleServerRelease(body)
	}

	@Post('/ops/update/ip/assign', { summary: 'IP分配' })
	async ipAssignHandler(@Body() body) {
		return this.ipService.handleIPAssign(body)
	}

	@Post('/ops/update/ip/release', { summary: 'IP回收' })
	async ipReleaseHandler(@Body() body) {
		return this.ipService.handleIPRelease(body)
	}

	@Post('/ops/update/sync', { summary: '同步订单' })
	async syncHandler(@Body() body) {
		return this.orderService.syncHandler(body)
	}

	@Post('/ops/update/send/zoho', { summary: '发送Zoho邮件' })
	async sendMessagehandler(@Body() body) {
		return this.orderService.sendMessageHandler(body)
	}

	@Post('/ops/update/mainIp', { summary: '设置主IP' })
	async setMainIpHandler(@Body() body) {
		if (isEmpty(body.ipId)) return this.fail('IP ID不能为空')
		return this.ipService.handleUpdateMainIp(body)
	}

	@Post('/select')
	async getSelectData() {
		return this.page()
	}
}

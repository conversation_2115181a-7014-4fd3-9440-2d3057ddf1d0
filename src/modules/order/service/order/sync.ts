import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { QueryRunner } from 'typeorm'
import { OrderLogEntity } from '../../entity/order_log'

import { CustomerEntity } from '../../../customer/entity/customer'
import { ServiceEntity } from '../../../service/entity/service'
import { whmcsCdApi } from '../../../../vendor/whmcs-cd'
import { OrderEntity } from '../../entity/order'
import { InfoService } from './info'
import { ServiceType } from '../../../../global/constants'
import { OrderReplyEntity } from '../../entity/reply'
import { SendMessageService } from './send-message'
import { FRONTEND_ADDRESS_ALL } from '../../../../global/env'
import { handleDiscordReplyMessage } from '../../utils'
import { whmcsCustomFieldsFormat, whmcsPidFormat } from '../../../../vendor/whmcs-cd/utils/format'

/**
 * 描述
 */
@Provide()
export class SyncService extends BaseService {
	@Inject()
	infoService: InfoService

	@Inject()
	sendMessageService: SendMessageService

	@Inject()
	ctx

	@CoolTransaction()
	// /order-detail?id=number/sync
	async execute(body, queryRunner?: QueryRunner) {
		const { username, userId } = this.ctx.admin
		const { orderId, customerId, priceoverride, hostname, billingcycle, serviceId, customFields1, customFields2 } = body

		try {
			// 1. 判断当前订单ID/客户ID/服务ID是否存在值
			if (!orderId || !customerId || !serviceId) {
				throw new Error('No parameters passed in orderId | customerId | serviceId')
			}

			// 2. 获取当前客户信息
			const currentCustomer = await queryRunner.manager.findOne(CustomerEntity, { id: customerId })

			if (!currentCustomer.cdWhClientId) {
				throw new Error('客户未关联whmcs账号')
			}

			// 3. 获取当前订单 = {order, service, subservice}
			const { order, service } = await this.infoService.infoDetail(orderId, queryRunner)

			// 4. 同步到whmcsCd
			const addWHOrderResult = await whmcsCdApi.add('AddOrder', {
				clientid: currentCustomer.cdWhClientId,
				paymentmethod: 'banktransfer',
				noinvoice: true,
				priceoverride,
				hostname,
				billingcycle,
				pid: whmcsPidFormat(service.serviceType as ServiceType),
				customfields: whmcsCustomFieldsFormat(service.serviceType as ServiceType, {
					location: customFields1,
					serviceDetails: customFields2,
				}),
			})

			console.log('addWHOrderResult', addWHOrderResult)
			const { result, orderid, serviceids, message: resultMessage } = addWHOrderResult

			// 5. 判断返回值
			if (result !== 'success' || !orderid || !serviceids) {
				throw new Error(
					`WhmcsCd 同步返回值异常: ${JSON.stringify({
						result,
						orderid,
						serviceids,
					})}${resultMessage}`
				)
			}

			await queryRunner.manager.update(OrderEntity, { id: orderId }, { cdWhOrderId: orderid })

			const whCdServiceFirstId = serviceids?.split(',')[0]
			await queryRunner.manager.update(ServiceEntity, { id: serviceId }, { cdWhServiceId: whCdServiceFirstId })

			// 5. 更新订单回复
			const replyMessage = `${username} 同步订单成功，新增WhmcsCd订单: orderid=${orderid};serviceids=${serviceids}`
			await queryRunner.manager.insert(OrderReplyEntity, {
				userId,
				orderId: orderId,
				replyMessage,
				currentAction: 'Price Approve',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: order.orderStatus,
				createBy: username,
				updateBy: username,
			})

			// 6. 更新日志
			await queryRunner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Price Approve',
				actionBy: username,
				paramsJson: JSON.stringify(body),
				actionDetails: JSON.stringify({
					orderStatus: order.orderStatus,
					assignedStaffId: order.assignedStaffId,
					assignedRoleId: order.assignedRoleId,
				}),
			})

			// 7. 发送discord信息
			await this.sendMessageService.sendDiscordMessage({
				params: { userId, serviceId: service.id },
				async cb({ user: assignedUser, service }) {
					return [
						{
							link: {
								title: `#${service.serviceNo}/${order.orderNo} - Sync.`,
								source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
							},
							blockquote: `${username}: <@${assignedUser?.discordId}> ${handleDiscordReplyMessage(replyMessage)}`,
						},
					]
				},
			})

			return {
				code: 1000,
				message: `Sync success! Synced orderid=${orderid};serviceids=${serviceids}`,
				data: {
					cdServiceid: serviceids,
					cdOrderid: orderid,
				},
			}
		} catch (e) {
			throw new Error(e.message)
		}
	}
}

import { InjectEntityModel } from '@midwayjs/orm'
import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { QueryRunner, Repository } from 'typeorm'
import * as _ from 'lodash'

// entity
import { OrderReplyEntity } from '../../entity/reply'
import { OrderLogEntity } from '../../entity/order_log'
import { TecServerEntity } from '../../../tec/entity/server'
import { TecProvisionLogEntity } from '../../../tec/entity/provision_log'
import { SendMessageService } from './send-message'

// env
import { FRONTEND_ADDRESS_ALL } from '../../../../global/env'
import { ServiceEntity } from '/@/modules/service/entity/service'
import { whmcsCdSql } from '/@/vendor/whmcs-cd'
import { ResponseMessage } from '/@/utils/message'
import { dcimServer } from '/@/vendor/dcim'

/**
 * 描述
 */
@Provide()
export class ServerService extends BaseService {
	@Inject()
	sendMessageService: SendMessageService

	@InjectEntityModel(TecServerEntity)
	tecServerEntity: Repository<TecServerEntity>

	@Inject()
	ctx

	@CoolTransaction()
	async handleServerAssign(body, runner?: QueryRunner) {
		const message = new ResponseMessage()

		const { order, service, upsert } = body
		const { username, userId } = this.ctx.admin

		// 1. 更新服务器
		try {
			await runner.manager.update(
				TecServerEntity,
				{ id: upsert.server },
				{
					customerId: order.customerId,
					serviceId: service.id,
					status: 'Assigned',
				}
			)
			message.success('服务器更新成功')
		} catch (e) {
			message.fail(e.message)
			message.error('服务器更新失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 2. 更新订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId: order.id,
				replyMessage: upsert.details,
				currentAction: 'Server Assign',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: order.orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success('订单回复成功')
		} catch (e) {
			message.fail(e.message)
			message.error('订单回复失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 3. 更新日志
		try {
			const serverRes = await runner.manager.findOne(TecServerEntity, upsert.server)

			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				referenceIds: serverRes.serverNo,
				actionType: 'Server Assign',
				paramsJson: JSON.stringify(body),
				actionDetails: upsert.details,
				actionBy: username,
			})

			message.success('更新订单日志成功')
		} catch (e) {
			message.fail(e.message)
			message.error('更新订单日志失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 4. 更新serverlog
		try {
			await runner.manager.insert(TecProvisionLogEntity, {
				userId,
				orderId: order.id,
				serviceId: service.id,
				serverId: upsert.server,
				actionType: 'Server Assign',
			})
			message.success('ServerLog更新成功')
		} catch (e) {
			message.fail(e.message)
			message.error('ServerLog更新失败', e.message, 'Rollbacking...')
			return message.value()
		}

		try {
			await this.sendMessageService.sendDiscordMessage({
				params: { userId: order.assignedStaffId, serviceId: service.id },
				async cb({ user: assignedUser, service }) {
					return [
						{
							link: {
								title: `#${service.serviceNo}/${order.orderNo} - Service assign.`,
								source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
							},
							blockquote: `${username}: <@${assignedUser?.discordId}>`,
						},
					]
				},
			})
		} catch (e) {
			message.warning('发送discord消息失败', e.message)
		}

		return message.ok('服务器分配成功').success('服务器分配成功').value()
	}

	@CoolTransaction()
	async handleServerRelease(body, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const { username, userId } = this.ctx.admin
		const { ids, order, upsert, service } = body

		// 1. 更新服务器
		try {
			await runner.manager.update(TecServerEntity, ids, {
				status: 'Available',
				customerId: null,
				serviceId: null,
				updateBy: username,
			})
			message.success('OPS服务器释放成功')
		} catch (e) {
			message.fail(e.message)
			message.error('OPS服务器释放失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 2. 更新订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId: order.id,
				currentAction: 'Server Release',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: order.orderStatus,
				replyMessage: upsert.releaseMessage,
				createBy: username,
				updateBy: username,
			})
			message.success('写入订单回复成功')
		} catch (e) {
			message.fail(e.message)
			message.error('写入订单回复失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 3. 更新日志
		try {
			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: order.serviceId,
				referenceIds: ids.join(','),
				actionType: 'Server Release',
				paramsJson: JSON.stringify(body),
				actionDetails: upsert.releaseMessage,
				actionBy: username,
			})
			message.success('写入订单日志成功')
		} catch (e) {
			message.fail(e.message)
			message.error('写入订单日志失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 4. 更新serverlog
		try {
			const historyLogs = ids.map((id) => ({
				userId,
				orderId: order.id,
				serviceId: order.serviceId,
				serverId: id,
				actionType: 'Server Release',
			}))

			await runner.manager.insert(TecProvisionLogEntity, historyLogs)
			message.success('ServerLog更新成功')
		} catch (e) {
			message.fail(e.message)
			message.error('ServerLog更新失败', e.message, 'Rollbacking...')
			return message.value()
		}

		// 5. 回收DCIM 服务器
		const promises = ids.map(async (serverId) => {
			try {
				await this.handleDcimServerRelease({ serverId, service }, message, runner)
			} catch (e) {
				message.warning('回收DCIM服务器失败', e.message)
			}
		})

		await Promise.all(promises)

		try {
			await this.sendMessageService.sendDiscordMessage({
				params: { userId: order.assignedStaffId, serviceId: service.id },
				async cb({ user: assignedUser, service }) {
					return [
						{
							link: {
								title: `#${service.serviceNo}/${order.orderNo} - Service release.`,
								source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
							},
							blockquote: `${username}: <@${assignedUser?.discordId}>`,
						},
					]
				},
			})
		} catch (e) {
			message.warning('发送discord消息失败', e.message)
		}

		return message.ok('服务器已回收').success('服务器已回收').value()
	}

	@CoolTransaction()
	async handleDcimServerRelease(param: any, message: ResponseMessage, runner?: QueryRunner) {
		const { serverId, service } = param
		const cdWhServiceId = service.cdWhServiceId

		const server = await this.tecServerEntity.findOne(serverId)

		if (!server || !server.dcimId) {
			return
		}

		const dcimServerId = server.dcimId

		// 重置OPS-Server.dcimId
		// try {
		// 	await runner.manager.update(TecServerEntity, serverId, { dcimId: null })
		// 	message.success('已重置OPS服务器关联的DCIM服务器信息')
		// } catch (e) {
		// 	message.fail(e.message)
		// 	message.warning('重置OPS服务器关联的DCIM服务器信息失败', e.message)
		// 	return message.value()
		// }

		// 修改dcimServer.nbtag为可分配
		try {
			await dcimServer.changeDcimServerNbtagById(+dcimServerId, '可分配')
			message.success('已修改DCIM服务器nbtag为可分配')
		} catch (e) {
			message.fail(e.message)
			message.warning('修改DCIM服务器nbtag为可分配失败', e.message)
			return message.value()
		}

		// 释放所有Dcim IP
		try {
			await dcimServer.recycleDcimServerIpById(+dcimServerId)
			message.success('已释放当前DCIM服务器所有IP')
		} catch (e) {
			message.fail(e.message)
			message.warning('释放当前DCIM服务器所有IP失败', e.message)
			return message.value()
		}

		// 如果当前服务器有关联的WH服务器，删除关联记录
		try {
			if (!cdWhServiceId) return
			await whmcsCdSql
				.builder()
				.delete()
				.from('mod_custom_ipmi')
				.where('serviceid = :cdWhServiceId', { cdWhServiceId })
				.andWhere('ipmi_id = :dcimId', { dcimId: dcimServerId })
				.execute()
			message.success('已删除关联的WH服务器记录')
		} catch (e) {
			message.fail(e.message)
			message.warning('删除关联的WH服务器记录失败')
			return message.value()
		}
	}

	@CoolTransaction()
	async updateWHAndDcim(param, runner?: QueryRunner) {
		const { serviceId, serverId } = param

		// 获取服务
		const service = await runner.manager.findOne<ServiceEntity>(ServiceEntity, serviceId)

		if (!service.cdWhServiceId) throw new Error('该订单服务未关联WH')

		// 获取当前服务器
		const server = await runner.manager.findOne<TecServerEntity>(TecServerEntity, serverId)

		if (!server.dcimId) throw new Error('该服务器未关联DCIM')

		// 开始关联
		await whmcsCdSql
			.builder()
			.update('mod_custom_ipmi')
			.set({ ipmi_id: server.dcimId })
			.where('serviceid = :cdWhServiceId', {
				cdWhServiceId: service.cdWhServiceId,
			})
			.execute()

		return 'DCIM关联WH服务器成功'
	}
}

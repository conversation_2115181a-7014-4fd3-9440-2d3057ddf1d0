import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { QueryRunner, Repository, Not } from 'typeorm'

// entity
import { OrderEntity } from '/$/order/entity/order'
import { OrderReplyEntity } from '/$/order/entity/reply'
import { OrderLogEntity } from '/$/order/entity/order_log'
import { ServiceEntity } from '/$/service/entity/service'

// service
import { ServiceService } from '/$/service/service/service'

import { InjectEntityModel } from '@midwayjs/orm'
import { BaseSysUserEntity } from '/$/base/entity/sys/user'
import { SendMessageService } from './send-message'

// env
import { FRONTEND_ADDRESS_ALL } from '/@/global/env'
import { whmcsCdApi, whmcsCdSql } from '/@/vendor/whmcs-cd'
import { isExistValue } from '/@/utils'
import { whmcsCustomFieldsFormat } from '/@/vendor/whmcs-cd/utils/format'
import { TecIpaddEntity } from '/$/tec/entity/ipadd'
import * as _ from 'lodash'
import { TecServerEntity } from '/$/tec/entity/server'
import ResponseMessage from '/@/utils/message'
import { dcimServer } from '/@/vendor/dcim'
import { TextEnum } from '/$/order/enums/textEnum'

/**
 * 描述
 */
@Provide()
export class ProvisionService extends BaseService {
	@InjectEntityModel(BaseSysUserEntity)
	userEntity: Repository<BaseSysUserEntity>

	@InjectEntityModel(TecIpaddEntity)
	ipEntity: Repository<TecIpaddEntity>

	@InjectEntityModel(TecServerEntity)
	tecServerRepository: Repository<TecServerEntity>

	@Inject()
	serviceService: ServiceService

	@Inject()
	sendMessageService: SendMessageService

	@Inject()
	ctx

	@CoolTransaction()
	async handleDcimServerAssign(param, message: ResponseMessage, runner?: QueryRunner) {
		const { server, service, primaryIp } = param

		// 更新ops当前服务绑定的所有ip.serverId = server.id
		// try {
		// 	await runner.manager.update(
		// 		TecIpaddEntity,
		// 		{ serviceId: service.id },
		// 		{ serverId: server.id }
		// 	)
		// 	message.success('已更新OPS当前服务绑定的IP到服务器', server.serverNo)
		// } catch (e) {
		// 	runner.rollbackTransaction()
		// 	return message
		// 		.fail(e.message)
		// 		.error(
		// 			'更新OPS当前服务绑定的IP到服务器失败。',
		// 			e.message,
		// 			'Rollbacking...'
		// 		)
		// 		.value()
		// }

		// 更新dcim服务器编号
		try {
			await dcimServer.changeDcimServerNbtagById(server.dcimId, service.serviceNo)
			message.success('已更新DCIM服务器nbtag', service.serviceNo)
		} catch (e) {
			message.warning('更新DCIM服务器nbtag失败。', e.message)
		}

		// 更新dcim服务器主IP
		try {
			const mainIp = await runner.manager.findOne(TecIpaddEntity, {
				where: { serviceId: service.id, serverId: server.id, ipStr: primaryIp },
			})

			if (mainIp) {
				await dcimServer.bindMainIpToDcimServer(server.dcimId, mainIp.ipStr)
				message.success('已更新DCIM服务器主IP', mainIp.ipStr)
			}
		} catch (e) {
			message.warning('更新DCIM服务器主IP失败。', e.message)
		}

		// 更新dcim服务器IP
		// 获取当前服务的所有已分配IP地址
		// try {
		// 	const ips = await runner.manager.find(TecIpaddEntity, {
		// 		where: {
		// 			ipStr: Not(primaryIp),
		// 			serviceId: service.id,
		// 			serverId: server.id,
		// 			status: 'Assigned',
		// 		},
		// 	})

		// 	if (ips.length) {
		// 		await dcimServer.bindIpToDcimServer(
		// 			server.dcimId,
		// 			ips.map((item) => item.ipStr)
		// 		)
		// 		message.success('已更新DCIM服务器IP', ...ips.map((item) => item.ipStr))
		// 	}
		// } catch (e) {
		// 	message.warning('更新DCIM服务器IP失败。', e.message)
		// }

		// 如果服务存在 cdWhServiceId，则更新whmcs.mod_custom_ipmi数据库
		// 更新内容：ipmi_id
		console.log('=========================交付更新WHMCS IPMI ID=========================')
		console.log('service.cdWhServiceId', service.cdWhServiceId)
		console.log('server.dcimId', server.dcimId)
		try {
			if (service.cdWhServiceId) {
				const result = await whmcsCdSql
					.builder()
					.update('mod_custom_ipmi')
					.set({ ipmi_id: server.dcimId })
					.where('serviceid = :cdWhServiceId', {
						cdWhServiceId: service.cdWhServiceId,
					})
					.execute()
				console.log('更新结果', result)
				message.success('已更新WHMCS-CD IPMI信息。')
			}
		} catch (e) {
			message.warning('更新WHMCS-CD IPMI信息失败。', e.message)
		}
	}

	@CoolTransaction()
	async serviceDelivery(body, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const userId = this.ctx.admin?.userId
		const username = this.ctx.admin?.username

		const { order, service, subservice } = body.detail
		const {
			assignedStaffId,
			assignedRoleId,
			orderStatus,
			replyMessage,
			activeDate,
			primaryIp, // 执行主IP（IP str）
			description, // 新的服务描述
		} = body.upsert

		// 更新 OPS 订单信息
		try {
			await runner.manager.update(
				OrderEntity,
				{ id: order.id },
				{
					orderStatus,
					assignedRoleId,
					assignedStaffId,
					updateBy: username,
				}
			)

			message.success(TextEnum.ORDER_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 插入订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId: userId,
				orderId: order.id,
				replyMessage,
				currentAction: 'Service Delivery',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success(TextEnum.ORDER_REPLY_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			message
				.fail(e.message)
				.error(TextEnum.ORDER_REPLY_INSERT_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新订单日志
		try {
			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Service Provision',
				actionBy: username,
				paramsJson: JSON.stringify(body),
				actionDetails: JSON.stringify({
					orderStatus,
					assignedStaffId,
					assignedRoleId,
				}),
			})
			message.success(TextEnum.ORDER_LOG_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_LOG_INSERT_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新服务主表
		try {
			const updateSerInfo: Record<string, any> = {
				serviceStatus: 'Active',
				primaryIp: primaryIp && primaryIp !== 'NA' ? primaryIp : subservice.productName,
				serviceDesc: description,
			}

			if (order.orderType === 'New') updateSerInfo.firstActiveDate = activeDate

			await runner.manager.update(ServiceEntity, { id: service.id }, updateSerInfo)

			message.success(TextEnum.SERVICE_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新当前子服务
		try {
			await this.serviceService.updateSubService(
				{
					service,
					subservice: {
						id: subservice.id,
						status: 'Active',
						activeDate: activeDate,
						description: description,
					},
				},
				runner
			)
			message.success(TextEnum.SERVICE_OPTION_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_OPTION_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 将服务上一个子服务状态改为 Terminated
		try {
			const subserviceList = await this.serviceService.getServiceOptions(service.id)

			const preActiveSubservice = _.chain(subserviceList)
				.filter((item) => item.status === 'Active' && item.id !== subservice.id)
				.maxBy((item) => item.id)
				.value()

			if (preActiveSubservice) {
				await this.serviceService.updateSubService(
					{
						service,
						subservice: {
							id: preActiveSubservice.id,
							status: 'Terminated',
							terminateDate: activeDate,
						},
					},
					runner
				)

				message.success(TextEnum.SERVICE_OPTION_PREV_UPDATE_SUCCESS)
			}
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_OPTION_PREV_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		/**
		 * 更新DCIM服务器相关信息
		 * 前置条件:
		 *  1. 服务类型为 Server
		 *  2. 关联服务器存在 dcimId
		 */
		// 更新DCIM服务器
		// 条件：服务类型为 Server, 且关联服务器存在 dcimId
		console.log('=========================交付更新DCIM服务器=========================')
		console.log('service.serviceType', service.serviceType)
		console.log('service.id', service.id)

		try {
			if (service.serviceType === 'Server') {
				const server = await this.tecServerRepository.findOne({
					where: { serviceId: service.id },
				})

				console.log('DCIM服务器', server.dcimId)
				console.log('server', server)

				if (server?.dcimId) {
					const param = {
						server,
						service,
						primaryIp,
					}

					await this.handleDcimServerAssign(param, message, runner)
				}
			}
		} catch (e) {
			message.warning('更新DCIM服务器失败。', e.message)
		}

		// 同步更新WhmcsCd服务
		try {
			if (isExistValue(service.cdWhServiceId)) {
				const {
					type,
					productName,
					cpuCores,
					memory,
					storage,
					networkProductName,
					bandwidth,
					burstBandwidth,
					protectedBandwidth,
					cleanBandwidth,
					ipAddress,
					description,
					rackUnit,
					powerAmp,
					serviceDetails,
				} = subservice ?? {}

				// 获取当前服务的所有已分配IP地址
				let ips = []
				ips = await runner.manager.find(TecIpaddEntity, {
					serviceId: service.id,
					status: 'Assigned',
				})

				// 订单交付 获取主ip的详情
				// const ipDetails = await this.ipEntity.findOne(primaryIp)
				const updateClientProductInfo: Record<string, any> = {
					serviceid: service.cdWhServiceId,
					regdate: order.orderType === 'New' ? activeDate : undefined, // 只有订单类型为New时才更新regdate
					status: 'Active',
					domain: service.serviceNo + '#' + `${primaryIp && primaryIp !== 'NA' ? primaryIp : productName}`,
					dedicatedip: primaryIp && primaryIp !== 'NA' ? primaryIp : productName,
					assignedips: ips.map((item) => item.ipStr).join(','),
					customfields: whmcsCustomFieldsFormat(service.serviceType, {
						location: subservice.location,
						serviceDetails: subservice.serviceDetails,
					}),
				}

				await whmcsCdApi.update('UpdateClientProduct', updateClientProductInfo)
				message.success(TextEnum.SERVICE_WH_UPDATE_SUCCESS)
			}
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_WH_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 发送discord消息, 发送消息错误不会回滚
		try {
			await this.sendMessageService.sendDiscordMessage({
				params: { userId: assignedStaffId, serviceId: service.id },
				async cb({ user: assignedUser, service }) {
					return [
						{
							link: {
								title: `#${service.serviceNo}/${order.orderNo} - Service delivery.`,
								source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
							},
							blockquote: `${username}: <@${assignedUser?.discordId}> ${replyMessage}`,
						},
					]
				},
			})
			message.success(TextEnum.DISCORD_MESSAGE_SUCCESS)
		} catch (e) {
			message.warning(TextEnum.DISCORD_MESSAGE_FAIL, e.message)
		}

		return message.ok('服务已交付').success('服务已交付').value()
	}

	@CoolTransaction()
	async serviceTerminate(body, runner?: QueryRunner) {
		const message = new ResponseMessage()
		const { username, userId } = this.ctx.admin
		const { order, service, subservice } = body.detail
		const { assignedStaffId, assignedRoleId, orderStatus, terminateDate, replyMessage } = body.upsert

		// 更新订单
		try {
			await runner.manager.update(
				OrderEntity,
				{ id: order.id },
				{
					orderStatus,
					assignedRoleId,
					assignedStaffId,
					updateBy: username,
				}
			)

			message.success(TextEnum.ORDER_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新订单回复
		try {
			await runner.manager.insert(OrderReplyEntity, {
				userId,
				orderId: order.id,
				replyMessage,
				currentAction: 'Service Terminate',
				currentOrderStatus: order.orderStatus,
				newOrderStatus: orderStatus,
				createBy: username,
				updateBy: username,
			})
			message.success(TextEnum.ORDER_REPLY_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_REPLY_INSERT_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新订单日志
		try {
			await runner.manager.insert(OrderLogEntity, {
				orderId: order.id,
				serviceId: service.id,
				actionType: 'Service Provision',
				actionBy: username,
				paramsJson: JSON.stringify(body),
				actionDetails: JSON.stringify({
					status: orderStatus,
					assignedStaffId,
					assignedRoleId,
				}),
			})
			message.success(TextEnum.ORDER_LOG_INSERT_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.ORDER_LOG_INSERT_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 更新服务主表
		try {
			await runner.manager.update(
				ServiceEntity,
				{ id: service.id },
				{ serviceStatus: 'Terminated', terminateDate: terminateDate }
			)
			message.success(TextEnum.SERVICE_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 5. 更新当前子服务
		try {
			await this.serviceService.updateSubService(
				{
					service,
					subservice: {
						id: subservice.id,
						status: 'Terminated',
						terminateDate: terminateDate,
					},
				},
				runner
			)
			message.success(TextEnum.SERVICE_OPTION_UPDATE_SUCCESS)
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_OPTION_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 将服务上一个子服务状态改为 Terminated
		try {
			const subserviceList = await this.serviceService.getServiceOptions(service.id)

			const preActiveSubservice = _.chain(subserviceList)
				.filter((item) => item.status === 'Active' && item.id !== subservice.id)
				.maxBy((item) => item.id)
				.value()

			if (preActiveSubservice) {
				await this.serviceService.updateSubService(
					{
						service,
						subservice: {
							id: preActiveSubservice.id,
							status: 'Terminated',
							terminateDate: terminateDate,
						},
					},
					runner
				)

				message.success(TextEnum.SERVICE_OPTION_PREV_UPDATE_SUCCESS)
			}
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_OPTION_PREV_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// #region 同步终止WHMCS-CD服务 /order-detail?id=number/provision/serviceTerminate
		// 更新WH服务信息
		try {
			if (isExistValue(service.cdWhServiceId)) {
				await whmcsCdApi.update('UpdateClientProduct', {
					serviceid: service.cdWhServiceId,
					terminationdate: terminateDate,
					status: 'Terminated',
				})

				message.success(TextEnum.SERVICE_WH_UPDATE_SUCCESS)
			}
		} catch (e) {
			runner.rollbackTransaction()
			return message
				.fail(e.message)
				.error(TextEnum.SERVICE_WH_UPDATE_FAIL, e.message)
				.error(TextEnum.TRANSACTION_ROLLBACK)
				.value()
		}

		// 发送discord消息
		// 发送消息错误不会回滚
		try {
			await this.sendMessageService.sendDiscordMessage({
				params: { userId: assignedStaffId, serviceId: service.id },
				async cb({ user: assignedUser, service }) {
					return [
						{
							link: {
								title: `#${service.serviceNo}/${order.orderNo} - Service terminate.`,
								source: `${FRONTEND_ADDRESS_ALL}/order-detail?id=${order.id}`,
							},
							blockquote: `${username}: <@${assignedUser?.discordId}> ${replyMessage}`,
						},
					]
				},
			})
			message.success(TextEnum.DISCORD_MESSAGE_SUCCESS)
		} catch (e) {
			message.warning(TextEnum.DISCORD_MESSAGE_FAIL, e.message)
		}

		return message.ok('服务已停止').success('服务已停止').value()
	}
}

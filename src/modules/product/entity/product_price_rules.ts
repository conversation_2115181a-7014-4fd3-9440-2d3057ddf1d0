import { EntityModel } from '@midwayjs/orm'
import { BaseEntity } from '@cool-midway/core'
import { Column } from 'typeorm'

/**
 * 描述
 */
@EntityModel('op_product_price_rules')
export class ProductPriceRulesEntity extends BaseEntity {
	// matchMethod
	@Column({ comment: '匹配方法', type: 'enum', enum: ['regex', 'include', 'exclude'], default: 'regex' })
	matchMethod: 'regex' | 'include' | 'exclude'

	// matchPattern
	@Column({ comment: '匹配模式', default: '' })
	matchPattern: string

	// 描述
	@Column({ comment: '描述', default: '' })
	description: string

	// 是否启用
	@Column({ comment: '是否启用', default: true })
	isActive: boolean

	// createBy/updateBy
	@Column({ comment: '创建者', length: 50, default: '' })
	createBy: string

	@Column({ comment: '更新者', length: 50, default: '' })
	updateBy: string
}

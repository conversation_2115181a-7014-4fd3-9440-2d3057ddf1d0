import { Post, Provide } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'

import { ProductPriceRulesEntity } from '/$/product/entity/product_price_rules'

/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: ProductPriceRulesEntity,
	pageQueryOp: {
		fieldEq: ['isActive', 'id'],
		keyWordLikeFields: ['description'],
	},
})
export class AdminProductPriceRulesController extends BaseController {
	@Post('/select')
	async getSelectData() {
		return this.page()
	}
}

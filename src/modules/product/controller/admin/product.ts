import { Body, Get, Inject, Post, Provide, Query } from '@midwayjs/decorator'
import { CoolController, BaseController } from '@cool-midway/core'
import { ProductEntity } from '../../entity/product'
import { ProductService } from '../../service/product'
import { Utils } from '/@/comm/utils'
import { PageOption } from '/@/utils/orm'
import { getSelectResult } from '/@/utils/interface-proxy'

export const productCommonQueryOp: PageOption = {
	select: ['a.*', 'b.name parentName', 'b.category parentCategory'],
	fieldEq: [
		{ column: 'a.id', requestParam: 'id' },
		{ column: 'a.category', requestParam: 'category' },
		{ column: 'a.subCategory', requestParam: 'subCategory' },
		{ column: 'a.thirdCategory', requestParam: 'thirdCategory' },
		{ column: 'a.type', requestParam: 'type' },
		{ column: 'a.parentProductId', requestParam: 'parentProductId' },
	],
	findInSet: [
		{ column: 'a.region', requestParam: 'region', useAll: true },
		{ column: 'a.location', requestParam: 'location', useAll: true },
		{ column: 'a.tags', requestParam: 'tags' },
	],
	keyWordLikeFields: ['a.productNo', 'a.name', 'a.detail'],
	join: [
		{
			entity: ProductEntity,
			type: 'leftJoin',
			alias: 'b',
			condition: 'a.parentProductId = b.id',
		},
	],
}

/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: ProductEntity,
	service: ProductService,
	pageQueryOp: productCommonQueryOp,
	listQueryOp: productCommonQueryOp,
})
export class ProductController extends BaseController {
	@Inject()
	productService: ProductService

	@Inject()
	utils: Utils

	@Get('/detail')
	async getProductDetail(@Query('id') id) {
		if (!id) return this.fail('Product ID is required')
		return this.ok(await this.productService.getProductDetail(+id))
	}

	@Get('/content')
	async getProductContent(@Query('id') productId) {
		return this.productService.getProductContent(productId)
	}

	@Post('/active')
	async handleChangeStatus(@Body() body) {
		const { id, status } = body
		if (!id || !status) return this.fail('ID or start invalid')

		if (status === 'Active') {
			return this.ok(await this.productService.update({ id, status }))
		}

		return this.ok(await this.productService.changeStatus(id, status))
	}

	@Post('/select')
	async getSelectData() {
		return getSelectResult.call(this)
	}

	@Post('/ops/select/distinct')
	async getSelectDistinct(@Body('column') column: string, @Body() body: Record<string, any>) {
		return this.utils.getSelectDistinct({
			table: 'op_product',
			column,
			query: body,
			optionalColumns: ['category', 'subCategory', 'thirdCategory', 'region', 'location', 'type', 'tags'],
		})
	}

	@Post('/ops/page/serverAvailableTotal')
	async getServerAvailableTotal(@Body() body) {
		return this.ok(await this.productService.getServerAvailableTotal(body))
	}

	@Post('/ops/page/ipAddressAvailableTotal')
	async getIpAddressAvailableTotal(@Body() body) {
		return this.ok(await this.productService.getIpAddressAvailableTotal(body))
	}

	@Get('/category/cascader')
	async getCategoryCascader(@Query('target') target: 'Product' | 'ProductPlan' = 'Product') {
		return this.ok(await this.productService.getCategoryCascader(target))
	}
}

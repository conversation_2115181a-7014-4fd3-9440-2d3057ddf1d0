import { Provide } from '@midwayjs/decorator'
import { BaseService } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { Repository } from 'typeorm'
import { ProductEntity } from '../entity/product'
import { ProductPriceRulesEntity } from '../entity/product_price_rules'

/**
 * 配置匹配结果接口
 */
export interface MatchResult {
	category: string
	qty: number
	content: string
	unitPrice?: number
	totalPrice?: number
}

/**
 * 配置选项接口
 */
export interface ConfigOptions {
	[key: string]: string
}

/**
 * 查询测试：
 *  SELECT
        op.id,
        op.productNo,
        op.subCategory,
        op.thirdCategory,
        op.productPriceRuleId,
        CASE
            WHEN op.productPriceRuleId IS NULL THEN NULL
            ELSE JSON_OBJECT('id', oppr.id, 'matchMethod', oppr.matchMethod, 'matchPattern', oppr.matchPattern)
        END as productPriceRule,
        JSON_ARRAYAGG(opp.tierPrice) as productListPrice
    FROM `op_product` op
    LEFT JOIN op_product_price_rules oppr ON oppr.id = op.productPriceRuleId
    LEFT JOIN op_product_price opp ON opp.productId = op.id AND opp.tier = "List"
    GROUP BY op.id
    ORDER BY op.id DESC
 */

/**
 * 配置匹配服务，主调用函数为matchConfig，格式化函数为formatResults
 */
@Provide()
export class ConfigMatcherService extends BaseService {
	@InjectEntityModel(ProductEntity)
	productRepository: Repository<ProductEntity>

	@InjectEntityModel(ProductPriceRulesEntity)
	productPriceRulesRepository: Repository<ProductPriceRulesEntity>

	/**
	 * 提取数量和实际值
	 * @param value 原始值，如 "2 * E3-1240v2"
	 * @returns {quantity: number, actualValue: string} 数量和实际值
	 */
	private extractQuantityAndValue(value: string) {
		const match = value.match(/^(\d+)\s*\*\s*(.+)$/)
		if (match) {
			return {
				quantity: parseInt(match[1]),
				actualValue: match[2].trim(),
			}
		}
		return {
			quantity: 1,
			actualValue: value,
		}
	}

	/**
	 * 正则表达式匹配（不区分大小写）
	 */
	private matchRegex(text: string, pattern: string, flags?: string): boolean {
		try {
			// 添加 i 标志使正则表达式不区分大小写
			const finalFlags = flags ? `${flags}i` : 'i'
			const regex = new RegExp(pattern, finalFlags)
			return regex.test(text)
		} catch (error) {
			console.error('正则表达式匹配错误:', error)
			return false
		}
	}

	/**
	 * 包含匹配（不区分大小写）
	 */
	private matchInclude(text: string, pattern: string): boolean {
		return text.toLowerCase().includes(pattern.toLowerCase())
	}

	/**
	 * 排除匹配（不区分大小写）
	 */
	private matchExclude(text: string, pattern: string): boolean {
		return !text.toLowerCase().includes(pattern.toLowerCase())
	}

	/**
	 * 复杂模式匹配函数
	 */
	private matchComplex(
		text: string,
		method: 'regex' | 'include' | 'exclude',
		patterns: string,
		flags?: string
	): boolean {
		// 按换行符分割，得到 OR 关系的组
		const orGroups = patterns.split('\n').filter((group) => group.trim())

		// 只要有一个 OR 组匹配成功，就返回 true
		return orGroups.some((orGroup) => {
			// 按逗号分割，得到 AND 关系的模式
			const andPatterns = orGroup
				.split(',')
				.map((pattern) => pattern.trim())
				.filter((pattern) => pattern)

			// 所有 AND 模式都必须匹配成功
			return andPatterns.every((pattern) => {
				switch (method) {
					case 'regex':
						return this.matchRegex(text, pattern, flags)
					case 'include':
						return this.matchInclude(text, pattern)
					case 'exclude':
						return this.matchExclude(text, pattern)
					default:
						return false
				}
			})
		})
	}

	/**
	 * 配置匹配主函数
	 * @param category2 产品子分类
	 * @param configOptions 配置选项
	 * @returns 匹配结果数组
	 */
	async matchConfig(category2: string, configOptions: ConfigOptions): Promise<MatchResult[]> {
		// 1. 获取产品列表
		const thirdCategories = Object.keys(configOptions)
		const productList = await this.productRepository
			.createQueryBuilder('p')
			.leftJoinAndSelect('p.productPriceRule', 'ppr', 'ppr.isActive = 1')
			.leftJoinAndSelect('p.productPrices', 'price', 'price.tier = "List"')
			.where('p.subCategory = :subCategory', { subCategory: category2 })
			.andWhere('p.thirdCategory IN (:...thirdCategories)', { thirdCategories })
			.andWhere('p.productPriceRuleId IS NOT NULL')
			.getMany()

		// 2. 对configOptions进行匹配
		const results: MatchResult[] = []

		for (const [category, content] of Object.entries(configOptions)) {
			// 获取对应资源的产品
			const products = productList.filter((p) => p.thirdCategory?.toLowerCase() === category.toLowerCase())

			if (products.length === 0) {
				results.push({
					category,
					qty: 1,
					content,
					unitPrice: 0,
					totalPrice: 0,
				})
				continue
			}

			// 检查content是否包含多个值（用逗号分隔）
			const values = content
				.split(',')
				.map((v) => v.trim())
				.filter((v) => v)

			if (values.length > 1) {
				// 多个值的情况，分别匹配每个值
				for (const value of values) {
					const { quantity, actualValue } = this.extractQuantityAndValue(value)

					const matchProduct = products.find((p) => {
						if (!p.productPriceRule || !p.productPriceRule.matchMethod || !p.productPriceRule.matchPattern) {
							return false
						}
						return this.matchComplex(actualValue, p.productPriceRule.matchMethod, p.productPriceRule.matchPattern)
					})

					const price = matchProduct?.productPrices[0]?.tierPrice ?? 0
					const unitPrice = price !== 0 && price !== undefined ? price : 0
					const totalPrice = price !== 0 && price !== undefined && !isNaN(unitPrice) ? unitPrice * quantity : 0

					results.push({
						category,
						qty: quantity,
						content: value,
						unitPrice,
						totalPrice,
					})
				}
			} else {
				// 单个值的情况
				const { quantity, actualValue } = this.extractQuantityAndValue(content)

				const matchProduct = products.find((p) => {
					if (!p.productPriceRule || !p.productPriceRule.matchMethod || !p.productPriceRule.matchPattern) {
						return false
					}
					return this.matchComplex(actualValue, p.productPriceRule.matchMethod, p.productPriceRule.matchPattern)
				})

				const price = matchProduct?.productPrices[0]?.tierPrice ?? 0
				const unitPrice = price !== 0 && price !== undefined ? price : 0
				const totalPrice = price !== 0 && price !== undefined && !isNaN(unitPrice) ? unitPrice * quantity : 0

				results.push({
					category,
					qty: quantity,
					content,
					unitPrice,
					totalPrice,
				})
			}
		}

		return results
	}

	/**
	 * 格式化匹配结果为字符串
	 * @param results 匹配结果数组
	 * @returns 格式化后的字符串
	 */
	formatResults(results: MatchResult[]): string {
		const lines: string[] = []
		let totalPrice = 0

		// 按category分组
		const groupedResults = results.reduce((acc, result) => {
			if (!acc[result.category]) {
				acc[result.category] = []
			}
			acc[result.category].push(result)
			return acc
		}, {} as Record<string, MatchResult[]>)

		// 格式化每个category
		for (const [category, categoryResults] of Object.entries(groupedResults)) {
			if (categoryResults.length === 1) {
				// 单个结果
				const result = categoryResults[0]
				const priceDisplay = result.totalPrice === 0 ? 'NA' : `$${result.totalPrice.toFixed(2).replace(/\.00$/, '')}`

				lines.push(`- ${category}: ${result.content}, Price: ${priceDisplay}`)

				if (result.totalPrice !== 0) {
					totalPrice += result.totalPrice
				}
			} else {
				// 多个结果
				lines.push(`- ${category}:`)
				categoryResults.forEach((result) => {
					const priceDisplay = result.totalPrice === 0 ? 'NA' : `$${result.totalPrice.toFixed(2).replace(/\.00$/, '')}`
					lines.push(`   - ${result.content}, Price: ${priceDisplay}`)
				})

				categoryResults.forEach((result) => {
					if (result.totalPrice !== 0) {
						totalPrice += result.totalPrice
					}
				})
			}
		}

		// 添加总价
		lines.push(`- Total Price: $${totalPrice.toFixed(2).replace(/\.00$/, '')}`)

		return lines.join('\n')
	}
}

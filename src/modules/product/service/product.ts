import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { Brackets, In, QueryRunner, Repository } from 'typeorm'
import { chain, difference, isNaN } from 'lodash'

import { Utils } from '/@/comm/utils'
import { PageResult, selectBuilder } from '/@/utils/orm'
import { isExist, isArray, isEmpty } from '/@/utils/types'
import { kvpFormatter } from '/@/utils/KVPFormatter'

import { TecServerEntity } from '/$/tec/entity/server'
import { ConfCustomNoService, Count } from '/$/conf/service/common'

import { ProductAddonMap } from '../entity/product_addon_map'
import { productCommonQueryOp } from '../controller/admin/product'

import { ProductOptionEntity } from '../entity/option'
import { ProductOptionService } from './option'
import { ProductPriceEntity } from '../entity/product_price'
import { ProductEntity } from '../entity/product'
import { IpsubnetProduct } from '/$/tec/entity/ipsubnet_product_map'
import { TecIpaddEntity } from '../../tec/entity/ipadd'
/**
 * 描述
 */
@Provide()
export class ProductService extends BaseService {
	@InjectEntityModel(ProductEntity)
	productRepository: Repository<ProductEntity>

	@InjectEntityModel(ProductPriceEntity)
	productPriceRepository: Repository<ProductPriceEntity>

	@InjectEntityModel(ProductAddonMap)
	productAddonMapRepository: Repository<ProductAddonMap>

	@InjectEntityModel(ProductOptionEntity)
	productOptionRepository: Repository<ProductOptionEntity>

	@Inject()
	productOptionService: ProductOptionService

	@Inject()
	confCustomNoService: ConfCustomNoService

	@Inject()
	ctx

	@Inject()
	utils: Utils

	@CoolTransaction()
	async add(params, runner?: QueryRunner) {
		const { username } = this.ctx.admin
		const { prices, option, availableProductId, ...restParams } = params
		const basePrice = restParams.basePrice

		restParams.updateBy = username
		restParams.createBy = username

		const newProduct = await this.confCustomNoService.handler(restParams, Count.PRODUCT, ProductEntity, runner)
		const productId = newProduct.id

		if (isExist(option)) await this.productOptionService.add({ ...option, productId }, runner)

		if (isArray(prices) && isExist(prices)) {
			await runner.manager.insert(
				ProductPriceEntity,
				prices.map((price) => ({ ...price, productId, basePrice }))
			)
		}

		if (isArray(availableProductId) && isExist(availableProductId)) {
			await runner.manager.insert(
				ProductAddonMap,
				availableProductId.map((parentProductId) => ({ productId: parentProductId, addonId: productId }))
			)
		}

		return newProduct
	}

	/**
	 * 更新产品价格
	 */
	async updatePrices(params, runner?: QueryRunner) {
		const { id, basePrice } = params
		const prices = await runner.manager.find(ProductPriceEntity, { where: { productId: id } })
		if (isEmpty(prices)) return
		const promises = prices.map((price) => {
			let tierPrice = +basePrice * +price.discountRate
			if (isNaN(tierPrice)) tierPrice = 0
			return runner.manager.upsert(
				ProductPriceEntity,
				{
					id: price.id,
					basePrice,
					tierPrice: +tierPrice.toFixed(2),
				},
				['id']
			)
		})
		return Promise.all(promises)
	}

	@CoolTransaction()
	async update(params, runner?: QueryRunner) {
		const { username } = this.ctx.admin
		const { prices, option, availableProductId, ...restParams } = params
		const basePrice = restParams.basePrice
		const productId = restParams.id

		restParams.updateBy = username

		if (isArray(availableProductId)) {
			const originAvailableProducts = await runner.manager.find(ProductAddonMap, { where: { addonId: productId } })

			const deleteAvailableProductIds = difference(
				originAvailableProducts.map(({ productId }) => productId),
				availableProductId
			)

			if (deleteAvailableProductIds.length > 0) {
				await runner.manager.delete(ProductAddonMap, { productId: In(deleteAvailableProductIds), addonId: productId })
			}

			await runner.manager.upsert(
				ProductAddonMap,
				availableProductId.map((parentProductId) => ({ productId: parentProductId, addonId: productId })),
				['productId', 'addonId']
			)
		}

		await runner.manager.update(ProductEntity, productId, restParams)

		if (isExist(option)) {
			option.productId = productId
			await this.productOptionService.update(option, runner)
		}

		if (isArray(prices) && isExist(prices)) {
			const originalPrices = await runner.manager.find(ProductPriceEntity, { where: { productId } })
			const { toDeleteIds: deletePriceIds } = this.utils.getDifferences(prices, originalPrices)
			if (deletePriceIds.length > 0) await runner.manager.delete(ProductPriceEntity, deletePriceIds)
			await Promise.all(
				prices.map(async (price) => {
					return runner.manager.upsert(ProductPriceEntity, { ...price, productId, basePrice }, ['id'])
				})
			)
		}

		if (basePrice && isEmpty(prices)) {
			await this.updatePrices(params, runner)
		}
	}

	async page(query: any, option = productCommonQueryOp): Promise<PageResult> {
		const { notRelatedAddonId, ...restQuery } = query
		const builder = this.productRepository.createQueryBuilder('a')

		if (notRelatedAddonId) {
			const addonIds = Array.isArray(notRelatedAddonId) ? notRelatedAddonId : [notRelatedAddonId]
			const relatedProductIds = await this.productAddonMapRepository
				.createQueryBuilder('map')
				.select('DISTINCT map.productId')
				.where('map.addonId IN (:...addonIds)', { addonIds })
				.getRawMany()
				.then((results) => results.map((r) => r.productId))

			if (relatedProductIds.length > 0) {
				builder.andWhere('a.id NOT IN (:...relatedProductIds)', { relatedProductIds })
			}
		}

		return selectBuilder.page(restQuery, option, builder)
	}

	@CoolTransaction()
	async delete(ids: number[] | string, runner?: QueryRunner): Promise<void> {
		ids = isArray(ids) ? ids : [+ids]
		await runner.manager.delete(ProductOptionEntity, { productId: In(ids) })
		await runner.manager.delete(ProductPriceEntity, { productId: In(ids) })
		await runner.manager.delete(ProductEntity, ids)
	}

	async getServerAvailableTotal(param: any) {
		const { list, pagination } = await this.page(param)

		const grades = chain(list)
			.map((item) => item.relatedServerGrades?.split(',').map((grade) => grade.trim()))
			.flatten()
			.filter(Boolean)
			.value()

		const servers = await this.getOrmManager().find(TecServerEntity, {
			where: { grade: In(grades) },
		})

		const calculateTotals = (product: any, status?: string) => {
			return servers.filter((server) => {
				const isGradeIncluded = product.relatedServerGrades
					?.split(',')
					.map((grade) => grade.trim())
					.includes(server.grade)
				return status ? isGradeIncluded && server.status === status : isGradeIncluded
			}).length
		}

		return {
			pagination,
			list: list.map((product) => ({
				...product,
				total: calculateTotals(product),
				availableTotal: calculateTotals(product, 'Available'),
			})),
		}
	}

	async getIpAddressAvailableTotal(param: any) {
		const productBuilder = this.getOrmManager()
			.createQueryBuilder(ProductEntity, 'p')
			.leftJoin(IpsubnetProduct, 'ip', 'p.id = ip.productId')
			.leftJoin(TecIpaddEntity, 'i', 'ip.ipsubnetId = i.ipsubnetId')
			.groupBy('p.id')
			.orderBy('CASE WHEN availableTotal = 0 THEN 999999 ELSE availableTotal END', 'ASC')

		return selectBuilder.page(
			param,
			{
				select: ['p.*', 'COUNT(i.id) total', 'COUNT(CASE WHEN i.status = "Available" THEN i.id END) availableTotal'],
				fieldEq: [
					{ column: 'p.id', requestParam: 'id' },
					{ column: 'p.type', requestParam: 'type' },
					{ column: 'p.category', requestParam: 'category' },
					{ column: 'p.status', requestParam: 'status' },
				],
				findInSet: [{ column: 'p.location', requestParam: 'location', useAll: true }],
				keyWordLikeFields: ['p.name', 'p.detail'],
			},
			productBuilder
		)
	}

	async getProductDetail(id: number) {
		const product = await this.productRepository.findOne(id)
		if (!product) throw new Error('Product Not Found.')

		const option = await this.productOptionRepository.findOne({ where: { productId: id, status: 'Active' } })
		const prices = await this.productPriceRepository.find({ where: { productId: id } })
		const availableProducts = await this.productAddonMapRepository.find({
			select: ['productId'],
			where: { addonId: id },
		})
		return {
			...product,
			option: option || {},
			prices,
			availableProductId: availableProducts.map(({ productId }) => productId),
		}
	}

	async getProductContent(id: number) {
		const productOption = await this.productOptionRepository.findOne({
			where: { productId: id, status: 'Active' },
		})

		if (isEmpty(productOption) || isEmpty(productOption.optionValues)) return ''
		return kvpFormatter.format(productOption.optionValues, '\n')
	}

	async changeStatus(id, status) {
		const currentOption = await this.productOptionRepository.findOne(id)

		if (!currentOption) return

		const options = await this.productOptionRepository
			.createQueryBuilder()
			.select(['id'])
			.where('id != :id', { id })
			.andWhere('productId != :pid', { pid: currentOption.productId })
			.getRawMany()

		const terminatedOptions = options.map(async (i) => {
			return this.productOptionRepository.save({
				id: i.id,
				status: 'Terminated',
			})
		})

		await Promise.all(terminatedOptions)
		await this.productOptionRepository.save({ id, status })
	}

	async pageByCategoryAndLocation(params) {
		const { keyWord, ...param } = params

		let locationCondition = '1=1'
		if (param.location) {
			const locations = isArray(param.location) ? param.location : param.location.split(',').map((loc) => loc.trim())
			locationCondition = locations.map((loc) => `FIND_IN_SET('${loc}', a.location)`).join(' OR ')
		}

		let categoryCondition = '1=1'

		if (params.type || params.category) {
			let category = params.type ?? params.category
			categoryCondition = `FIND_IN_SET(a.category, '${category}')`
		}

		const data = await this.productRepository
			?.createQueryBuilder('a')
			.select('a.*')
			.andWhere(categoryCondition)
			.andWhere(
				new Brackets((qb) => {
					qb.where('a.location = "ALL"').orWhere(locationCondition)
				})
			)
			.andWhere(keyWord ? `a.name LIKE "%${keyWord}%"` : '1=1')
			.orderBy(params.sortKey ?? { 'a.name': 'ASC' })
			.getRawMany()

		return data
	}

	async getCategoryCascader(target: 'Product' | 'ProductPlan' = 'Product') {
		const repository = target === 'Product' ? this.productRepository : this.getOrmManager().getRepository('ProductPlan')

		const products = await repository
			.createQueryBuilder('p')
			.select('p.category, p.subCategory, p.thirdCategory')
			.where('p.category IS NOT NULL AND p.category != ""')
			.orderBy('p.category, p.subCategory, p.thirdCategory')
			.getRawMany()

		const categories = new Map()
		const subCategories = new Map()
		const thirdCategories = new Map()

		// 构建分类树
		products.forEach((product) => {
			const { category, subCategory, thirdCategory } = product

			if (category) {
				if (!categories.has(category)) {
					categories.set(category, {
						value: category,
						label: category,
						children: [],
					})
				}

				if (subCategory) {
					const subCategoryKey = `${category}-${subCategory}`
					if (!subCategories.has(subCategoryKey)) {
						subCategories.set(subCategoryKey, {
							value: subCategory,
							label: subCategory,
							children: [],
						})
						// 检查是否已经添加过这个子分类
						const categoryObj = categories.get(category)
						if (!categoryObj.children.some((child) => child.value === subCategory)) {
							categoryObj.children.push(subCategories.get(subCategoryKey))
						}
					}

					if (thirdCategory && thirdCategory !== '') {
						const thirdCategoryKey = `${subCategoryKey}-${thirdCategory}`
						if (!thirdCategories.has(thirdCategoryKey)) {
							thirdCategories.set(thirdCategoryKey, {
								value: thirdCategory,
								label: thirdCategory,
							})
							// 检查是否已经添加过这个三级分类
							const subCategoryObj = subCategories.get(subCategoryKey)
							if (!subCategoryObj.children.some((child) => child.value === thirdCategory)) {
								subCategoryObj.children.push(thirdCategories.get(thirdCategoryKey))
							}
						}
					}
				}
			}
		})

		return Array.from(categories.values())
	}
}

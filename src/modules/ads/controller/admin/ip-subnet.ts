import { Body, Get, Post, Provide, Query } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'

import { adsIpSubnet } from '/@/vendor/ads'
/**
 * 描述
 */
@Provide()
@CoolController()
export class AdminipSubnetController extends BaseController {
	@Post('/add')
	async handleAdd(@Body() body) {
		return await adsIpSubnet.add(body)
	}

	@Post('/page')
	async handlePage(@Body() body) {
		return await adsIpSubnet.page(body)
	}

	@Post('/delete')
	async handleDelete(@Body() body) {
		if (body.ids?.length > 0) return await adsIpSubnet.delete(body.ids)
		throw new Error('参数缺失')
	}

	@Post('/update')
	async handleUpdate(@Body() body) {
		return await adsIpSubnet.update(body)
	}

	@Post('/ops/update/selected')
	async handleBatchUpdate(@Body() body) {
		return await adsIpSubnet.batchUpdate(body)
	}

	@Get('/info')
	async handleInfo(@Query('id') id) {
		return await adsIpSubnet.info(id)
	}

	@Post('/resynchronize')
	async resynchronize() {
		return adsIpSubnet.resynchronize()
	}
}

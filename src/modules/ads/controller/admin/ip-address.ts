import { Body, Get, Post, Provide, Query } from '@midwayjs/decorator'
import { CoolController, BaseController } from '@cool-midway/core'

import { adsIpAddress } from '/@/vendor/ads'
/**
 * 描述
 */
@Provide()
@CoolController()
export class AdminipAddressController extends BaseController {
	@Post('/add')
	async handleAdd(@Body() body) {
		return await adsIpAddress.add(body)
	}

	@Post('/page')
	async handlePage(@Body() body) {
		return await adsIpAddress.page(body)
	}

	@Post('/delete')
	async handleDelete(@Body() body) {
		if (body.ids?.length > 0) return await adsIpAddress.delete(body.ids)
		throw new Error('参数缺失')
	}

	@Post('/update')
	async handleUpdate(@Body() body) {
		return await adsIpAddress.update(body)
	}

	@Post('/ops/update/selected')
	async handleBatchUpdate(@Body() body) {
		return await adsIpAddress.batchUpdate(body)
	}

	@Get('/info')
	async handleInfo(@Query('id') id) {
		return await adsIpAddress.info(id)
	}

	@Post('/resynchronize')
	async resynchronize() {
		return adsIpAddress.resynchronize()
	}
}

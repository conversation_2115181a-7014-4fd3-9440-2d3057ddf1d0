import { EntityModel } from '@midwayjs/orm'
import { BaseEntity } from '@cool-midway/core'
import { Column } from 'typeorm'
import { ManyToOne } from 'typeorm'
import { BaseSysUserEntity } from '../../base/entity/sys/user'
import { TecServerEntity } from './server'
import { TecIpaddEntity } from './ipadd'

/**
 * 描述
 */
@EntityModel('op_tec_provision_log')
export class TecProvisionLogEntity extends BaseEntity {
	@Column({ type: 'int', nullable: true })
	serviceId: number

	@Column({ nullable: true })
	orderId: number

	@ManyToOne(() => BaseSysUserEntity, { nullable: true })
	user: BaseSysUserEntity
	@Column({ nullable: true })
	userId: number

	@ManyToOne(() => TecServerEntity, { nullable: true, onDelete: 'SET NULL' })
	server: TecServerEntity
	@Column({ nullable: true })
	serverId: number

	@ManyToOne(() => TecIpaddEntity, { nullable: true })
	ipadd: TecIpaddEntity
	@Column({ nullable: true })
	ipaddId: number

	@Column({ default: '' })
	actionType: string
}

import { EntityModel } from '@midwayjs/orm'
import { BaseEntity } from '@cool-midway/core'
import { Column, ManyToOne, OneToMany } from 'typeorm'

import { CustomerEntity } from '../../customer/entity/customer'
import { VendorEntity } from '../../vendor/entity/vendor'
import { ProductEntity } from '../../product/entity/product'
import { ServiceEntity } from '/$/service/entity/service'
import { IpsubnetProduct } from './ipsubnet_product_map'
/**
 * 描述
 */
@EntityModel('op_tec_ipsubnet')
export class TecIpsubnetEntity extends BaseEntity {
	@OneToMany(() => IpsubnetProduct, (ipsubnetProduct) => ipsubnetProduct.ipsubnet)
	ipsubnetProducts: IpsubnetProduct[]

	@ManyToOne(() => TecIpsubnetEntity, { nullable: true })
	parentIpSubnet: TecIpsubnetEntity
	@Column({ comment: '父级ID', nullable: true })
	parentIpSubnetId: number

	@Column({ type: 'bigint', comment: 'IP段，以整数形式存储' })
	subnetNo: number

	@Column({ comment: 'IP段' })
	subnetStr: string

	@ManyToOne(() => CustomerEntity, {
		onDelete: 'RESTRICT',
		onUpdate: 'RESTRICT',
		nullable: true,
	})
	customer: CustomerEntity
	@Column({ comment: '客户ID', nullable: true })
	customerId: number

	@ManyToOne(() => VendorEntity, {
		onDelete: 'RESTRICT',
		onUpdate: 'RESTRICT',
		nullable: true,
	})
	vendor: VendorEntity
	@Column({ comment: '供应商ID', nullable: true })
	vendorId: number

	@ManyToOne(() => ServiceEntity, { nullable: true })
	relatedService: ServiceEntity
	@Column({ comment: '关联服务ID', nullable: true })
	relatedServiceId: number

	@ManyToOne(() => ProductEntity, {
		onDelete: 'RESTRICT',
		onUpdate: 'RESTRICT',
		nullable: true,
	})
	product: ProductEntity
	@Column({ comment: '产品ID', nullable: true })
	productId: number

	@Column({ comment: '掩码长度', default: 0 })
	prefixLength: number

	@Column({ comment: '机房', default: '' })
	location: string

	@Column({ comment: '类别', default: '' })
	category: string

	@Column({ comment: '标签，多个以逗号分割', default: '' })
	tags: string

	@Column({ comment: '状态（例如：使用中、空闲、保留等）', default: '' })
	status: string

	@Column({ comment: '所有者', default: '' })
	owner: string

	@Column({ comment: '维护者', default: '' })
	maintainer: string

	@Column({ comment: '通告ASN', default: '' })
	originAsn: string

	@Column({ comment: '注册机构', default: '' })
	icann: string

	@Column({ comment: '宣告上游ISP列表', default: '' })
	advertisedUpstreams: string

	@Column({ type: 'varchar', comment: '宣告路由器', default: '' })
	advertisedOn: Date

	@Column({ comment: '宣告communities，多个以逗号分割', default: '' })
	advertisedCommunities: string

	@Column({ comment: '网关设备', default: '' })
	gateway: string

	@Column({ comment: 'VRF 名称或标识', default: '' })
	vrf: string

	@Column({ type: 'varchar', length: 2550, default: '', comment: '备注信息' })
	notes: string

	@Column({ comment: '创建人', default: '' })
	createBy: string

	@Column({ comment: '更新人', default: '' })
	updateBy: string
}

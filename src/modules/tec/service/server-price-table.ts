import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { In, Repository } from 'typeorm'
import { chain, isNumber } from 'lodash'
import { TecServerEntity } from '../entity/server'
import { ProductEntity } from '/$/product/entity/product'
import { ConfigMatcherService, MatchResult, ConfigOptions } from '/$/product/service/config-matcher'
import { AssetEntity } from '/$/asset/entity/asset'

/**
 * 描述
 */
@Provide()
export class ServerPriceTableService extends BaseService {
	@InjectEntityModel(TecServerEntity)
	tecServerRepository: Repository<TecServerEntity>

	@InjectEntityModel(ProductEntity)
	productRepository: Repository<ProductEntity>

	@InjectEntityModel(AssetEntity)
	assetRepository: Repository<AssetEntity>

	@Inject()
	configMatcherService: ConfigMatcherService

	/**
	 * 服务器(OPS)报价表
	 */
	async serverOpsPriceTable(serverId: number): Promise<{
		configOptions: ConfigOptions
		matchResults: MatchResult[]
		formatted: string
	}> {
		// 1. 格式化服务器详情，返回configOptions格式
		const configOptions = await this.serverOpsDetailFormat(serverId)

		// 2. 使用ConfigMatcherService进行匹配
		const matchResults = await this.configMatcherService.matchConfig('Baremetal', configOptions)

		// 3. 格式化匹配结果
		const formatted = this.configMatcherService.formatResults(matchResults)

		// 4. 更新服务器总价格字段
		const basePrice = matchResults.reduce((acc, result) => acc + result.totalPrice, 0)
		await this.tecServerRepository.update(serverId, {
			basePrice: isNumber(basePrice) ? basePrice : 0,
		})

		return {
			configOptions,
			matchResults,
			formatted,
		}
	}

	/**
	 * 获取OPS服务器
	 */
	async serverOpsDetailFormat(id: number) {
		const server = await this.tecServerRepository.findOne(id)

		const assetIds = chain([server.cpuAssetId, server.memoryAssetId, server.storagesAssetId])
			.filter(Boolean)
			.map((assetIdStr) => assetIdStr.split(',').map(Number))
			.flatten()
			.value()

		let assets: AssetEntity[] = []
		if (assetIds.length > 0) assets = await this.assetRepository.find({ where: { id: In(assetIds) } })

		if (server.cpuAssetId) {
			const cpuAssetIds = server.cpuAssetId.split(',').map(Number)
			const cpuAssets = assets.filter((asset) => cpuAssetIds.includes(asset.id))
			server.cpuModel = this.formatAssetGroups(cpuAssets)
		} else {
			server.cpuModel = `${server.cpuNo} * ${server.cpuModel}`
		}

		if (server.memoryAssetId) {
			const memoryAssetIds = server.memoryAssetId.split(',').map(Number)
			const memoryAssets = assets.filter((asset) => memoryAssetIds.includes(asset.id))
			server.memoryModel = this.formatAssetGroups(memoryAssets)
		} else {
			server.memoryModel = `${server.memoryInstalledNo} * ${server.memoryModel}`
		}

		if (server.storagesAssetId) {
			const storageAssetIds = server.storagesAssetId.split(',').map(Number)
			const storageAssets = assets.filter((asset) => storageAssetIds.includes(asset.id))
			server.hdds = this.formatAssetGroups(storageAssets)
		} else {
			server.hdds = `${server.hdds}`
		}

		return {
			CPU: server.cpuModel,
			Memory: server.memoryModel,
			Storage: server.hdds,
			NIC: server.nics,
			RAID: server.raidModel,
		}
	}

	/**
	 * 通用资产分组格式化函数
	 * @param assets 资产列表
	 * @returns 格式化后的字符串
	 */
	private formatAssetGroups(assets: AssetEntity[]): string {
		const modelGroups = chain(assets)
			.groupBy('model')
			.map((items, model) => {
				const count = items.length
				return count === 1 ? model : `${count} * ${model}`
			})
			.value()

		return modelGroups.join(',')
	}
}

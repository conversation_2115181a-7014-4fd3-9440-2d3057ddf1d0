import { Provide } from '@midwayjs/decorator'
import { BaseService } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { In, Repository } from 'typeorm'
import { TecIpaddEntity } from '../entity/ipadd'
import { TecIpsubnetEntity } from '../entity/ipsubnet'
import { ServiceEntity } from '../../service/entity/service'
import { CustomerEntity } from '../../customer/entity/customer'
import { ProductEntity } from '../../product/entity/product'
import dayjs = require('dayjs')
import { isEmpty, uniq } from 'lodash'
import { isExist } from '/@/utils/types'

/**
 * 描述
 */
@Provide()
export class IpExtraInfoService extends BaseService {
	@InjectEntityModel(TecIpaddEntity)
	ipAddressRepository: Repository<TecIpaddEntity>

	@InjectEntityModel(TecIpsubnetEntity)
	ipSubnetRepository: Repository<TecIpsubnetEntity>

	async getIpAddressListByUpdatedAfter(timestamp: string) {
		const formattedTimestamp = this.formatTimestamp(timestamp)
		const result = await this.ipAddressRepository
			.createQueryBuilder('a')
			.select('a.ipStr ipAddress')
			.where('a.updateTime > :lastUpdateTime', {
				lastUpdateTime: formattedTimestamp,
			})
			.getRawMany()

		const ipAddressList = result.map((i) => i.ipAddress)
		if (isEmpty(ipAddressList)) return []

		return this.getIpAddressList(ipAddressList)
	}

	async getIpSubnetListByUpdatedAfter(timestamp: string) {
		const formattedTimestamp = this.formatTimestamp(timestamp)
		const result = await this.ipSubnetRepository
			.createQueryBuilder('a')
			.select('a.subnetStr ipSubnet')
			.where('a.updateTime > :lastUpdateTime', {
				lastUpdateTime: formattedTimestamp,
			})
			.getRawMany()

		const ipSubnetList = result.map((i) => i.ipSubnet)
		if (isEmpty(ipSubnetList)) return []

		return this.getIpSubnetList(ipSubnetList)
	}

	async getIpAddressList(ipAddressList: string[]) {
		ipAddressList = uniq(ipAddressList)

		const ipAddressResult = await this.ipAddressRepository
			.createQueryBuilder('i')
			.select([
				'i.ipStr as ipAddress',
				'i.ipNo as ipAddressNum',
				'i.ipsubnetId as ipSubnetId',
				'i.usageDesc description',
				's.serviceNo serviceNo',
				's.serviceNo2 serviceNo2',
				'c.customerNo customerNo',
				'sub.subnetStr ipSubnetStr',
				'sub.prefixLength ipSubnetPrefixLength',
			])
			.leftJoin(ServiceEntity, 's', 's.id = i.serviceId')
			.leftJoin(CustomerEntity, 'c', 'c.id = s.customerId')
			.leftJoin(TecIpsubnetEntity, 'sub', 'sub.id = i.ipsubnetId')
			.where('i.ipStr IN (:...ipAddressList)', { ipAddressList })
			.getRawMany()

		const ipAddressResultMap = new Map(
			ipAddressResult.map((item) => {
				return [item.ipAddress, item]
			})
		)

		return ipAddressList
			.filter((ipAddress) => ipAddressResultMap.has(ipAddress))
			.map((ipAddress) => {
				const ipAddressItem = ipAddressResultMap.get(ipAddress)
				if (!ipAddressItem) return null
				return {
					ipAddressNum: ipAddressItem.ipAddressNum,
					ipAddress: ipAddressItem.ipAddress,
					subnet: ipAddressItem.ipSubnetStr,
					subnetPrefixLength: ipAddressItem.ipSubnetPrefixLength || '',
					serviceNo: ipAddressItem.serviceNo || '',
					serviceNo2: ipAddressItem.serviceNo2 || '',
					customerNo: ipAddressItem.customerNo || '',
					description: ipAddressItem.description || '',
					opsIpSubnetId: ipAddressItem.ipSubnetId,
				}
			})
			.filter((item) => isExist(item))
	}

	async getIpSubnetList(list: string[]) {
		const result = await this.ipSubnetRepository
			.createQueryBuilder('sub')
			.select([
				'sub.subnetStr ipSubnet',
				'sub.id ipSubnetId',
				'sub.prefixLength ipSubnetPrefixLength',
				'sub.subnetNo ipSubnetNum',
				'sub.location location',
				'sub.originAsn originAsn',
				'sub.owner owner',
				'sub.notes description',
				's.serviceNo serviceNo',
				's.serviceNo2 serviceNo2',
				'sc.customerNo serviceCustomerNo',
				'c.customerNo customerNo',
				'p.name productName',
			])
			.leftJoin(ServiceEntity, 's', 's.id = sub.relatedServiceId')
			.leftJoin(CustomerEntity, 'sc', 'sc.id = s.customerId')
			.leftJoin(CustomerEntity, 'c', 'c.id = sub.customerId')
			.leftJoin(ProductEntity, 'p', 'p.id = sub.productId')
			.where('sub.subnetStr IN (:...list)', { list })
			.getRawMany()

		return result.map((item) => ({
			subnetNum: item.ipSubnetNum,
			prefixLength: item.ipSubnetPrefixLength || '',
			subnet: item.ipSubnet,
			customerNo: item.serviceCustomerNo || item.customerNo || '',
			serviceNo: item.serviceNo || '',
			serviceNo2: item.serviceNo2 || '',
			opsIpSubnetId: item.ipSubnetId,
			location: item.location || '',
			originAsn: item.originAsn || '',
			productName: item.productName || '',
			owner: item.owner || '',
			description: item.description || '',
		}))
	}

	formatTimestamp(timestamp: string) {
		return dayjs(+timestamp).format('YYYY-MM-DD HH:mm:ss SSS')
	}
}

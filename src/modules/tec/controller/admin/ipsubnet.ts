import { Body, Get, Inject, Post, Provide } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'
import { TecIpsubnetEntity } from '/$/tec/entity/ipsubnet'
import { CustomerEntity } from '/$/customer/entity/customer'
import { VendorEntity } from '/$/vendor/entity/vendor'
import { ProductEntity } from '/$/product/entity/product'
import { TecIpsubnetService } from '/$/tec/service/ipsubnet'
import { ServiceEntity } from '/$/service/entity/service'
import { isEmpty } from '/@/utils/types'
import { Utils } from '/@/comm/utils'
import { TecIpsubnetSplitMergeService } from '/$/tec/service/ipsubnet-split-merge'
/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: TecIpsubnetEntity,
	service: TecIpsubnetService,

	pageQueryOp: {
		keyWordLikeFields: ['a.subnetStr', 'a.tags', 'a.maintainer', 'a.originAsn', 'a.icann', 'a.advertisedUpstreams'],
		fieldEq: [
			{ column: 'a.customerId', requestParam: 'customerId' },
			{ column: 'a.relatedServiceId', requestParam: 'relatedServiceId' },
			{ column: 'a.vendorId', requestParam: 'vendorId' },
			{ column: 'a.location', requestParam: 'location' },
			{ column: 'a.productId', requestParam: 'productId' },
			{ column: 'a.ipsubnetId', requestParam: 'ipsubnetId' },
			{ column: 'a.category', requestParam: 'category' },
			{ column: 'a.status', requestParam: 'status' },
			{ column: 'a.owner', requestParam: 'owner' },
			{ column: 'a.maintainer', requestParam: 'maintainer' },
		],
		select: [
			'a.*',
			'b.customerNo as customerNo',
			'c.vendorNo as vendorNo',
			'c.alias as vendorName',
			'd.name as productName',
			'd.productNo as productNo',
			'e.serviceNo as serviceNo',
			'(SELECT COUNT(*) + 0 FROM op_tec_ipsubnet WHERE parentIpSubnetId = a.id) AS childrenIpSubnetCount',
		],
		join: [
			{
				type: 'leftJoin',
				entity: CustomerEntity,
				alias: 'b',
				condition: 'a.customerId = b.id',
			},
			{
				type: 'leftJoin',
				entity: VendorEntity,
				alias: 'c',
				condition: 'a.vendorId = c.id',
			},
			{
				type: 'leftJoin',
				entity: ProductEntity,
				alias: 'd',
				condition: 'a.productId = d.id',
			},
			{
				type: 'leftJoin',
				entity: ServiceEntity,
				alias: 'e',
				condition: 'a.relatedServiceId = e.id',
			},
		],
	},
})
export class TecIpsubnetController extends BaseController {
	@Inject()
	tecIpsubnetService: TecIpsubnetService

	@Inject()
	tecIpsubnetSplitMergeService: TecIpsubnetSplitMergeService

	@Inject()
	utils: Utils

	@Post('/ops/update/selected')
	async updateSelected(@Body() body) {
		return this.ok(await this.tecIpsubnetService.updateSelected(body))
	}

	@Post('/sync/ads')
	async handleManualSync(@Body('ids') ids: Array<number>) {
		if (isEmpty(ids)) throw new Error('参数缺失')
		return this.tecIpsubnetService.handleManualSync(ids)
	}

	@Get('/owner')
	async getOwnerList() {
		return this.tecIpsubnetService.getColumnList('owner')
	}

	@Post('/ops/select/distinct')
	async getSelectDistinct(@Body('column') column: string, @Body() body: Record<string, any>) {
		return this.utils.getSelectDistinct({
			table: 'op_tec_ipsubnet',
			column,
			query: body,
			optionalColumns: ['category', 'gateway', 'vrf'],
		})
	}

	@Post('/ops/list/ipAddressAvailableTotal', { summary: '获取ip地址可用总数' })
	async getIpAddressAvailableTotal(@Body() body) {
		return this.ok(await this.tecIpsubnetService.getIpAddressAvailableTotal(body))
	}

	// 获取分类ip地址可用总数
	@Post('/ops/list/categoryIpAddressAvailableTotal', { summary: '获取分类ip地址可用总数' })
	async getCategoryIpAddressAvailableTotal(@Body() body) {
		return this.ok(await this.tecIpsubnetService.getCategoryIpAddressAvailableTotal(body))
	}

	@Post('/split', { summary: '拆分IP子网' })
	async splitSubnet(@Body('id') id: number, @Body('targetPrefixLength') targetPrefixLength: number) {
		return this.ok(await this.tecIpsubnetSplitMergeService.subnetSplit(id, targetPrefixLength))
	}

	@Post('/merge', { summary: '合并IP子网' })
	async mergeSubnets(@Body('ids') ids: number[]) {
		return this.ok(await this.tecIpsubnetSplitMergeService.subnetMerge(ids))
	}
}

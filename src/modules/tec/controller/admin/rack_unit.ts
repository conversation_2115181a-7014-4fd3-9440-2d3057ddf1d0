import { Provide } from '@midwayjs/decorator'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core'

import { TecRackUnitEntity } from '/$/tec/entity/rack_unit'
import { TecServerEntity } from '/$/tec/entity/server'
import { AssetEntity } from '/$/asset/entity/asset'
import { ServiceEntity } from '/$/service/entity/service'

import { TecRackUnitService } from '/$/tec/service/rack_unit'
/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: TecRackUnitEntity,
	service: TecRackUnitService,
	pageQueryOp: {
		select: [
			'a.*',
			'b.serverNo serverNo',
			'b.label serverLabel',
			'b.status serverStatus',
			'b.brand serverBrand',
			'b.cpuNo cpuNo',
			'b.cpuModel cpuModel',
			'b.memorySize memorySize',
			'b.hdds hdds',
			'b.ipmiIp ipmiIp',
			'b.location serverLocation',
			'b.rack serverRack',
			'b.power serverPower',
			'c.assetNo assetNo',
			'c.label assetLabel',
			'c.status assetStatus',
			'c.brand assetBrand',
			'c.assetType assetType',
			'c.location assetLocation',
			'c.rack assetRack',
			'c.version version',
			'c.ipAddr ipAddr',
			'c.description assetDescription',
			'c.model assetModel',
			'd.serviceNo serviceNo',
		],
		keyWordLikeFields: ['b.label', 'c.label', 'a.notes'],
		fieldEq: [{ column: 'a.rackId', requestParam: 'rackId' }],
		join: [
			{
				entity: TecServerEntity,
				alias: 'b',
				type: 'leftJoin',
				condition: 'a.serverId = b.id',
			},
			{
				entity: AssetEntity,
				alias: 'c',
				type: 'leftJoin',
				condition: 'a.assetId = c.id',
			},
			{
				type: 'leftJoin',
				entity: ServiceEntity,
				alias: 'd',
				condition: 'b.serviceId = d.id',
			},
		],
		where: async (ctx) => {
			const { serverStatus = [], assetStatus = [] } = ctx.request.body
			const status = [...serverStatus, ...assetStatus]
			const whereList = []
			if (status.length > 0) {
				whereList.push([
					'(b.status IN (:serverStatus) OR c.status IN (:assetStatus))',
					{ serverStatus: status, assetStatus: status },
				])
			}
			return whereList
		},
	},
	listQueryOp: {
		select: ['a.*', 'b.serverNo serverNo', 'b.label serverLabel', 'c.assetNo assetNo', 'c.label assetLabel'],
		fieldEq: [{ column: 'a.rackId', requestParam: 'rackId' }],
		join: [
			{
				entity: TecServerEntity,
				alias: 'b',
				type: 'leftJoin',
				condition: 'a.serverId = b.id',
			},
			{
				entity: AssetEntity,
				alias: 'c',
				type: 'leftJoin',
				condition: 'a.assetId = c.id',
			},
		],
		addOrderBy: {
			'a.seq': 'ASC',
			'a.id': 'ASC',
		},
	},
})
export class AdminRackUnitController extends BaseController {}

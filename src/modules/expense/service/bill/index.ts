import { QueryRunner, Repository } from 'typeorm'
import { Inject, Provide } from '@midwayjs/decorator'
import { InjectEntityModel } from '@midwayjs/orm'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import * as _ from 'lodash'
import * as dayjs from 'dayjs'
import { isArray } from '/@/utils/types'
import { selectBuilder, PageOption } from '/@/utils/orm'

import { TbCountEntity } from '../../../conf/entity/count'

import { ExpenseBillEntity } from '../../entity/bill'
import { ExpenseBillItemEntity } from '../../entity/bill_item'
import { ExpenseBillMatchService } from './match'
import { ExpenseBillUnmatchService } from './unmatch'
import { ExpenseEntity } from '../../entity/expense'
import { VendorEntity } from '/$/vendor/entity/vendor'
import { mathUtils } from '/@/utils/math'
import { ConfCustomNoService, Count } from '/$/conf/service/common'

/**
 * 描述
 */
@Provide()
export class ExpenseBillService extends BaseService {
	@InjectEntityModel(TbCountEntity)
	count: Repository<TbCountEntity>

	@InjectEntityModel(ExpenseEntity)
	expense: Repository<ExpenseEntity>

	@InjectEntityModel(ExpenseBillEntity)
	bill: Repository<ExpenseBillEntity>

	@InjectEntityModel(ExpenseBillItemEntity)
	billItem: Repository<ExpenseBillItemEntity>

	@Inject()
	confCustomNoService: ConfCustomNoService

	@Inject()
	match: ExpenseBillMatchService

	@Inject()
	unmatch: ExpenseBillUnmatchService

	@Inject()
	ctx

	@CoolTransaction()
	async add(param: any, queryRunner?: QueryRunner): Promise<Object> {
		const { items: billItems, ...billRest } = param
		const { username } = this.ctx.admin

		const totalAmount = billItems.reduce((total, item) => {
			return mathUtils.add(total, item.itemAmount)
		}, 0)

		billRest.totalAmount = totalAmount
		billRest.createBy = username
		billRest.updateBy = username

		const newBill = await this.confCustomNoService.handler(billRest, Count.EXPENSE_BILL, ExpenseBillEntity, queryRunner)

		await queryRunner.manager.insert(
			ExpenseBillItemEntity,
			billItems.map((item: any) => ({
				...item,
				billId: newBill.id,
				taxedAmount: item.itemAmount * (1 + item.taxRate / 100),
				updateBy: username,
				createBy: username,
			}))
		)

		const updateExpense = _.chain(billItems)
			.filter((billItem) => {
				return billItem.expenseId && billItem.billCycle !== 'One-Time' && billItem.billCycle !== 'Free'
			})
			.groupBy('expenseId') // 对expenseId进行分组
			.map((expenseGroup, expenseId) => {
				const diffDays = expenseGroup
					.filter((expense) => expense.fromDate && expense.toDate)
					.map((expense) => {
						return dayjs(expense.toDate).diff(dayjs(expense.fromDate), 'day')
					})

				const diffDayTotal = diffDays.reduce((total, cur) => (total += cur), 0)
				return { id: +expenseId, diffDayTotal }
			})
			.value()

		const updateExpensePromises = updateExpense.map(async ({ id, diffDayTotal }) => {
			const expense = await this.expense.findOne(id)
			expense.nextBillDate = dayjs(expense.nextBillDate)
				.add(diffDayTotal + 1, 'day')
				.format('YYYY-MM-DD')
			return queryRunner.manager.save(expense)
		})

		await Promise.all(updateExpensePromises)
		return newBill
	}

	async info(id: any): Promise<any> {
		const bill = await this.bill.findOne(id)
		const items = await this.billItem.find({ billId: id })
		return { ...bill, items }
	}

	@CoolTransaction()
	async update(param: any, queryRunner?: QueryRunner): Promise<void> {
		const { username } = this.ctx.admin
		const { items: billItems, ...billRest } = param

		const totalAmount = billItems.reduce((total, item) => {
			return mathUtils.add(total, item.itemAmount)
		}, 0)

		billRest.totalAmount = totalAmount

		// 1. 获取当前账单的Items(数据库中的Items)
		const findedItems = await this.billItem.find({ billId: billRest.id })

		const itemIds = billItems.filter((item) => item.id !== undefined).map((item) => item.id)
		const deleteIds = findedItems.filter((item) => !itemIds.includes(item.id)).map((item) => item.id)

		if (deleteIds.length > 0) {
			await this.billItem.delete(deleteIds)
		}

		await this.bill.update(billRest.id, {
			...billRest,
			updateBy: username,
		})

		await this.billItem.insert(
			billItems
				.filter((item) => item.id === undefined)
				.map((item) => ({
					...item,
					id: null,
					billId: billRest.id,
					taxedAmount: item.itemAmount * (1 + item.taxRate / 100),
					createBy: username,
					updateBy: username,
				}))
		)

		const updateItemIds = billItems.filter((item) => item.id !== undefined).map((item) => item.id)

		for (const updateItemId of updateItemIds) {
			const updateItem = billItems.find((item) => item.id === updateItemId)
			await this.billItem.update(updateItemId, {
				...updateItem,
				taxedAmount: updateItem.itemAmount * (1 + updateItem.taxRate / 100),
				billId: billRest.id,
				updateBy: username,
			})
		}
	}

	@CoolTransaction()
	async delete(ids: any, queryRunner?: QueryRunner): Promise<void> {
		for (const id of ids) {
			await queryRunner.manager.delete(ExpenseBillItemEntity, { billId: id })
			await queryRunner.manager.delete(ExpenseBillEntity, id)
		}
	}

	async updateAttachments(param: any) {
		const { id, updateBy, attachments } = param
		return await this.bill.update(id, { attachments, updateBy })
	}

	async deleteInfo(id) {
		const billItems = await this.billItem.find({ billId: id })

		return [
			{
				name: 'Bill Items',
				items: billItems.map((item) => ({
					...item,
					link: '/app/expense/bill-item/ops/delete',
				})),
			},
		]
	}

	/**
	 * 查询统计信息
	 */
	async getStatistics(body: any) {
		const builder = this.bill
			.createQueryBuilder('a')
			.select([
				'SUM(a.totalAmount) OVER() total',
				'SUM(a.paidAmount) OVER() paid',
				'SUM(a.totalAmount - a.paidAmount) OVER() unpaid',
				'COUNT(*) OVER() totalCount',
				'SUM(CASE WHEN a.status = "Unpaid" THEN 1 ELSE 0 END) OVER() unpaidCount',
				'SUM(CASE WHEN a.status = "Paid" THEN 1 ELSE 0 END) OVER() paidCount',
			])
			.leftJoin(ExpenseBillItemEntity, 'b', 'a.id = b.billId')
			.where('a.status IN (:...status)', { status: ['Unpaid', 'Paid'] })
			.groupBy('a.id')

		const timeFilter = [
			{ column: 'a.dueDate', requestParam: 'dueDate' },
			{ column: 'b.dueDate', requestParam: 'itemDueDate' },
		]

		timeFilter.forEach((item) => {
			const date = body[item.requestParam]
			if (!isArray(date) || date.length !== 2) return
			const [start, end] = date
			builder.andWhere(`${item.column} BETWEEN :${item.requestParam}Start AND :${item.requestParam}End`, {
				[`${item.requestParam}Start`]: start,
				[`${item.requestParam}End`]: end,
			})
		})

		if (body.currency) {
			const currency = isArray(body.currency) ? body.currency : [body.currency]
			builder.andWhere('a.currency IN (:...currency)', { currency })
		}

		const result = await builder.getRawMany()
		return result[0] || {}
	}

	/**
	 * 查询汇总信息
	 */
	async getSummary(body: any) {
		const builder = this.bill
			.createQueryBuilder('a')
			.select([
				'SUM(b.itemAmount) total',
				// 'SUM(DISTINCT a.totalAmount) - SUM(DISTINCT a.paidAmount) unpaid',
				'a.currency currency',
				'a.vendorId vendorId',
				'c.alias vendorAlias',
				'COUNT(DISTINCT a.id) billCount',
				'COUNT(*) OVER() totalCount',
			])
			.leftJoin(ExpenseBillItemEntity, 'b', 'a.id = b.billId')
			.leftJoin(VendorEntity, 'c', 'a.vendorId = c.id')
			.groupBy('a.vendorId')
			.addGroupBy('a.currency')
		// .where('a.status IN (:...status)', { status: ['Unpaid', 'Paid'] })

		const timeFilter = [
			{ column: 'a.dueDate', requestParam: 'dueDate' },
			{ column: 'b.dueDate', requestParam: 'itemDueDate' },
		]

		timeFilter.forEach((item) => {
			const date = body[item.requestParam]
			if (!isArray(date) || date.length !== 2) return
			const [start, end] = date
			builder.andWhere(`${item.column} BETWEEN :${item.requestParam}Start AND :${item.requestParam}End`, {
				[`${item.requestParam}Start`]: start,
				[`${item.requestParam}End`]: end,
			})
		})

		const pageOption: PageOption = {
			fieldEq: [
				{ column: 'a.vendorId', requestParam: 'vendorId' },
				{ column: 'a.currency', requestParam: 'currency' },
				{ column: 'a.status', requestParam: 'status' },
			],
		}

		const { list, pagination } = await selectBuilder.page(body, pageOption, builder)
		return {
			list,
			pagination: {
				...pagination,
				total: Number(list?.[0]?.['totalCount']) || 0,
			},
		}
	}
}

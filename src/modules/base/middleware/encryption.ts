import { App, Config, Inject, Middleware } from '@midwayjs/decorator'
import { NextFunction, Context } from '@midwayjs/koa'
import { IMiddleware, IMidwayApplication } from '@midwayjs/core'
import { RESCODE } from '@cool-midway/core'
import { CryptoUtils } from '../utils/crypto'

/**
 * 加密传输中间件
 * 通过X-Encrypted请求头判断是否进行加密解密处理
 */
@Middleware()
export class BaseEncryptionMiddleware implements IMiddleware<Context, NextFunction> {
	@Config('koa.globalPrefix')
	prefix

	@Config('module.base')
	baseConfig

	@App()
	app: IMidwayApplication

	private cryptoUtils: CryptoUtils

	resolve() {
		return async (ctx: Context, next: NextFunction) => {
			// 初始化加密工具
			if (!this.cryptoUtils) {
				const secretKey = this.baseConfig?.encryption?.secretKey || 'your-server-secret-key-2024'
				this.cryptoUtils = new CryptoUtils(secretKey)
			}

			// 处理加密请求
			await this.handleEncryptedRequest(ctx)

			// 继续处理请求
			await next()

			// 处理加密响应
			await this.handleEncryptedResponse(ctx)
		}
	}

	/**
	 * 处理加密请求
	 */
	private async handleEncryptedRequest(ctx) {
		const body = ctx.request.body

		// 确认是否需要进行解密
		if (body && body.isEncrypted) {
			try {
				// 解密请求数据
				if (body.encryptedData) {
					const decryptedData = this.cryptoUtils.decryptObject(body.encryptedData)
					ctx.request.body = decryptedData
				}
			} catch (error) {
				console.error('解密请求数据失败:', error)
				throw new Error('请求数据解密失败')
			}
		}
	}

	/**
	 * 处理加密响应
	 */
	private async handleEncryptedResponse(ctx) {
		const responseBody = ctx.body

		// 检查响应是否需要加密
		const needEncryption = this.baseConfig.encryption.enabled && ctx.url.startsWith('/admin')

		if (needEncryption) {
			try {
				const encryptedData = this.cryptoUtils.encryptObject(responseBody)
				ctx.body = { encryptedData, isEncrypted: true }
			} catch (error) {
				console.error('加密响应数据失败:', error)
				throw new Error('响应数据加密失败')
			}
		}
	}
}

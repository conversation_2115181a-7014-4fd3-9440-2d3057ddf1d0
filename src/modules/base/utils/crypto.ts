import * as crypto from 'crypto'

// 加密算法配置
const ALGORITHM = 'aes-256-cbc'
const ENCODING = 'hex'

/**
 * 加密工具类
 * 提供AES-256-CBC加密解密功能
 */
export class CryptoUtils {
	private secretKey: Buffer

	constructor(secretKey: string) {
		// 用SHA256生成32字节密钥，保证与前端一致
		this.secretKey = crypto.createHash('sha256').update(secretKey).digest()
	}

	/**
	 * 加密文本数据
	 * @param text 要加密的文本
	 * @returns 加密后的字符串（格式：iv:encryptedData）
	 */
	encrypt(text: string): string {
		try {
			// 生成随机初始化向量
			const iv = crypto.randomBytes(16)

			// 创建加密器
			const cipher = crypto.createCipheriv(ALGORITHM, this.secretKey, iv)

			// 加密数据
			let encrypted = cipher.update(text, 'utf8', ENCODING)
			encrypted += cipher.final(ENCODING)

			// 返回IV和加密数据的组合（IV用于解密）
			return iv.toString(ENCODING) + ':' + encrypted
		} catch (error) {
			console.error('加密失败:', error)
			throw new Error('加密失败')
		}
	}

	/**
	 * 解密文本数据
	 * @param encryptedData 加密的数据（格式：iv:encryptedData）
	 * @returns 解密后的文本
	 */
	decrypt(encryptedData: string): string {
		try {
			// 分离IV和加密数据
			const parts = encryptedData.split(':')
			if (parts.length !== 2) {
				throw new Error('加密数据格式错误')
			}

			const iv = Buffer.from(parts[0], ENCODING)
			const encrypted = parts[1]

			// 创建解密器
			const decipher = crypto.createDecipheriv(ALGORITHM, this.secretKey, iv)

			// 解密数据
			let decrypted = decipher.update(encrypted, ENCODING, 'utf8')
			decrypted += decipher.final('utf8')

			return decrypted
		} catch (error) {
			console.error('解密失败:', error)
			throw new Error('解密失败')
		}
	}

	/**
	 * 加密对象
	 * @param obj 要加密的对象
	 * @returns 加密后的字符串
	 */
	encryptObject(obj: any): string {
		return this.encrypt(JSON.stringify(obj))
	}

	/**
	 * 解密对象
	 * @param encryptedData 加密的数据
	 * @returns 解密后的对象
	 */
	decryptObject(encryptedData: string): any {
		return JSON.parse(this.decrypt(encryptedData))
	}

	/**
	 * 验证加密数据格式是否正确
	 * @param encryptedData 加密的数据
	 * @returns 是否为有效的加密数据格式
	 */
	isValidEncryptedData(encryptedData: string): boolean {
		try {
			const parts = encryptedData.split(':')
			return parts.length === 2 && parts[0].length === 32 && parts[1].length > 0
		} catch {
			return false
		}
	}
}

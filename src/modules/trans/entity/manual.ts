import { EntityModel } from '@midwayjs/orm'
import { BaseEntity } from '@cool-midway/core'
import { Column, JoinColumn, JoinTable, ManyToMany, ManyToOne } from 'typeorm'

import { CustomerEntity } from '../../customer/entity/customer'
import { VendorEntity } from '../../vendor/entity/vendor'
import { AccountChartEntity } from '../../account/entity/chart'
import { ExpenseBillEntity } from '../../expense/entity/bill'
import { InvoiceEntity } from '../../invoice/entity/invoice'

/**
 * 描述
 */
@EntityModel('op_trans_manual')
export class TransManualEntity extends BaseEntity {
	@Column({ comment: '交易号', default: '', unique: true })
	tranNo: string

	@Column({ type: 'date', comment: '交易日期', nullable: true })
	tranDate: Date

	@Column({ type: 'date', comment: '应计日期', nullable: true })
	accrualDate: Date

	@Column({ comment: '交易描述', default: '' })
	description: string

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		comment: '已匹配账单金额',
		default: 0,
	})
	matchedAmount: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		comment: '已匹配import银行交易金额',
		default: 0,
	})
	matchedImportAmount: number

	@Column({ comment: '交易类型', default: '' })
	tranType: string

	@Column({ comment: '交易币种', default: '' })
	tranCurrency: string

	@ManyToOne(() => AccountChartEntity, { nullable: true })
	@JoinColumn({ name: 'currentAccountId' })
	@Column({ nullable: true, comment: '关联当前账户ID' })
	currentAccountId: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		comment: '当前账户金额',
		default: 0,
	})
	currentAccountAmount: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		comment: '交易手续费',
		default: 0,
	})
	tranFee: number

	@Column({ comment: '交易网关', default: '' })
	tranGateway: string

	@Column({ comment: '入账币种', default: '' })
	counterAccountCurrency: string

	@ManyToOne(() => AccountChartEntity, { nullable: true })
	@JoinColumn({ name: 'counterAccountId' })
	@Column({ nullable: true, comment: '对方账户ID' })
	counterAccountId: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		comment: '对方账户金额',
		default: 0,
	})
	counterAccountAmount: number

	@Column({ comment: '可见角色', default: '' })
	visibleRoleId: string

	@Column({ nullable: true, comment: '关联客户ID' })
	customerId: number

	@ManyToOne(() => CustomerEntity)
	customer: CustomerEntity

	@Column({ nullable: true, comment: '关联供应商ID' })
	vendorId: number

	@ManyToOne(() => VendorEntity)
	vendor: VendorEntity

	@Column({
		default: '',
		nullable: false,
		comment: '账户关键字，多个以逗号分割',
	})
	accountKeyword: string

	@Column({
		type: 'varchar',
		nullable: false,
		default: '',
		comment: '标签，多个标签以逗号分割',
	})
	tags: string

	@Column({ comment: '备注1', type: 'text', nullable: true })
	notes: string

	@Column({ comment: '创建者', default: '' })
	createBy: string

	@Column({ comment: '更新者', default: '' })
	updateBy: string

	@Column({ comment: '附件', default: '' })
	attachments: string

	// 对应的银行交易ID
	@Column({ comment: '对应的银行交易ID', nullable: true, unique: true })
	bankTranId: string

	// 对应的CD WHMCSID
	@Column({ comment: '对应的CDWHMCS交易ID', nullable: true, unique: true })
	cdWhTranId: number

	@Column({
		type: 'varchar',
		length: 20,
		default: 'Pending',
		comment: '记账状态：Pending-待记账, Journaled-已记账, Classified-已分类'
	})
	journalStatus: 'Pending' | 'Journaled' | 'Classified'

	@ManyToMany(() => ExpenseBillEntity, (bill) => bill.trans)
	bills: ExpenseBillEntity[]

	@ManyToMany(() => InvoiceEntity, (invoice) => invoice.trans)
	invoices: InvoiceEntity[]

	@ManyToMany(() => TransManualEntity, (transManual) => transManual.relatedTransManuals, { nullable: true })
	@JoinTable({
		name: 'op_trans_manual_related_trans_manual',
		joinColumn: { name: 'transManualId', referencedColumnName: 'id' },
		inverseJoinColumn: {
			name: 'relatedTransManualId',
			referencedColumnName: 'id',
		},
	})
	relatedTransManuals: TransManualEntity[]

	// 可选的属性，仅用在添加Transfer时作逻辑判断，不会存入数据库
	reverse?: boolean
}

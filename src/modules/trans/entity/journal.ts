import { EntityModel } from '@midwayjs/orm'
import { BaseEntity } from '@cool-midway/core'
import { Column, ManyToOne, JoinColumn } from 'typeorm'

import { TransManualEntity } from './manual'

/**
 * 描述
 */
@EntityModel('op_trans_journal')
export class TransJournalEntity extends BaseEntity {
	@Column({ type: 'varchar', length: 255, comment: '流水号' })
	journalNo: string

	@Column({ type: 'date', comment: '记账日期' })
	journalDate: Date

	@Column({ type: 'varchar', length: 255, comment: '描述' })
	description: string

	@ManyToOne(() => TransManualEntity, {
		onDelete: 'RESTRICT',
		onUpdate: 'RESTRICT',
	})
	@JoinColumn({ name: 'transManualId' })
	transManual: TransManualEntity

	@Column({ type: 'int', nullable: true, comment: '关联手工录入交易ID' })
	transManualId: number

	@Column({ type: 'varchar', length: 255, comment: '状态：未分类, 已分类' })
	status: string

	@Column({ type: 'varchar', length: 2550, default: '' })
	notes: string

	@Column({
		type: 'varchar',
		length: 255,
		default: '',
		comment: '可见角色，多个以逗号分割',
	})
	visibleRoleId: string

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: 0.0,
		comment: '账单总金额',
	})
	totalAmount: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 4,
		default: 0,
		comment: '转换为基础货币的汇率',
	})
	rateToBcy: number

	@Column({ type: 'varchar', length: 10, default: '', comment: '原始当前货币' })
	originCurrentCurrency: string

	@Column({
		type: 'decimal',
		precision: 15,
		scale: 2,
		default: 0.0,
		comment: '原始当前金额',
	})
	originCurrentAmount: number

	@Column({ type: 'varchar', length: 10, default: '', comment: '原始对手货币' })
	originCounterCurrency: string

	@Column({
		type: 'decimal',
		precision: 15,
		scale: 2,
		default: 0.0,
		comment: '原始对手金额',
	})
	originCounterAmount: number

	@Column({ type: 'varchar', length: 255, comment: '创建者' })
	createBy: string

	@Column({ type: 'varchar', length: 255, comment: '更新者' })
	updateBy: string
}

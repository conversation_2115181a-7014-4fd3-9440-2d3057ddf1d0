import { EntityModel } from '@midwayjs/orm'
import { BaseEntity } from '@cool-midway/core'
import { Column, ManyToOne, JoinColumn } from 'typeorm'
import { AccountChartEntity } from '../../account/entity/chart'
import { CustomerEntity } from '../../customer/entity/customer'
import { VendorEntity } from '../../vendor/entity/vendor'
import { TransJournalEntity } from './journal'

/**
 * 描述
 */
@EntityModel('op_trans_journal_item')
export class TransJournalItemEntity extends BaseEntity {
	@Column({ nullable: true, comment: '关联客户ID' })
	customerId: number

	@ManyToOne(() => CustomerEntity)
	customer: CustomerEntity

	@ManyToOne(() => TransJournalEntity, { onDelete: 'CASCADE', onUpdate: 'CASCADE' })
	journal: TransJournalEntity
	@Column({ nullable: true, comment: '关联记账ID' })
	journalId

	@Column({ nullable: true, comment: '关联供应商ID' })
	vendorId: number

	@ManyToOne(() => VendorEntity)
	vendor: VendorEntity

	@Column({ type: 'date', comment: '应记日期，指该笔款项实际应收/付的日期' })
	accrualDate: Date

	@ManyToOne(() => AccountChartEntity, {
		onDelete: 'RESTRICT',
		onUpdate: 'RESTRICT',
	})
	@JoinColumn({ name: 'accountChartId' })
	accountChart: AccountChartEntity

	@Column({ type: 'int', nullable: true, comment: '关联会计科目ID' })
	accountChartId: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: 0,
		comment: '借方金额',
	})
	debit: number

	@Column({
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: 0,
		comment: '贷方金额',
	})
	credit: number

	@Column({ type: 'varchar', length: 2550, default: '' })
	notes: string

	@Column({ type: 'varchar', length: 255, comment: '创建者' })
	createBy: string

	@Column({ type: 'varchar', length: 255, comment: '更新者' })
	updateBy: string
}

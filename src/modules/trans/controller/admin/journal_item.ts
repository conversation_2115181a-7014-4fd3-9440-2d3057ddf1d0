import { Provide } from '@midwayjs/decorator'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, BaseController } from '@cool-midway/core'
import { TransJournalItemEntity } from '../../entity/journal_item'
import { TransJournalEntity } from '../../entity/journal'
import { CustomerEntity } from '../../../customer/entity/customer'
import { VendorEntity } from '../../../vendor/entity/vendor'
import { AccountChartEntity } from '../../../account/entity/chart'
/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: TransJournalItemEntity,
	pageQueryOp: {
		fieldEq: [
			{ column: 'a.journalId', requestParam: 'journalId' },
			{ column: 'journal.transManualId', requestParam: 'transManualId' },
			{ column: 'a.customerId', requestParam: 'customerId' },
			{ column: 'a.vendorId', requestParam: 'vendorId' },
			{ column: 'a.accountChartId', requestParam: 'accountChartId' },
			{ column: 'journal.status', requestParam: 'status' },
		],
		keyWordLikeFields: [
			'a.notes',
			'journal.journalNo',
			'journal.description',
			'customer.customerNo',
			'customer.alias',
			'vendor.vendorNo',
			'vendor.alias',
			'chart.accountCode',
			'chart.accountName',
		],
		select: [
			'a.*',
			'journal.journalNo',
			'journal.description as journalDescription',
			'journal.status as journalStatus',
			'journal.transManualId',
			'customer.customerNo',
			'customer.alias as customerAlias',
			'vendor.vendorNo',
			'vendor.alias as vendorAlias',
			'chart.accountCode',
			'chart.accountName',
			'chart.accountAlias',
		],
		join: [
			{
				entity: TransJournalEntity,
				alias: 'journal',
				condition: 'a.journalId = journal.id',
				type: 'leftJoin',
			},
			{
				entity: CustomerEntity,
				alias: 'customer',
				condition: 'a.customerId = customer.id',
				type: 'leftJoin',
			},
			{
				entity: VendorEntity,
				alias: 'vendor',
				condition: 'a.vendorId = vendor.id',
				type: 'leftJoin',
			},
			{
				entity: AccountChartEntity,
				alias: 'chart',
				condition: 'a.accountChartId = chart.id',
				type: 'leftJoin',
			},
		],
	},
})
export class AdminJournalItemController extends BaseController {}

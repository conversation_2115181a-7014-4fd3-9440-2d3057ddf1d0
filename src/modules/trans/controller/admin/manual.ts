import { Brackets, SelectQueryBuilder } from 'typeorm'
import { Body, Inject, Post, Provide } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'
import { TransManualEntity } from '../../entity/manual'
import { AccountChartEntity } from '../../../account/entity/chart'
import { CustomerEntity } from '../../../customer/entity/customer'
import { VendorEntity } from '../../../vendor/entity/vendor'
import { TransManualService } from '../../service/manual'
import { visibleRoleHandler } from '../../../../utils'
import { ManualRelatedManualService } from '../../service/manual/manual_related_manual'
import { findKeyWords } from '/@/utils/builder/find-key-words'
import { isArray } from '/@/utils/types'

function getMatchableWhere(ctx: any) {
	const { matchable } = ctx.request.body
	if (!matchable) return []
	return [['a.currentAccountAmount > a.matchedAmount', {}]]
}

function getAmountWhere(ctx: any) {
	const curAmount = ctx.request.body?.currentAccountAmount
	const date = ctx.request.body?.date
	const where = []
	if (date && isArray(date) && date.length === 2) {
		const [startDate, endDate] = date
		where.push([
			`CAST(a.tranDate AS DATE) >= CAST(:startDate AS DATE)
    AND CAST(a.tranDate AS DATE) <= CAST(:endDate AS DATE)`,
			{ startDate, endDate },
		])
	}
	if (!curAmount) return where
	if (curAmount.toString().startsWith('~')) {
		const currentAccountAmount = parseFloat(curAmount.slice(1)) // 去掉"~"并解析数值
		if (!(currentAccountAmount > 0)) return where

		const percentage = 0.05 // 这里定义正负范围的百分比
		const minAmount = currentAccountAmount * (1 - percentage)
		const maxAmount = currentAccountAmount * (1 + percentage)

		where.push(['a.currentAccountAmount BETWEEN :minAmount AND :maxAmount', { minAmount, maxAmount }])
	} else if (curAmount > 0) {
		where.push(['a.currentAccountAmount = :curAmount', { curAmount: parseFloat(curAmount) }])
	}
	return where
}

const keyWordLikeFields = [
	'a.tranNo',
	'a.description',
	'a.accountKeyword',
	'd.customerNo',
	'd.alias',
	'e.vendorNo',
	'e.alias',
	'b.accountName',
	'c.accountName',
]

@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: TransManualEntity,
	service: TransManualService,

	pageQueryOp: async (ctx: any) => ({
		keyWordLikeFields,

		fieldEq: [
			{ column: 'a.status', requestParam: 'status' },
			{ column: 'a.tranType', requestParam: 'tranType' },
			{ column: 'a.customerId', requestParam: 'customerId' },
			{ column: 'a.customerId', requestParam: 'customerIds' },
			{ column: 'a.vendorId', requestParam: 'vendorId' },
			{ column: 'a.id', requestParam: 'id' },
			{ column: 'a.currentAccountId', requestParam: 'currentAccountId' },
			{ column: 'a.counterAccountId', requestParam: 'counterAccountId' },
			{ column: 'a.tranCurrency', requestParam: 'tranCurrency' },
			{
				column: 'a.counterAccountCurrency',
				requestParam: 'counterAccountCurrency',
			},
			{ column: 'a.description', requestParam: 'description' },
			{ column: 'a.journalStatus', requestParam: 'journalStatus' },
			// {
			// 	column: 'a.currentAccountAmount',
			// 	requestParam: 'currentAccountAmount',
			// },
		],
		select: [
			'a.*',
			'b.accountCode currentAccountCode',
			'b.accountName currentAccountName',
			'b.currency currentAccountCurrency',
			'c.accountCode counterAccountCode',
			'c.accountName counterAccountName',
			'd.customerNo customerNo',
			'd.alias customerAlias',
			'd.cdWhClientId cdWhClientId',
			'e.vendorNo vendorNo',
			'e.alias vendorAlias',
		],
		join: [
			{
				type: 'leftJoin',
				entity: AccountChartEntity,
				alias: 'b',
				condition: 'a.currentAccountId = b.id',
			},
			{
				type: 'leftJoin',
				entity: AccountChartEntity,
				alias: 'c',
				condition: 'a.counterAccountId = c.id',
			},
			{
				type: 'leftJoin',
				entity: CustomerEntity,
				alias: 'd',
				condition: 'a.customerId = d.id',
			},
			{
				type: 'leftJoin',
				entity: VendorEntity,
				alias: 'e',
				condition: 'a.vendorId = e.id',
			},
		],
		extend: async (find: SelectQueryBuilder<any>) => {
			await visibleRoleHandler(ctx)(find)

			const value = ctx.request.body.keyWords
			findKeyWords(find, { value, keyWordLikeFields })
		},
		where: async (ctx) => getAmountWhere(ctx),
	}),

	listQueryOp: async () => ({
		fieldEq: ['customerId', 'tranType'],
		addOrderBy: { id: 'DESC' },
		where: async (ctx: any) => getMatchableWhere(ctx),
	}),
})
export class TransManualController extends BaseController {
	@Inject()
	transManualService: TransManualService

	@Inject()
	manualRelatedManual: ManualRelatedManualService

	// 获取关联的transManual
	@Post('/related')
	async getRelatedTransManualById(@Body('id') transManualId) {
		return this.ok(await this.manualRelatedManual.getRelatedTransManualById(transManualId))
	}

	// 同步操作
	@Post('/ops/update/sync/whmcscd')
	async syncTrans(@Body() body) {
		return this.transManualService.sync(body)
	}

	// unlink
	@Post('/ops/update/unlink')
	async unlink(@Body() body) {
		return await this.transManualService.unlink(body)
	}

	// WHMCS CD invoice match
	@Post('/ops/update/invoicewhcd/match')
	async matchWHCdInvoiceAmount(@Body() body) {
		return await this.transManualService.matchWHCdInvoiceAmount(body)
	}

	// WHMCS CD invoice unmatch
	@Post('/ops/update/invoicewh_cd/unmatch')
	async unmatchWHCdInvoiceAmount(@Body() body) {
		return await this.transManualService.unmatchWHCdInvoiceAmount(body)
	}

	// match trans import
	@Post('/ops/update/match/import')
	async matchTransImport(@Body() body) {
		const { manualId, importId, matchAmount } = body

		if (!manualId) return this.fail('Manual id is invalid')
		if (!importId) return this.fail('Import id is invalid')
		if (!matchAmount || +matchAmount <= 0) return this.fail('Amount is invalid')

		return this.transManualService.matchTransImport(body)
	}

	// match trans import
	@Post('/ops/update/unmatch/import')
	async unmatchTransImport(@Body() body) {
		return this.ok(await this.transManualService.unmatchTransImport(body))
	}

	@Post('/ops/page/by/import')
	async pageByImport(@Body() body) {
		return await this.transManualService.pageByImport(body)
	}

	// 同步操作弹出框本地交易记录
	@Post('/ops/page/sync/local/trans')
	async pageLocalTrans(@Body() body) {
		return this.ok(await this.transManualService.pageLocalTrans(body))
	}

	// 同步操作弹出框whmcscd交易记录
	@Post('/ops/page/sync/whmcscd/trans')
	async pageWhmcsCdTrans(@Body() body) {
		return this.ok(await this.transManualService.pageWhmcsCdTrans(body))
	}

	@Post('/select')
	async getSelectData() {
		return this.page()
	}
}

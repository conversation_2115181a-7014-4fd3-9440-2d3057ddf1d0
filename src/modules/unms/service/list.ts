import { CacheManager } from '@midwayjs/cache'
import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { QueryRunner, Repository, createQueryBuilder } from 'typeorm'
import * as _ from 'lodash'
import * as dayjs from 'dayjs'
import { MonItemListEntity } from '../entity/list'
import { MonClassifyPolicyEntity } from '../entity/policy'
import { MonItemHistoryEntity } from '../entity/history'
import { MonClassifyPolicyService } from './policy'
import { getMatchedPolicy } from '../utils/classify'

/**
 * 描述
 */
@Provide()
export class MonItemListService extends BaseService {
	/**
	 * 获取缓存中 itemList 的key
	 */

	@Inject()
	cacheManager: CacheManager

	@Inject()
	policyService: MonClassifyPolicyService

	@InjectEntityModel(MonItemListEntity)
	itemList: Repository<MonItemListEntity>

	@InjectEntityModel(MonItemHistoryEntity)
	itemHistory: Repository<MonItemHistoryEntity>

	@InjectEntityModel(MonClassifyPolicyEntity)
	policy: Repository<MonClassifyPolicyEntity>

	@Inject()
	ctx

	getItemRemindCacheKey(id: number) {
		return `unms:list:remind:${id}`
	}

	getItemConfirmCacheKey(id: number) {
		return `unms:list:confirm:${id}`
	}

	async list(): Promise<any> {
		const list = await createQueryBuilder()
			.select(['l.*', 'p.name policyName'])
			.from(MonItemListEntity, 'l')
			.leftJoin(MonClassifyPolicyEntity, 'p', 'l.policyId = p.id')
			.getRawMany()

		const listPromises = _.chain(list)
			.map((i) => {
				i.isUnupdate = dayjs(i.lastUpdate)
					.add(+(i.unupdateRemind || '86400'), 'seconds')
					.isBefore(dayjs())
				return i
			})
			.map(async (i) => {
				const cacheKey = this.getItemRemindCacheKey(i.id)
				const confirmKey = this.getItemConfirmCacheKey(i.id)
				i.originData = _.pick(i.originData, ['name', 'description'])
				i.remind = await this.cacheManager.get(cacheKey)
				i.confirmMsg = await this.cacheManager.get(confirmKey)
				return i
			})
			.value()

		return Promise.all(listPromises)
	}

	async update(param: any): Promise<void> {
		const { username } = this.ctx.admin
		const { id, mrtgLink, ...newItem } = param
		const ids = _.isArray(id) ? id : [id]

		// TODO: 动态表单配置功能完成之后需要进行修改
		if (mrtgLink) {
			newItem.itemValues = newItem.itemValues || {}
			newItem.itemValues.mrtgLink = mrtgLink
		}

		const updateBy = username
		const saveItems = ids.map((id) => ({ id, updateBy, ...newItem }))

		await this.taskCallUpdate(saveItems)
	}

	async updateSelected(params) {
		const { username } = this.ctx.admin
		const { ids, ...newItem } = params
		await this.update({ ...newItem, id: ids, updateBy: username })
	}

	// TODO: 后续需要将map label / value 修改为 [type, type, ...]
	// 获取监控项目类型
	async getMonTypeGroup() {
		const typeList = await this.itemList
			.createQueryBuilder('l')
			.select(['l.monType monType'])
			.where('l.monType != ""')
			.groupBy('l.monType')
			.getRawMany()

		return typeList.map(({ monType }) => ({ label: monType, value: monType }))
	}

	async getClassify(monItem: MonItemListEntity, values: any) {
		const result = { severity: '', message: '', lastChange: '' }
		if (!monItem.policyId) return result

		const pItems = await this.policyService.getItemsCache(monItem.policyId)

		if (!pItems.length) return result

		const classify = getMatchedPolicy(values, pItems)

		if (!classify) return result

		const { severity, message } = classify

		result.severity = severity ?? ''
		result.message = message ?? ''

		const currentDateTime = dayjs().format('YYYY-MM-DD HH:mm:ss')

		if (monItem.severity !== severity) {
			result.lastChange = currentDateTime
		} else {
			result.lastChange = monItem.lastChange
		}

		return result
	}

	/**
	 * 修改监控项的延迟提醒时间
	 * @param {Number | Number[]} id 监控项id（s）
	 * @param {Number} seconds 延迟提醒时间（秒）
	 */
	async changeRemind(id: number | number[], seconds) {
		const ids = _.isArray(id) ? id : [id]
		const promises = ids.map((id) => {
			const cacheKey = this.getItemRemindCacheKey(id)
			if (+seconds === 1) return this.cacheManager.del(cacheKey)
			return this.cacheManager.set(cacheKey, seconds, {
				ttl: seconds,
			})
		})

		await Promise.all(promises)
	}

	// 设置确认信息
	async setConfirm(body: Record<string, any>) {
		const { id, remind, confirmMsg, removeTime } = body

		if (remind !== undefined) await this.changeRemind(id, remind)

		const cacheKey = this.getItemConfirmCacheKey(id)

		if (!confirmMsg) return this.cacheManager.del(cacheKey)
		return this.cacheManager.set(cacheKey, `${this.ctx.admin.username}: ${confirmMsg}`, {
			ttl: removeTime || 0,
		})
	}

	@CoolTransaction({ isolation: 'READ UNCOMMITTED' })
	async taskCallUpdate(monItems: MonItemListEntity[], runner?: QueryRunner) {
		return runner?.manager.transaction(async (builder) => {
			const promises = monItems.map((monItem) => {
				return builder.update(MonItemListEntity, monItem.id, monItem)
			})

			return Promise.all(promises)
		})
	}

	// async taskCallUpdate(monItems: MonItemListEntity[]) {
	// 	const promises = monItems.map((monItem) => {
	// 		return this.itemList.update(monItem.id, monItem)
	// 	})

	// 	return Promise.all(promises)
	// }

	mapField(params) {
		const monItem = new MonItemListEntity()
		const monHistory = new MonItemHistoryEntity()

		const {
			itemId,
			itemid,
			name,
			platform,
			policyId,
			pid,
			desc,
			description,
			monType,
			type,
			monSrc,
			src,
			monDest,
			dst,
			relatedLocations,
			loc,
			relatedDevices,
			devs,
			relatedLinks,
			lnks,
			unit,
			valueFields,
			...rest
		} = params

		monItem.itemId = itemId || itemid
		monItem.name = name
		monItem.platform = platform
		monItem.description = description || desc || ''
		monItem.monType = monType || type || ''
		monItem.monSrc = monSrc || src || ''
		monItem.monDest = monDest || dst || ''
		monItem.relatedLocations = relatedLocations || loc || ''
		monItem.relatedDevices = relatedDevices || devs || ''
		monItem.relatedLinks = relatedLinks || lnks || ''
		monItem.unit = unit || ''
		monItem.originData = { name }

		const lastDate = dayjs().format('YYYY-MM-DD HH:mm:ss')

		monItem.lastPull = lastDate
		monItem.lastUpdate = lastDate
		monItem.classifyField = _.keys(rest).join(',')

		monHistory.originData = rest
		monHistory.lastUpdate = lastDate
		monHistory.lastPull = lastDate

		const valueArray = valueFields?.split(',').map((i) => i.trim())

		if (_.isArray(valueArray) && valueArray.length > 0) {
			const value = valueArray
				.map((k) => {
					if (!params[k]) return
					return `${k}: ${params[k] ?? ''}`
				})
				.filter((i) => i)
				.join('; ')

			monItem.value = value
			monHistory.value = value
		}

		return {
			monItem,
			monHistory,
		}
	}

	async pushItem(params) {
		let { monItem, monHistory } = this.mapField(params)

		const platform = monItem.platform
		const itemId = monItem.itemId
		const localItem = await this.itemList.findOne({ platform, itemId })

		monItem = await this.itemList.save({ ...localItem, ...monItem })

		const classify = await this.getClassify(monItem, monHistory.originData)

		monHistory.monItemId = monItem.id
		monHistory.severity = classify.severity
		monHistory.message = classify.message
		monHistory.lastChange = classify.lastChange
		monItem.severity = classify.severity
		monItem.message = classify.message
		monItem.lastChange = classify.lastChange

		// 加入历史记录表
		await this.itemList.update(monItem.id, monItem)
		await this.itemHistory.insert(monHistory)
		return monItem
	}

	async pushItems(params) {
		const { platform, items, pid } = params
		const promises = items.map(async (item) => {
			return this.pushItem({ ...item, platform, pid })
		})
		return Promise.all(promises)
	}
}

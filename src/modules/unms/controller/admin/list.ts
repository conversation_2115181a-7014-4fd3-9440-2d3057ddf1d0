import { Body, Inject, Post, Provide } from '@midwayjs/decorator'
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core'
import { MonItemListEntity } from '../../entity/list'
import { MonItemListService } from '../../service/list'
import { MonZabbixService } from '../../service/zabbix'

/**
 * 描述
 */
@Provide()
@CoolController({
	api: ['add', 'delete', 'update', 'info', 'list', 'page'],
	entity: MonItemListEntity,
	service: MonItemListService,
})
export class AdminMonItemListController extends BaseController {
	@Inject()
	monItemListService: MonItemListService

	@Inject()
	monZabbixService: MonZabbixService

	@Post('/ops/update/selected')
	async updateSelected(@Body() body) {
		return this.ok(await this.monItemListService.updateSelected(body))
	}

	@Post('/remind')
	async changeDelayedReminder(@Body() body) {
		if (!body.id) throw new Error('id is required')
		return this.ok(await this.monItemListService.changeRemind(body.id, body.seconds))
	}

	@Post('/confirm')
	async setConfirm(@Body() body) {
		return this.ok(await this.monItemListService.setConfirm(body))
	}
}

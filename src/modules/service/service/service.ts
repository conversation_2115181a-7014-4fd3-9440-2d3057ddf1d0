import { Inject, Provide } from '@midwayjs/decorator'
import { BaseService, CoolTransaction } from '@cool-midway/core'
import { InjectEntityModel } from '@midwayjs/orm'
import { Brackets, Not, QueryRunner, Repository } from 'typeorm'

import { ServiceEntity } from '../entity/service'
import { CustomerEntity } from '../../customer/entity/customer'
import { whmcsCdApi, whmcsCdSql } from '../../../vendor/whmcs-cd'
import { calculateDaysDifference, getCurrentDate } from '../../../utils/day'
import { Message } from '../../../hooks/message'
import { whmcsCustomFieldsFormat } from '/@/vendor/whmcs-cd/utils/format'
import ResponseMessage from '/@/utils/message'
import { TextEnum } from '/$/order/enums/textEnum'
import * as _ from 'lodash'
import { ServiceOptionEntity } from '../entity/option'
import { ServiceType } from '/@/global/constants'
import { Service, ServiceOption } from '../entity'
import { ServiceItem } from '../entity/service_item'

/**
 * 描述
 */
@Provide()
export class ServiceService extends BaseService {
	@InjectEntityModel(ServiceEntity)
	serviceEntity: Repository<ServiceEntity>

	@InjectEntityModel(ServiceOption)
	serviceOptionRepository: Repository<ServiceOption>

	@InjectEntityModel(CustomerEntity)
	customerEntity: Repository<CustomerEntity>

	@Inject()
	ctx

	// 获取上一个子服务
	@CoolTransaction()
	async getPrevServiceItem(serviceId, currentServiceItemId, runner?: QueryRunner) {
		const serviceItem = await runner.manager.findOne(ServiceItem, {
			where: { serviceId, id: Not(currentServiceItemId), parentItemId: null },
			order: { id: 'DESC' },
		})
		return serviceItem
	}

	/**
	 * 描述
	 */
	@CoolTransaction()
	async add(params, queryRunner?: QueryRunner) {
		const { username } = this.ctx.admin
		const { Base } = params
		const subservice = params[Base.serviceType]

		// 1. 获取客户信息
		const customer = await queryRunner.manager.findOne(CustomerEntity, Base.customerId)

		// 2. 获取新增service.serviceNo
		const { serviceNo, serviceNo2 } = await this.getServiceNo(customer.id)

		// 3. 新增service
		const { raw } = await queryRunner.manager.insert(ServiceEntity, {
			...Base,
			serviceNo,
			serviceNo2,
			location: subservice.location,
			billCurrency: subservice.billCurrency,
			billCycle: subservice.billCycle,
			billAmount: subservice.billAmount,
			billStatus: subservice.billStatus,
			createBy: username,
			updateBy: username,
		})

		// 3. 获取插入的serviceId, 插入对应的子服务

		const serviceOption = this.convertToServiceOptions(subservice)
		serviceOption.serviceId = raw.insertId
		serviceOption.createBy = username
		serviceOption.updateBy = username
		serviceOption.burstPrice = Base.burstPrice
		await queryRunner.manager.insert(ServiceOptionEntity, serviceOption)

		return queryRunner.manager.findOne(ServiceEntity, raw.insertId)
	}

	@CoolTransaction()
	async updateFormService(params, queryRunner?: QueryRunner) {
		const { username } = this.ctx.admin
		const { Base } = params
		const { serviceType, burstPrice } = Base
		const subservice = params[serviceType]
		const message = new ResponseMessage()

		// 1. 更新service
		await queryRunner.manager.update(ServiceEntity, Base.id, {
			...Base,
			location: subservice.location,
			billCurrency: Base.billCurrency,
			billCycle: Base.billCycle,
			billAmount: Base.billAmount,
			billStatus: Base.billStatus,
			serviceStatus: Base.serviceStatus,
			updateBy: username,
		})

		// 2. 更新子服务
		// const SubserviceEntity = await this.getSubserviceEntity(serviceType, true)
		// await queryRunner.manager.update(SubserviceEntity, params[serviceType].id, {
		// 	...subservice,
		// 	updateBy: username,
		// })
		subservice.updateBy = username
		const serviceOption = this.convertToServiceOptions(subservice)
		serviceOption.burstPrice = burstPrice
		await queryRunner.manager.update(ServiceOption, serviceOption.id, serviceOption)

		// 3. 更新WHMCS服务信息
		const serviceRecord = await queryRunner.manager.findOne(ServiceEntity, {
			id: Base.id,
		})

		if (serviceRecord && serviceRecord.cdWhServiceId) {
			const queryBuilder = whmcsCdSql.builder()
			// 判断OPS服务是否终止，如果终止且终止日期有效，则同步更新WHMCS，否则不更新
			let terminationDate
			if (
				serviceRecord.billStatus === 'Terminated' &&
				Base.terminateDate !== '1000-01-01' &&
				Base.terminateDate !== null
			) {
				terminationDate = Base.terminateDate
			} else {
				terminationDate = undefined
			}

			const updateObject = {
				regdate: Base.firstActiveDate,
				domain: Base.serviceNo + '#' + (Base.primaryIp ?? Base.serviceDesc),
				domainstatus: Base.serviceStatus,
				amount: Base.billAmount,
				billingcycle: subservice.billCycle,
				nextduedate: Base.dueDate,
				nextinvoicedate: Base.nextInvoiceDate,
				...(terminationDate && { termination_date: terminationDate }),
			}

			await queryBuilder
				.update('tblhosting')
				.set(updateObject)
				.where('id = :id', { id: serviceRecord.cdWhServiceId })
				.execute()

			// 更新服务的自定义字段（此处通过API方式更新会更简单）
			const updateClientProductInfo: Record<string, any> = {
				serviceid: serviceRecord.cdWhServiceId,
				customfields: whmcsCustomFieldsFormat(serviceRecord.serviceType, {
					location: subservice.location,
					serviceDetails: subservice.serviceDetails,
				}),
			}

			await whmcsCdApi.update('UpdateClientProduct', updateClientProductInfo)
			message.success(TextEnum.SERVICE_WH_UPDATE_SUCCESS)
		}
	}

	async info(id: number) {
		// 1. 获取基础信息
		const service = await this.serviceEntity.findOne(id)

		// 2. 获取子服务信息
		// 获取第一个（即id最大的那一个）
		// 按照ID降序排序，这样最大的ID会是第一个
		const serviceOption = await this.serviceOptionRepository.findOne({
			where: { serviceId: id, status: Not('Voided') },
			order: { id: 'DESC' },
		})

		if (!serviceOption) {
			throw new Error('当前服务已终止或正在订单跟进中, 不允许直接修改！')
		}

		// 兼容前端现有的配置
		const subservice = {
			..._.omit(serviceOption, ['optionValues']),
			...serviceOption.optionValues,
		}

		return {
			Base: service,
			[service.serviceType]: subservice,
		}
	}

	async getServiceInfo(id: number) {
		const service = await this.serviceEntity.findOne(id)
		const serviceItem = await this.getOrmManager().findOne(ServiceItem, {
			where: { serviceId: id, parentItemId: null, status: Not('Voided') },
			order: { id: 'DESC' },
		})

		if (!serviceItem) {
			throw new Error('当前服务已终止或正在订单跟进中, 不允许直接修改！')
		}

		return {
			...service,
			serviceItem,
		}
	}

	@CoolTransaction()
	async updateService(body, runner?: QueryRunner) {
		const { username } = this.ctx.admin
		const { serviceItem, id, ...service } = body

		await runner.manager.update(ServiceEntity, id, { ...service, updateBy: username })
		await runner.manager.update(ServiceItem, serviceItem.id, { ...serviceItem, updateBy: username })

		if (service.cdWhServiceId) {
			const queryBuilder = whmcsCdSql.builder()
			const {
				billStatus,
				terminateDate,
				firstActiveDate,
				serviceNo,
				primaryIp,
				serviceDesc,
				serviceStatus,
				billAmount,
				dueDate,
				nextInvoiceDate,
			} = service as ServiceEntity

			// 判断OPS服务是否终止，如果终止且终止日期有效，则同步更新WHMCS，否则不更新
			let terminationDate
			if (billStatus === 'Terminated' && terminateDate !== '1000-01-01' && terminateDate !== null) {
				terminationDate = terminateDate
			} else {
				terminationDate = undefined
			}

			await queryBuilder
				.update('tblhosting')
				.set({
					regdate: firstActiveDate,
					domain: serviceNo + '#' + (primaryIp ?? serviceDesc),
					domainstatus: serviceStatus,
					amount: billAmount,
					billingcycle: serviceItem.billCycle,
					nextduedate: dueDate,
					nextinvoicedate: nextInvoiceDate,
					...(terminationDate && { termination_date: terminationDate }),
				})
				.where('id = :id', { id: service.cdWhServiceId })
				.execute()

			// 更新服务的自定义字段（此处通过API方式更新会更简单）
			const updateClientProductInfo: Record<string, any> = {
				serviceid: service.cdWhServiceId,
				customfields: whmcsCustomFieldsFormat(service.serviceType, {
					location: serviceItem.location,
					serviceDetails: serviceItem.serviceDetails,
				}),
			}

			await whmcsCdApi.update('UpdateClientProduct', updateClientProductInfo)
		}
	}

	@CoolTransaction()
	async createService(body, runner?: QueryRunner) {
		const { username } = this.ctx.admin

		const { serviceItem, ...service } = body

		// 1. 获取客户信息
		const customer = await runner.manager.findOne(CustomerEntity, service.customerId)

		// 2. 获取新增service.serviceNo
		const { serviceNo, serviceNo2 } = await this.getServiceNo(customer.id)

		const { raw } = await runner.manager.insert(ServiceEntity, {
			location: serviceItem.location,
			...service,
			serviceNo,
			serviceNo2,
			createBy: username,
			updateBy: username,
		})

		await runner.manager.insert(ServiceItem, {
			...serviceItem,
			serviceId: raw.insertId,
			createBy: username,
			updateBy: username,
		})

		return raw
	}

	// 更新子服务
	@CoolTransaction()
	async updateSubService(params, runner?: QueryRunner) {
		const { username } = this.ctx.admin || {}
		const { subservice } = params
		const originServiceOption = await this.serviceOptionRepository.findOne(subservice.id)

		if (!originServiceOption) {
			throw new Error('Service Option Not Exist.')
		}

		const serviceOption = _.mergeWith(
			{},
			originServiceOption,
			this.convertToServiceOptions({ ...subservice, updateBy: username }),
			(objValue, srcValue) => {
				if (_.isArray(objValue)) return srcValue // 进行数组替换
				return undefined // 使用默认合并逻辑
			}
		)

		await runner.manager.update(ServiceOption, subservice.id, serviceOption)
	}

	// 获取服务的子服务
	async getServiceOptions(serviceId: number) {
		// const subentity = await this.getSubserviceEntity(serviceType)
		// const prevSubservice = await subentity.find({ serviceId })
		// const service = await this.serviceEntity.findOne(serviceId)
		// const result = prevSubservice
		// 	// .filter((item) => item.orderId !== orderId)  // 过滤掉当前订单的子服务
		// 	.map((item) => ({ ...item, serviceNo: service.serviceNo }))
		// 	.sort((a, b) => b.id - a.id)

		const result = await this.serviceOptionRepository
			.createQueryBuilder('a')
			.select('a.*')
			.addSelect('b.serviceNo serviceNo')
			.addSelect('b.serviceNo2 serviceNo2')
			.leftJoin(Service, 'b', 'b.id = a.serviceId')
			.where('a.serviceId = :serviceId', { serviceId })
			.orderBy('a.id', 'DESC')
			.getRawMany()

		return result
	}

	// 返回服务编号
	async getServiceNo(customerId: number) {
		let serviceNo: string
		let serviceNo2: string

		const { customerNo, customerCode } = await this.customerEntity.findOne(customerId)

		const serviceList = await this.serviceEntity.find({ customerId })

		if (serviceList.length === 0) {
			serviceNo = `${customerNo}-${'1'.padStart(3, '0')}`
			serviceNo2 = `${customerCode}-${'1'.padStart(3, '0')}`
		} else {
			const nos: number[] = serviceList.map((item) => {
				return +item.serviceNo.split('-')[1]?.trim().replace(/^0+/, '')
			})

			const max = Math.max(...nos)
			serviceNo = `${customerNo}-${(max + 1).toString().padStart(3, '0')}`
			serviceNo2 = `${customerCode}-${(max + 1).toString().padStart(3, '0')}`
		}

		return { serviceNo, serviceNo2 }
	}

	// 获取服务及外部平台过期账单数量
	async pageServiceAndCdInvoice(params) {
		const {
			customerId,
			cdWhClientId,
			dueDate,
			page = 1,
			size = 20,
			currency = 'USD',
			billStatus,
			order = 'dueDate',
			sort = 'ASC',
			keyWord,
		} = params

		const serviceQb = this.serviceEntity.createQueryBuilder('a')
		serviceQb
			.select([
				'a.*',
				'b.customerNo customerNo',
				'b.alias customerAlias',
				'b.platform customerPlatform',
				'b.cdWhClientId cdWhClientId',
				'COUNT(*) OVER() total',
			])
			.leftJoin(CustomerEntity, 'b', 'a.customerId = b.id')
			.where(
				new Brackets((b) => {
					if (customerId) b.orWhere('b.id = :cid', { cid: customerId })
					if (cdWhClientId) {
						b.orWhere('b.cdWhClientId = :wid', { wid: cdWhClientId })
					}

					return b
				})
			)
			.andWhere('a.billCurrency in (:currency)', {
				currency: _.isArray(currency) ? currency : currency.split(','),
			})

		if (billStatus) {
			serviceQb.andWhere('a.billStatus in (:billStatus)', {
				billStatus: _.isArray(billStatus) ? billStatus : billStatus.split(','),
			})
		}

		if (Array.isArray(dueDate) && dueDate.length === 2) {
			const [startDate, endDate] = dueDate

			serviceQb.andWhere(
				'CAST(:startDate AS DATE) <= CAST(a.dueDate AS DATE) && CAST(a.dueDate AS DATE) <= CAST(:endDate AS DATE)',
				{ startDate, endDate }
			)
		}

		if (keyWord) {
			serviceQb.andWhere(
				new Brackets((qb) => {
					qb.where('b.customerNo LIKE :keyWord', {
						keyWord: `%${keyWord}%`,
					})
						.orWhere('b.alias LIKE :keyWord', { keyWord: `%${keyWord}%` })
						.orWhere('a.serviceNo LIKE :keyWord', { keyWord: `%${keyWord}%` })
				})
			)
		}

		serviceQb.limit(size).offset((page - 1) * size)

		if (order && sort) {
			serviceQb.orderBy(`a.${order}`, sort)
		}

		const serviceRes = await serviceQb.getRawMany<ServiceEntity & { total: string }>()

		const serviceCdIds = serviceRes
			.map((service) => service.cdWhServiceId)
			.filter((cdId) => cdId !== null)
			.join(',')

		const cdInvoiceQb = whmcsCdSql.builder()
		const cdInvoiceRes = await cdInvoiceQb
			.select([
				'relserviceid',
				"SUM(CASE WHEN status = 'unpaid' THEN 1 ELSE 0 END) AS unpaidCount",
				"SUM(CASE WHEN status = 'unpaid' THEN item_amount ELSE 0 END) AS unpaidAmount",
				'COUNT(*) totalNo',
			])
			.from('view_invoice_items', 'a')
			.where('a.relserviceid <> 0')
			.andWhere('a.status = "Unpaid" OR a.status = "Paid"')
			.andWhere('find_in_set(a.relserviceid, :serviceCdIds)', { serviceCdIds })
			.groupBy('a.relserviceid')
			.orderBy('a.relserviceid')
			.getRawMany()

		const serviceResult = serviceRes.map((service) => {
			const cdInvoices = cdInvoiceRes.find((invoice) => service.cdWhServiceId === invoice.relserviceid)

			return {
				...service,
				...(cdInvoices ?? {}),
				daysDifference: calculateDaysDifference(service.dueDate, getCurrentDate()),
			}
		})

		const total = serviceResult.length > 0 ? Number(serviceResult[0].total) : 0
		const list = _.chain(serviceResult)
			.map((item) => _.omit(item, ['total']))
			.value()

		return {
			list,
			pagination: {
				total,
				page,
				size,
			},
		}
	}

	// 生成账单
	@CoolTransaction()
	async generateInvoice(params, runner?: QueryRunner) {
		const message = new Message()

		const { serviceIds } = params

		const out_invoiceid = '@outputValue'
		const out_invoiceNo = '@outputValue'
		const out_message = '@outputValue'

		// 调用存储过程
		await runner.query(`CALL sp_invoice_gen_for_services(?, ${out_invoiceid}, ${out_invoiceNo}, ${out_message})`, [
			serviceIds.join(','),
		])

		// 获取输出
		const newInvoiceIds = await runner.query(`SELECT ${out_invoiceid} AS outputValue;`)

		const newInvoiceNos = await runner.query(`SELECT ${out_invoiceNo} AS outputValue;`)

		const message_ = await runner.query(`SELECT ${out_message} AS outputValue;`)

		return message.success({
			data: { newInvoiceIds, message: message_, newInvoiceNos },
			message: newInvoiceNos[0].outputValue,
		})
	}

	// 自动生成账单任务
	async taskGenerateInvoice(params?) {
		const { intervalDay = '5' } = params || {}

		// 1.获取所有billStatus=Active 及 nextInvoiceDate <= currentDate + 5 days 及 客户不存在cdWhClientId等外部客户ID 的 services,
		const services = await this.serviceEntity
			.createQueryBuilder('s')
			.select(['s.id id', 's.customerId customerId', 's.billCurrency billCurrency'])
			.leftJoin(CustomerEntity, 'c', 's.customerId = c.id')
			.where('s.billStatus = "Active"')
			.andWhere('c.cdWhClientId IS NULL')
			.andWhere('s.billCycle <> "Free"')
			.andWhere(`s.nextInvoiceDate <= DATE_ADD(CURDATE(), INTERVAL ${intervalDay} DAY)`)
			.andWhere("s.nextInvoiceDate >= '2000-01-01'")
			.getRawMany()

		if (!services.length) return '没有需要生成账单的服务'

		// 2.1 先以客户进行分组
		// 2.2 每个客户中再以币种进行分组
		// 2.3 转化为 { customerId: [[serviceId, ...], ...], customerId: [[serviceId, ...], ...], ... }
		const customerServiceMap = _.chain(services)
			.groupBy('customerId')
			.mapValues((item) => {
				return _.chain(item)
					.groupBy('billCurrency')
					.mapValues((item) => _.map(item, 'id'))
					.value()
			})
			.mapValues((item) => _.values(item))
			.value()

		// 2.以客户为单位逐个客户处理，调用存储过程sp_invoice_gen_for_services为该客户到期的serviceIds生成账单
		const promises = _.map(customerServiceMap, async (serviceIds2d /* ,customerId */) => {
			const ps = serviceIds2d.map(async (serviceIds) => {
				return (await this.generateInvoice({ serviceIds }))?.message
			})

			return Promise.all(ps)
		})

		const results = await Promise.all(promises)
		return _.chain(results).flatten().compact().value()
	}

	// 将旧格式参数转化为Service option
	convertToServiceOptions(subservice: Record<string, any>) {
		const mapping = new Map<string, string>([
			['id', 'id'],
			['serviceId', 'serviceId'],
			['orderId', 'orderId'],
			['productId', 'productId'],
			['networkProductId', 'networkProductId'],
			['description', 'description'],
			['serviceDetails', 'serviceDetails'],
			['type', 'type'],
			['status', 'status'],
			['location', 'location'],
			['billStatus', 'billStatus'],
			['billCycle', 'billCycle'],
			['billCurrency', 'billCurrency'],
			['billAmount', 'billAmount'],
			['burstPrice', 'burstPrice'],
			['activeDate', 'activeDate'],
			['terminateDate', 'terminateDate'],
			['billStartDate', 'billStartDate'],
			['billStopDate', 'billStopDate'],
			['notes', 'notes'],
			['createBy', 'createBy'],
			['updateBy', 'updateBy'],
			['type', 'type'],
		])

		const serviceOption: any = {}

		for (const [v, k] of mapping) {
			if (subservice[v]) serviceOption[k] = subservice[v]
		}

		serviceOption.optionValues = _.chain(subservice)
			.mapValues((v, k) => (!mapping.get(k) ? v : undefined))
			.pickBy((v) => !_.isUndefined(v))
			.value()

		return serviceOption as ServiceOptionEntity
	}

	convertToSubService(selectOption) {
		return {
			..._.omit(selectOption, ['optionValues']),
			...selectOption.optionValues,
		}
	}
}

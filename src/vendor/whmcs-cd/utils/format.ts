import { base64_encode, isExistValue, serialize } from '../../../utils'
import { isCompute, isNetwork, isDatacenter, isCustom } from '/$/service/utils/types'

export function whmcsPidFormat(serviceType: string) {
	if (isCompute(serviceType)) {
		return 43
	} else if (isNetwork(serviceType)) {
		return 44
	} else if (isDatacenter(serviceType)) {
		return 45
	} else if (isCustom(serviceType)) {
		return 46
	} else {
		throw new Error(`Service type error! Type: ${serviceType}, Address: ${__filename}`)
	}
}

/**
 *
 *
 * @param serviceType 服务类型 ’Server‘ ｜ ’Circuit‘ ｜ ’Colocation‘ ｜ ’Custom‘
 * @param customFieldVals [c1, c2], c2会进行 base64(serialize(val)) 处理
 * @param isLog 是否打印 customFields
 * @returns
 */
export function whmcsCustomFieldsFormat(
	serviceType: string,
	customFieldVals: { location: string; serviceDetails: string }
) {
	const { location, serviceDetails } = customFieldVals

	const c1 = location
	const c2 = serviceDetails

	if (!c1) {
		throw new Error('WHMCS Custom Fields error location is not value')
	}

	if (!c2) {
		throw new Error('WHMCS Custom Fields error serviceDetails is not value')
	}

	let customFields = undefined

	if (isCompute(serviceType)) {
		customFields = { 74: c1, 75: c2 }
	} else if (isNetwork(serviceType)) {
		customFields = { 77: c1, 78: c2 }
	} else if (isDatacenter(serviceType)) {
		customFields = { 80: c1, 81: c2 }
	} else if (isCustom(serviceType)) {
		customFields = { 82: c1, 83: c2 }
	} else {
		throw new Error(`Service type error! Type: ${serviceType}, Address: ${__filename}`)
	}

	return base64_encode(serialize(customFields))
}

import { adsRequest } from './utils'

export default {
	/**
	 * 分页
	 */
	async page(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipAddress/page',
			data: body,
		})
	},

	/**
	 * 添加
	 */
	async add(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipAddress/add',
			data: body,
		})
	},

	/**
	 * 修改
	 */
	async update(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipAddress/update',
			data: body,
		})
	},

	/**
	 * 批量修改
	 */
	async batchUpdate(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipAddress/batch/update',
			data: body,
		})
	},

	/**
	 * 删除
	 */
	async delete(ids: Array<number>) {
		return await adsRequest.post({
			url: '/sync/ops/ipAddress/delete',
			data: { ids },
		})
	},

	/**
	 * 详情
	 */
	async info(id: number) {
		return await adsRequest.get({
			url: '/sync/ops/ipAddress/info',
			params: { id },
		})
	},

	/**
	 * 从新同步 OPS IP 地址信息
	 */
	async resynchronize() {
		return await adsRequest.post({
			url: '/sync/ops/ipAddress/resynchronize',
		})
	},
}

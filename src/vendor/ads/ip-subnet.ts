import { adsRequest } from './utils'

export default {
	/**
	 * 分页
	 */
	async page(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipSubnet/page',
			data: body,
		})
	},

	/**
	 * 添加
	 */
	async add(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipSubnet/add',
			data: body,
		})
	},

	/**
	 * 修改
	 */
	async update(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipSubnet/update',
			data: body,
		})
	},

	/**
	 * 批量修改
	 */
	async batchUpdate(body: any) {
		return await adsRequest.post({
			url: '/sync/ops/ipSubnet/batch/update',
			data: body,
		})
	},

	/**
	 * 删除
	 */
	async delete(ids: Array<number>) {
		return await adsRequest.post({
			url: '/sync/ops/ipSubnet/delete',
			data: { ids },
		})
	},

	/**
	 * 详情
	 */
	async info(id: number) {
		return await adsRequest.get({
			url: '/sync/ops/ipSubnet/info',
			params: { id },
		})
	},

	/**
	 * 从新同步 OPS IP段 信息
	 */
	async resynchronize() {
		return await adsRequest.post({
			url: '/sync/ops/ipSubnet/resynchronize',
		})
	},
}

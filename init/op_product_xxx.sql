-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.9.5deb2
-- https://www.phpmyadmin.net/
--
-- 主机： localhost:3306
-- 生成日期： 2024-04-02 09:40:12
-- 服务器版本： 8.0.36-0ubuntu0.20.04.1
-- PHP 版本： 7.4.3-4ubuntu2.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `cool-ops-dev`
--

-- --------------------------------------------------------

--
-- 表的结构 `op_product`
--
DROP TABLE IF EXISTS `op_product`;
CREATE TABLE `op_product` (
  `id` int NOT NULL COMMENT 'ID',
  `productNo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `parentProductId` int DEFAULT NULL COMMENT '关联产品',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '产品名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '产品描述',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '产品类型',
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '产品分类',
  `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '地址',
  `createBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------
-- 转存表中的数据 `op_product`
--

INSERT INTO `op_product` (`id`, `productNo`, `parentProductId`, `name`, `description`, `type`, `category`, `location`, `createBy`, `createTime`, `updateBy`, `updateTime`) VALUES
(100, 'PD10000', NULL, 'Dedicated', 'Dedicated Baremetal Server', 'Product', 'Server', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:36:41.508863'),
(101, 'PD10001', NULL, 'IPT CNO', 'IPT China Optimized', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:36:46.835725'),
(102, 'PD10002', NULL, 'IPT GLO', 'IPT Global Optimized', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(103, 'PD10003', NULL, 'IPT CND', 'IPT China Direct', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(104, 'PD10004', NULL, 'ADS', 'AntiDDoS', 'Product', '', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(105, 'PD10005', NULL, 'Colocation', 'Colocation', 'Product', 'Colocation', 'ALL', '', '2023-07-21 16:58:57.000000', 'admin', '2024-03-28 11:37:15.194961'),
(106, 'PD10006', NULL, 'Custom', 'Custom', 'Product', 'Custom', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(108, 'PD10007', NULL, 'VPS', 'VPS Server', 'Product', 'Server', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(109, 'PD10008', NULL, 'Xconnect', 'Xconnect', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', 'admin', '2024-03-28 11:37:15.194961'),
(110, 'PD10009', NULL, 'DIA', 'Dedicated Internet Access', 'Product', 'DIA', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(112, 'PD10010', NULL, 'IPT GLB', 'IPT Global', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(113, 'PD10011', NULL, 'IPT Custom', 'IPT Custom', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(114, 'PD10012', NULL, 'IPT CSO', 'Customer-owned Network', 'Product', 'Circuit', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961'),
(115, 'PD10013', 100, 'Additional Stroage', 'Additional Stroage/额外增加硬盘', 'Addon', 'Server', 'ALL', '', '2023-07-21 16:58:57.000000', '', '2024-03-28 11:37:15.194961');

--
--
-- 表的结构 `op_product_option`
--
DROP TABLE IF EXISTS `op_product_option`;
CREATE TABLE `op_product_option` (
  `id` int NOT NULL COMMENT 'ID',
  `productId` int NOT NULL COMMENT '产品ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '产品选项名称',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '状态，Active/Terminated',
  `optionValues` json NOT NULL COMMENT '产品选项值',
  `notes` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `createBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `op_product_price`
--
DROP TABLE IF EXISTS `op_product_price`;
CREATE TABLE `op_product_price` (
  `id` int NOT NULL COMMENT 'ID',
  `planId` int DEFAULT NULL COMMENT '关联套餐ID',
  `productOptionId` int DEFAULT NULL COMMENT '关联产品选项ID',
  `type` enum('Unit Price','Plan Price') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类别，单价/套餐价',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '状态，Valid/Expired',
  `priceTier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '价格等级',
  `currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '币种',
  `basePrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '基础价格', 
  `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '报价单位',
  `qty` int NOT NULL DEFAULT '0' COMMENT '数量',
  `discountRate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠百分比',
  `finalPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最终价格',
  `productOptionValues` varchar(2550) NOT NULL DEFAULT '' COMMENT '惨选项值',
  `validFrom` date DEFAULT NULL COMMENT '有效起始日',
  `validTo` date DEFAULT NULL COMMENT '有效截止日',
  `terms` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '适用条件',
  `notes` varchar(2550) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `visibleRoleId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '可见角色ID，多个以逗号分割',
  `createBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateBy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `op_product_price_plan`
--
DROP TABLE IF EXISTS `op_product_price_plan`;
CREATE TABLE `op_product_price_plan` (
  `id` int NOT NULL COMMENT 'ID',
  `planNo` varchar(255) NOT NULL DEFAULT '' COMMENT '方案号',
  `planName` varchar(255) NOT NULL DEFAULT '' COMMENT '方案名称',
  `validFrom` date DEFAULT NULL COMMENT '生效开始日',
  `validTo` date DEFAULT NULL COMMENT '生效到期日',
  `note` varchar(2550) NOT NULL DEFAULT '' COMMENT '备注',
  `planPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '套餐价格',
  `planTerms` varchar(2550) NOT NULL DEFAULT '' COMMENT '套餐条款',
  `createBy` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateBy` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `op_product_quote`
--
DROP TABLE IF EXISTS `op_product_quote`;
CREATE TABLE `op_product_quote` (
  `id` int NOT NULL COMMENT 'ID',
  `quoteNo` varchar(255) NOT NULL COMMENT '报价编号',
  `customerId` int DEFAULT NULL COMMENT '关联客户ID',
  `firstName` varchar(255) NOT NULL COMMENT '客户姓',
  `lastName` varchar(255) NOT NULL COMMENT '客户名',
  `companyName` varchar(255) NOT NULL COMMENT '公司名称',
  `email` varchar(255) NOT NULL COMMENT '客户邮箱',
  `stage` enum('Draft','Delivered','Accepted','Lost') NOT NULL DEFAULT 'Draft' COMMENT '报价阶段',
  `quoteDate` date NOT NULL COMMENT '报价发送日期',
  `validFrom` date DEFAULT NULL COMMENT '生效开始日',
  `validTo` date DEFAULT NULL COMMENT '生效到期日',
  `currency` varchar(255) NOT NULL COMMENT '货币',
  `untaxedTotal` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '不含税总金额',
  `taxedTotal` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '含税总额',
  `customerNotes` text NOT NULL COMMENT '客户备注',
  `adminNotes` text NOT NULL COMMENT '管理员备注',
  `attachments` varchar(500) NOT NULL DEFAULT '' COMMENT '附件',
  `visibleRoleIds` text NOT NULL COMMENT '可见角色ID',
  `createBy` varchar(255) NOT NULL COMMENT '创建者',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateBy` varchar(255) NOT NULL COMMENT '更新者',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- 表的结构 `op_product_quote_item`
--
DROP TABLE IF EXISTS `op_product_quote_item`;
CREATE TABLE `op_product_quote_item` (
  `id` int NOT NULL COMMENT 'ID',
  `quoteId` int DEFAULT NULL COMMENT '关联报价单ID',
  `productOptionId` int DEFAULT NULL COMMENT '关联产品选项ID',
  `description` varchar(255) NOT NULL COMMENT '产品描述',
  `qty` int NOT NULL COMMENT '数量',
  `unit` varchar(255) NOT NULL COMMENT '单位',
  `unitPrice` decimal(10,2) NOT NULL COMMENT '单价',
  `subTotal` decimal(10,2) NOT NULL COMMENT '小计',
  `createBy` varchar(50) DEFAULT NULL COMMENT '创建者',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateBy` varchar(50) DEFAULT NULL COMMENT '更新者',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- 转储表的索引
--

--
-- 表的索引 `op_product`
--
ALTER TABLE `op_product`
  ADD PRIMARY KEY (`id`),
  ADD KEY `IDX_4ec842d4292458e1cb85873633` (`createTime`),
  ADD KEY `IDX_d28adf36a83108c8ae6ae47bd2` (`updateTime`),
  ADD KEY `FK_0d1026201387517acaccc363ce2` (`parentProductId`);

--
-- 表的索引 `op_product_option`
--
ALTER TABLE `op_product_option`
  ADD PRIMARY KEY (`id`),
  ADD KEY `IDX_e642fa8ad6b98c74c759a2453d` (`createTime`),
  ADD KEY `IDX_b2cf62b1cc789e11b1f2fafdfd` (`updateTime`),
  ADD KEY `FK_857b8516d97bbf7513224b17b6b` (`productId`);

--
-- 表的索引 `op_product_price`
--
ALTER TABLE `op_product_price`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK_productPrice_planId` (`planId`),
  ADD KEY `FK_productPrice_productOptionId` (`productOptionId`);

--
-- 表的索引 `op_product_price_plan`
--
ALTER TABLE `op_product_price_plan`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `planNo` (`planNo`);

--
-- 表的索引 `op_product_quote`
--
ALTER TABLE `op_product_quote`
  ADD PRIMARY KEY (`id`),
  ADD KEY `IDX_d3e79bfd2d186bb924d01c6194` (`createTime`),
  ADD KEY `IDX_37370fd573867d0cae18fd0b89` (`updateTime`),
  ADD KEY `FK_9858e22f27cb3c3f26dc8498ad1` (`customerId`);

--
-- 表的索引 `op_product_quote_item`
--
ALTER TABLE `op_product_quote_item`
  ADD PRIMARY KEY (`id`),
  ADD KEY `IDX_4458ce9db609e18ada29307d31` (`createTime`),
  ADD KEY `IDX_14da4dab6178303abe01810f24` (`updateTime`),
  ADD KEY `FK_b656ca98ee9972cbeca90577c3c` (`quoteId`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `op_product`
--
ALTER TABLE `op_product`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `op_product_option`
--
ALTER TABLE `op_product_option`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `op_product_price_plan`
--
ALTER TABLE `op_product_price_plan`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `op_product_quote`
--
ALTER TABLE `op_product_quote`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 使用表AUTO_INCREMENT `op_product_quote_item`
--
ALTER TABLE `op_product_quote_item`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID';

--
-- 限制导出的表
--

--
-- 限制表 `op_product`
--
ALTER TABLE `op_product`
  ADD CONSTRAINT `FK_0d1026201387517acaccc363ce2` FOREIGN KEY (`parentProductId`) REFERENCES `op_product` (`id`);

--
-- 限制表 `op_product_option`
--

--
-- 限制表 `op_product_price`
--
ALTER TABLE `op_product_price`
  ADD CONSTRAINT `FK_productPrice_planId` FOREIGN KEY (`planId`) REFERENCES `op_product_price_plan` (`id`) ON DELETE SET NULL ON UPDATE SET NULL,
  ADD CONSTRAINT `FK_productPrice_productOptionId` FOREIGN KEY (`productOptionId`) REFERENCES `op_product_option` (`id`) ON DELETE SET NULL ON UPDATE SET NULL;

--
-- 限制表 `op_product_quote`
--
ALTER TABLE `op_product_quote`
  ADD CONSTRAINT `FK_9858e22f27cb3c3f26dc8498ad1` FOREIGN KEY (`customerId`) REFERENCES `op_customer` (`id`);

--
-- 限制表 `op_product_quote_item`
--
ALTER TABLE `op_product_quote_item`
  ADD CONSTRAINT `FK_b656ca98ee9972cbeca90577c3c` FOREIGN KEY (`quoteId`) REFERENCES `op_product_quote` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;


ALTER TABLE `cool-ops-dev`.`op_product_quote_item` DROP FOREIGN KEY `FK_3e1b8aa15578c6a49b1eb774588`;
ALTER TABLE `cool-ops-dev`.`op_service_circuit` DROP FOREIGN KEY `FK_6a447de30edb2801a0d0ef056a7`;
ALTER TABLE `cool-ops-dev`.`op_service_circuit` DROP FOREIGN KEY `FK_ec1b3256db05fb14904102bf3c2`;
ALTER TABLE `cool-ops-dev`.`op_service_colocation` DROP FOREIGN KEY `FK_1fbbf4526ecb0e3d4dcfd323559`;
ALTER TABLE `cool-ops-dev`.`op_service_custom` DROP FOREIGN KEY `FK_4000313ff063282c1184a5a1404`;
ALTER TABLE `cool-ops-dev`.`op_service_iptransit` DROP FOREIGN KEY `FK_159b59bbd13c1d2c39967f6d8c3`;
ALTER TABLE `cool-ops-dev`.`op_service_iptransit` DROP FOREIGN KEY `FK_89cbe8219a0901d74a297eaf25d`;
ALTER TABLE `cool-ops-dev`.`op_service_option` DROP FOREIGN KEY `FK_3c9352b11c28d10435be45d7d48`;
ALTER TABLE `cool-ops-dev`.`op_service_option` DROP FOREIGN KEY `FK_75b99d45c029c8f0eaeabef2e4d`;
ALTER TABLE `cool-ops-dev`.`op_service_server` DROP FOREIGN KEY `FK_847ba44b4250dcf993323eca53a`;
ALTER TABLE `cool-ops-dev`.`op_service_server` DROP FOREIGN KEY `FK_b945d7d99461aec287f137faac7`;
ALTER TABLE `cool-ops-dev`.`op_tec_ipsubnet` DROP FOREIGN KEY `FK_913ae7d2f7f8a58fba0aed22d13`;


ALTER TABLE `cool-ops-dev`.`op_product_quote_item` ADD CONSTRAINT `FK_3e1b8aa15578c6a49b1eb774588` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `cool-ops-dev`.`op_service_circuit` ADD CONSTRAINT `FK_6a447de30edb2801a0d0ef056a7` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_circuit` ADD CONSTRAINT `FK_ec1b3256db05fb14904102bf3c2` FOREIGN KEY (`networkProductId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_colocation` ADD CONSTRAINT `FK_1fbbf4526ecb0e3d4dcfd323559` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_custom` ADD CONSTRAINT `FK_4000313ff063282c1184a5a1404` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_iptransit` ADD CONSTRAINT `FK_159b59bbd13c1d2c39967f6d8c3` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `cool-ops-dev`.`op_service_iptransit` ADD CONSTRAINT `FK_89cbe8219a0901d74a297eaf25d` FOREIGN KEY (`networkProductId`) REFERENCES `op_product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
ALTER TABLE `cool-ops-dev`.`op_service_option` ADD CONSTRAINT `FK_3c9352b11c28d10435be45d7d48` FOREIGN KEY (`networkProductId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_option` ADD CONSTRAINT `FK_75b99d45c029c8f0eaeabef2e4d` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_server` ADD CONSTRAINT `FK_847ba44b4250dcf993323eca53a` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_service_server` ADD CONSTRAINT `FK_b945d7d99461aec287f137faac7` FOREIGN KEY (`networkProductId`) REFERENCES `op_product` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE `cool-ops-dev`.`op_tec_ipsubnet` ADD CONSTRAINT `FK_913ae7d2f7f8a58fba0aed22d13` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT;
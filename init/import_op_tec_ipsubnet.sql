-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_ipsubnets表，并转换为op_tec_ipsubnet表对应结构
SELECT 
 t1.id AS id,
 t2.id AS customerId,
 t3.id AS vendorId,
 CASE 
  WHEN t1.ProductCode = 'CNO' THEN 101
  WHEN t1.ProductCode = 'GLO' THEN 102
  WHEN t1.ProductCode = 'CND' THEN 103
  WHEN t1.ProductCode LIKE '%ADS%' THEN 104
  WHEN t1.ProductCode LIKE '%CNO%' THEN 101
  WHEN t1.ProductCode LIKE '%GLO%' THEN 102
  WHEN t1.ProductCode LIKE '%CND%' THEN 103
  WHEN t1.ProductCode = 'Colocation' THEN 105
  ELSE NULL 
 END AS `productId`,
 INET_ATON(SUBSTRING_INDEX(t1.SubnetC, '/', 1)) AS subnetNo,
 SUBSTRING_INDEX(t1.SubnetC, '/', 1) AS subnetStr,
 SUBSTRING_INDEX(t1.SubnetC, '/', -1) AS prefixLength,
 t1.Location AS location,
 '' AS category,
 '' AS tags,
 t1.Status AS status,
 t1.Owner AS owner,
 t1.OriginASN AS originAsn,
 t1.Upstream AS advertisedUpstreams,
 t1.AdvertiseCommunities AS advertisedCommunities,
 t1.AdvertiseFrom AS advertisedOn,
  CASE 
    WHEN t1.Remark IS NULL THEN ''
    ELSE t1.Remark
  END AS 'notes',
  t1.RIR AS icann,
  '' AS createBy,
  '2023-07-20 00:00' AS createTime,
  '' AS updateBy,
  '2023-07-20 00:00' AS updateTime
FROM tbl_ipsubnets t1
LEFT JOIN tbl_customers t2 ON t2.CustomerID = t1.Owner
LEFT JOIN tbl_vendors t3 ON t3.VendorID = t1.Owner


/*
-- 更新tbl_ipadd表中的ipsubnetId字段
UPDATE op_tec_ipadd 
INNER JOIN op_tec_ipsubnet
ON op_tec_ipsubnet.subnetStr = CONCAT(SUBSTRING_INDEX(op_tec_ipadd.ipStr, '.', 3), '.0')
SET op_tec_ipadd.ipsubnetId = op_tec_ipsubnet.id
WHERE op_tec_ipadd.ipsubnetId IS NULL;

*/

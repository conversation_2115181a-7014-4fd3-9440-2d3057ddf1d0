-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_tickets表，并转换为op_ticket表对应结构
SELECT 
 t1.id AS id,
 t2.id AS customerId,
 t3.id AS serviceId,
 CONCAT('TK', t1.id) AS ticketNo,
 t1.Type AS category,
 t1.Priority AS priority,
 t1.Status AS status,
 t1.Subject AS title,
 t1.Message AS message,
 NULL AS assignedUserId,
 1 AS assignedRoleId,
 '' AS contactName,
 '' AS contactEmail,
 '' contactPhone,
 t1.CreateBy AS createBy,
 t1.CreateAt AS createTime,
 t1.UpdateBy AS updateBy,
 t1.UpdateAt AS updateTime
FROM tbl_tickets t1
LEFT JOIN tbl_customers t2 ON t1.RelatedCustomer = t2.CustomerID
LEFT JOIN tbl_services t3 ON t1.RelatedService = t3.ServiceID


/*
UPDATE `tbl_tickets` 
SET 
`AssignedStaff` = COALESCE(`AssignedStaff`, ''),
`RelatedCustomer` = COALESCE(`RelatedCustomer`, ''),
`RelatedService` = COALESCE(`RelatedService`, ''),
`InvolvedTeams` = COALESCE(`InvolvedTeams`, ''),
`CreateBy` = COALESCE(`CreateBy`, '');

*/
-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_bills表，并转换为op_invoice表对应结构
SELECT 
 t1.id AS id,
 REPLACE(t1.BillID, 'BI', 'INV') AS invoiceNo,
 t2.id AS customerId,
 t1.BillDesc AS description,
 t1.BillCurrency AS currency,
 t1.BillAmount AS totalAmount,
 t3.id AS transManualId,
 CASE 
  WHEN t1.BillStatus = '已支付' THEN 'Paid'
  WHEN t1.BillStatus = '待支付' THEN 'Unpaid'
  WHEN t1.BillStatus = '部分支付' THEN 'Partially Paid'
  WHEN t1.BillStatus = '' THEN 'Draft'
  ELSE 'Draft' 
 END AS `status`,
 t1.BillDate AS issueDate,
 t1.BillDueDate AS dueDate,
 t1.BillPaidDate AS paidDate,
 t1.BillRemark AS notes,
 1 AS visibleRoleId,
 '' AS attachments,
 t1.CreateBy AS createBy,
 t1.CreateDate AS createDate,
 CASE WHEN t1.UpdateBy IS NULL THEN t1.CreateBy ELSE t1.UpdateBy END AS updateBy,
 CASE WHEN t1.UpdateTime IS NULL THEN t1.CreateDate ELSE t1.CreateDate END AS updateTime
FROM tbl_bills t1
LEFT JOIN tbl_customers t2 ON t1.RelCustomerID = t2.CustomerID
LEFT JOIN tbl_trans t3 ON t1.RelTranID = t3.TranID
WHERE BillType = 'Invoice'


/*
-- 在导出数据前先把varchar字段的NULL值替换为空字符串（因为ops3数据库的varchar字段不允许NULL值）
UPDATE tbl_bills
SET 
BillDesc = IFNULL(BillDesc, ''),
BillCurrency = IFNULL(BillCurrency, ''),
BillStatus = IFNULL(BillStatus, ''),
BillVdInvoiceID = IFNULL(BillVdInvoiceID, ''),
BillRemark = IFNULL(BillRemark, ''),
RelTranID = IFNULL(RelTranID, ''),
RelOpexID = IFNULL(RelOpexID, ''),
RelServiceID = IFNULL(RelServiceID, ''),
RelAcctID = IFNULL(RelAcctID, ''),
RelVendorID = IFNULL(RelVendorID, ''),
RelCustomerID = IFNULL(RelCustomerID, ''),
CreateBy = IFNULL(CreateBy, ''),
UpdateBy = IFNULL(UpdateBy, '')
*/
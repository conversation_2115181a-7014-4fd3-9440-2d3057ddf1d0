-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一一对应
-- 导出tbl_customer表，并转换为op_customer表对应结构
SELECT 
    id,
    CustomerID AS customerNo,
    ShortName AS alias,
    CompanyName AS company,
    CompanyName AS firstName,
    '' AS lastName,
    Email AS email,
    '' AS customerType,
    '' AS tier,
    REPLACE(REPLACE(Platform, 'Global', 'CD'), 'GLOBAL', 'CD') AS platform,
    RegisterDate AS registerDate,
    CDWHMCSID AS cdWhClientId,
    CNWHMCSID AS cnWhClientId,
    NULL AS accountManagerId,
    NULL AS salesManagerId,
    '1,19,20,21' AS visiableRoleId,
    '' AS notes,
    '' AS createBy,
    CreateTime AS createTime,
    '' AS updateBy,
    UpdateTime AS updateTime
FROM `tbl_customers` ORDER BY `id`  DESC  
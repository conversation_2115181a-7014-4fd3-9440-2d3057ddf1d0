-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_vendors表，并转换为op_vendor表对应结构
SELECT 
 t1.id AS id,
 t1.VendorID AS vendorNo,
 t1.VendorAlias AS alias,
 t1.VendorFullName AS fullName,
 t1.VendorFullName AS companyName,
 t1.Category AS category,
 t1.ProductsOffered AS products,
 t1.Website AS website,
 'Company' AS type,
 '' AS notes,
 1 AS visibleRoleId,
 'Sam Gu' AS createBy,
 '2023-07-24 00:00' AS createTime,
 'Sam Gu' AS updateBy,
 '2023-07-24 00:00' AS updateTime
FROM tbl_vendors t1
-- 删除存储过程
DROP PROCEDURE IF EXISTS `sp_invoice_gen_for_services`;

DELIMITER //

CREATE PROCEDURE sp_invoice_gen_for_services(p_serviceIds TEXT, OUT out_invoiceId INT, OUT out_invoiceNo VARCHAR(255), OUT out_resultMessage VARCHAR(255))
early_exit: BEGIN
/*
======================================================================
存储过程名称：sp_invoice_gen_for_services

更新时间：2024-06-04 13:10, by <PERSON>

描述：
此存储过程旨在为指定的一组服务生成发票。

操作流程：
1. 验证所给的服务ID，确保它们存在且相关。
2. 确保所有服务都属于同一客户并使用相同的计费货币，如果不同则终止并返回错误信息。
3. 利用fn_custom_no_gen函数生成一个自定义的发票编号。
4. 创建一个新的Invoice记录，其总金额先设为0。
5. 将这些服务添加为Invoice Item，计算它们的各自费用和到期日期。
6. 更新Invoice的总金额。
7. 根据其计费周期为每个服务更新下一个发票日期。
8. 返回创建的Invoice的ID、编号以及状态消息。

======================================================================
*/
    DECLARE v_newInvoiceId INT;
    DECLARE v_customInvoiceNo VARCHAR(255);
    DECLARE v_billCurrency VARCHAR(255);
    DECLARE v_customerId INT;
    DECLARE v_customerNo VARCHAR(255);
    DECLARE v_minNextInvoiceDate DATE;
    DECLARE v_validServicesCount INT;
    DECLARE v_customersCount INT;
    DECLARE v_billCurrencyCount INT;
    DECLARE v_freeBillCycleCount INT;


    -- 开启事务处理
    START TRANSACTION;

    -- 确保传入的p_serviceIds参数不为空或者不只包含无效的id
    SELECT COUNT(*) INTO v_validServicesCount FROM op_service WHERE FIND_IN_SET(id, p_serviceIds);
    IF p_serviceIds IS NULL OR v_validServicesCount = 0 THEN
        SET out_resultMessage = 'Error: Invalid service IDs provided.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    END IF;

    -- 检查这些服务是否同属于一个客户
    SELECT COUNT(DISTINCT customerId) INTO v_customersCount FROM op_service WHERE FIND_IN_SET(id, p_serviceIds);
    IF v_customersCount > 1 THEN
        SET out_resultMessage = 'Error: Provided services do not belong to the same customer.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    END IF;

    -- 检查这些服务是否属于同一个billCurrency
    SELECT COUNT(DISTINCT billCurrency) INTO v_billCurrencyCount FROM op_service WHERE FIND_IN_SET(id, p_serviceIds);

    IF v_billCurrencyCount > 1 THEN
        SET out_resultMessage = 'Error: Provided services do not have the same bill currency.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    ELSE
        SELECT DISTINCT billCurrency INTO v_billCurrency FROM op_service WHERE FIND_IN_SET(id, p_serviceIds);
    END IF;

    -- 检查是否有billCycle为Free的服务
    SELECT COUNT(*) INTO v_freeBillCycleCount FROM op_service WHERE FIND_IN_SET(id, p_serviceIds) AND billCycle = 'Free';
    IF v_freeBillCycleCount > 0 THEN
        SET out_resultMessage = 'Error: One or more services have a billing cycle of Free.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    END IF;

    -- 获取客户ID、客户编号、最早到期日
    SELECT customerId, MIN(nextInvoiceDate) INTO v_customerId, v_minNextInvoiceDate FROM op_service WHERE FIND_IN_SET(id, p_serviceIds) GROUP BY customerId;

    -- 获取客户编号
    SELECT customerNo INTO v_customerNo FROM op_customer WHERE id = v_customerId;


    -- 获取自定义发票编号
    SET v_customInvoiceNo = fn_custom_no_gen('invoice');

    -- 创建新发票
    INSERT INTO op_invoice (invoiceNo, description, totalAmount, currency, customerId, status, dueDate, issueDate, createBy) 
    VALUES (v_customInvoiceNo, CONCAT(v_customerNo, ' Invoice ', DATE_FORMAT(v_minNextInvoiceDate, '%Y/%m/%d')), 0, v_billCurrency, v_customerId, 'Unpaid', v_minNextInvoiceDate, v_minNextInvoiceDate, 'OPS');

    SET v_newInvoiceId = LAST_INSERT_ID();

    -- 添加服务项到发票项
    INSERT INTO op_invoice_item (invoiceId, serviceId, primaryIp,itemDetails, billCycle, fromDate, toDate, itemAmount, taxRate,  dueDate, createBy)
    SELECT 
        v_newInvoiceId AS invoiceId, 
        s.id AS serviceId,
        s.primaryIp, 
        CONCAT(
            s.serviceNo, 
            IFNULL(CONCAT('#', NULLIF(s.primaryIp, '')), ''), 
            '#', 
            s.serviceDesc, 
            '\nPeriod: ', 
            DATE_FORMAT(s.nextInvoiceDate, '%Y/%m/%d'), 
            ' - ', 
            DATE_FORMAT(v_toDate, '%Y/%m/%d')
        ) AS itemDetails,
        s.billCycle AS billCycle,
        s.nextInvoiceDate AS fromDate,
        v_toDate AS toDate,
        s.billAmount AS itemAmount,
        s.taxRate AS taxRate,
        s.nextInvoiceDate AS dueDate,
        'OPS' AS createBy
    FROM (
        SELECT *,
            CASE 
                WHEN billCycle = 'Monthly' THEN DATE_SUB(DATE_ADD(nextInvoiceDate, INTERVAL 1 MONTH), INTERVAL 1 DAY)
                WHEN billCycle = 'Quarterly' THEN DATE_SUB(DATE_ADD(nextInvoiceDate, INTERVAL 3 MONTH), INTERVAL 1 DAY)
                WHEN billCycle = 'Semi-Annually' THEN DATE_SUB(DATE_ADD(nextInvoiceDate, INTERVAL 6 MONTH), INTERVAL 1 DAY)
                WHEN billCycle = 'Annually' THEN DATE_SUB(DATE_ADD(nextInvoiceDate, INTERVAL 1 YEAR), INTERVAL 1 DAY)
                ELSE nextInvoiceDate
            END as v_toDate
        FROM op_service
    ) s
    WHERE FIND_IN_SET(s.id, p_serviceIds);


    -- 更新总金额
    UPDATE op_invoice 
    SET totalAmount = (SELECT SUM(itemAmount) FROM op_invoice_item WHERE invoiceId = v_newInvoiceId)
    WHERE id = v_newInvoiceId;

    -- 把服务的nextInvoiceDate延期1个周期
    UPDATE op_service SET nextInvoiceDate = CASE billCycle
        WHEN 'Monthly' THEN DATE_ADD(nextInvoiceDate, INTERVAL 1 MONTH)
        WHEN 'Quarterly' THEN DATE_ADD(nextInvoiceDate, INTERVAL 3 MONTH)
        WHEN 'Semi-Annually' THEN DATE_ADD(nextInvoiceDate, INTERVAL 6 MONTH)
        WHEN 'Annually' THEN DATE_ADD(nextInvoiceDate, INTERVAL 1 YEAR)
        ELSE nextInvoiceDate
    END WHERE FIND_IN_SET(id, p_serviceIds);


    -- 提交事务
    COMMIT;

    -- 设置输出参数
    SET out_invoiceId = v_newInvoiceId;
    SET out_invoiceNo = v_customInvoiceNo;
    SET out_resultMessage = CONCAT('Success: invoice (', v_customInvoiceNo, ') has been generated.');
END //

DELIMITER ;


-- 测试方式：
-- CALL sp_invoice_gen_for_services('1,2,3,4,5', @out_invoiceId, @out_invoiceNo, @out_resultMessage); 
-- SELECT @out_invoiceId, @out_invoiceNo, @out_resultMessage;
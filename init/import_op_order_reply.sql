-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一一对应
-- 导出tbl_orderlogs表，并转换为op_order_reply表对应结构
SELECT 
 t1.id,
 t2.id AS orderId,
 t3.id AS userId,
 '' AS currentAction,
 '' AS currentOrderStatus,
 '' AS newOrderStatus,
 t1.LogDetail AS replyMessage,
 CASE 
  WHEN t1.UpdateBy IS NULL THEN ''
  WHEN t1.UpdateBy = 'null' THEN ''
  ELSE t1.UpdateBy 
 END AS createBy,
 t1.LogDate AS createTime,
 CASE 
  WHEN t1.UpdateBy IS NULL THEN ''
  WHEN t1.UpdateBy = 'null' THEN ''
  ELSE t1.UpdateBy 
 END AS updateBy,
 t1.LogDate AS updateTime
FROM tbl_orderlogs t1
JOIN tbl_orders t2 ON t2.OrderNum = t1.RelOrderNum
LEFT JOIN tbl_users t3 ON t3.FullName = t1.UpdateBy
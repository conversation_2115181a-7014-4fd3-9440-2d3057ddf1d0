-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_trans表，并转换为op_trans_manual表对应结构
SELECT 
 t1.id AS id,
 t2.id AS customerId,
 t3.id AS vendorId,
 t1.TranID AS tranNo,
 t1.TranDate AS tranDate,
  CASE 
    WHEN t1.TranInOut = '收入' THEN 'Money In'
    WHEN t1.TranInOut  = '支出' THEN 'Money Out'
    WHEN t1.TranInOut  = '转账' THEN 'Transfer'
    ELSE ''
  END AS 'tranType',
  t4.AcctCurrency AS tranCurrency,
  CASE 
    WHEN t1.TranInOut = '收入' THEN 59
    WHEN t1.TranInOut  = '支出' THEN 64
    ELSE NULL
  END AS 'currentAccountId',
  t1.TranOursAmount AS currentAccountAmount,
  t1.TranFee AS tranFee,
  '' AS tranGateway,
  t1.TranDesc AS description,
  
  
FROM tbl_trans t1 
LEFT JOIN tbl_customers t2 ON t1.RelCustomerID = t2.CustomerID
LEFT JOIN tbl_vendors t3 ON t1.RelVendorID = t3.VendorID
LEFT JOIN tbl_accounts t4 ON t1.AcctOurs = t4.AcctID
LEFT JOIN tbl_accounts t5 ON t1.AcctOthers = t5.AcctID
DELIMITER //

CREATE FUNCTION fn_custom_no_gen(p_type VARCHAR(255)) 
RETURNS VARCHAR(255) 
DETERMINISTIC 
READS SQL DATA
BEGIN
/*
-- 创建一个名为fn_custom_no_gen的函数，用于生成一个自定义的编号。
-- 这个编号是基于传入的p_type参数从conf_count表中选取的前缀和当前计数来生成的。
-- 每次函数被调用时，对应类型的计数都会增加1，以确保编号的唯一性。
-- 这个函数锁定了选取的行以防止并发的事务干扰编号生成过程。
*/
    DECLARE cur_count INT;
    DECLARE cur_prefix VARCHAR(255);
    DECLARE new_no VARCHAR(255);

    -- 选取对应类型的当前计数和前缀，并锁定该行以防止其他事务干扰
    SELECT count, prefix INTO cur_count, cur_prefix FROM conf_count WHERE type = p_type FOR UPDATE;

    -- 将计数器加1
    SET cur_count = cur_count + 1;

    -- 生成自定义编号
    SET new_no = CONCAT(cur_prefix, cur_count);

    -- 更新计数器
    UPDATE conf_count SET count = cur_count WHERE type = p_type;

    RETURN new_no;
END //

DELIMITER ;

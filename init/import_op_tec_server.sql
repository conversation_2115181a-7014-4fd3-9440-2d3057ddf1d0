-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_servers表，并转换为op_tec_server表对应结构
SELECT 
 t1.id AS id,
 t1.ServerID AS serverNo,
 NULL AS equipmentId,
 t3.id AS customerId,
 t2.id AS serviceId,
 t1.ServerLabel AS label,
 t1.ChassisID AS chassisId,
 t1.NodeID AS nodeId,
 CASE 
  WHEN t1.Status = '已下架' THEN 'Stocked'
  WHEN t1.Status = '保留的' THEN 'Reserved'
  WHEN t1.Status = '可分配' THEN 'Available'
  WHEN t1.Status = '故障的' THEN 'Faulty'
  WHEN t1.Status = '已分配' THEN 'Assigned'
  ELSE 'Available' 
 END AS `status`,
 CASE 
  WHEN t1.Location IN ('NA', 'na', 'null', 'NULL', NULL) THEN ''
  ELSE t1.Location 
 END AS `location`,
 t1.RelRackID AS rack,
 t1.ServerOwner AS owner,
 t1.UsageDesc AS `usage`,
 '' AS tags,
 t1.ServerBrand AS brand,
 t1.ServerModel AS model,
 CASE
  WHEN t1.ServerRU REGEXP '[^0-9]' THEN 0
  WHEN t1.ServerRU IS NULL THEN 0
  WHEN t1.ServerRU = '' THEN 0
  ELSE t1.ServerRU
 END AS rackUnit,
 t1.Series AS series,
 t1.CPU AS cpuModel,
 t1.CpuNo AS cpuNo,
 0 AS cpuCores,
 t1.MemModel AS memoryModel,
 t1.MemNo AS memoryInstalledNo,
 0 AS memoryMaxNo,
 t1.MemSize AS memorySize,
 (
   (CASE WHEN HDD1 != 'NA' THEN 1 ELSE 0 END) +
   (CASE WHEN HDD2 != 'NA' THEN 1 ELSE 0 END) +
   (CASE WHEN HDD3 != 'NA' THEN 1 ELSE 0 END) +
   (CASE WHEN HDD4 != 'NA' THEN 1 ELSE 0 END) + 
   (CASE WHEN HDD5 != 'NA' THEN 1 ELSE 0 END) + 
   (CASE WHEN HDD6 != 'NA' THEN 1 ELSE 0 END) + 
   (CASE WHEN HDD7 != 'NA' THEN 1 ELSE 0 END) + 
   (CASE WHEN HDD8 != 'NA' THEN 1 ELSE 0 END) + 
   (CASE WHEN HDD9 != 'NA' THEN 1 ELSE 0 END)  
 ) AS hddInstalledNo,
 0 AS hddMaxNo,
 CONCAT_WS(',', 
   IF(hdd1 != 'NA', hdd1, NULL),
   IF(hdd2 != 'NA', hdd2, NULL),
   IF(hdd3 != 'NA', hdd3, NULL),
   IF(hdd4 != 'NA', hdd4, NULL),
   IF(hdd5 != 'NA', hdd5, NULL),
   IF(hdd6 != 'NA', hdd6, NULL),
   IF(hdd7 != 'NA', hdd7, NULL),
   IF(hdd8 != 'NA', hdd8, NULL),
   IF(hdd9 != 'NA', hdd9, NULL)
  ) AS hdds,
 (
   (CASE WHEN NIC1 != 'NA' THEN 1 ELSE 0 END) +
   (CASE WHEN NIC2 != 'NA' THEN 1 ELSE 0 END) +
   (CASE WHEN NIC3 != 'NA' THEN 1 ELSE 0 END) +
   (CASE WHEN NIC4 != 'NA' THEN 1 ELSE 0 END) 
 ) AS nicInstalledNo,
 0 AS nicMaxNo,
 CONCAT_WS(',', 
   IF(NIC1 != 'NA', NIC1, NULL),
   IF(NIC2 != 'NA', NIC2, NULL),
   IF(NIC3 != 'NA', NIC3, NULL),
   IF(NIC4 != 'NA', NIC4, NULL)
  ) AS nics,
  t1.RAID AS raidModel,
  CASE WHEN t1.IPMIIP IS NULL THEN '' ELSE t1.IPMIIP END AS ipmiIp,
  CASE WHEN t1.IPMIPort IS NULL THEN '' ELSE t1.IPMIPort END AS ipmiAccessSwitch,
  CASE WHEN t1.IPMIPort IS NULL THEN '' ELSE t1.IPMIPort END AS ipmiAccessPort,
  CASE WHEN t1.AccessPort IS NULL THEN '' ELSE t1.AccessPort END AS serviceAccessSwitch,
  CASE WHEN t1.AccessPort IS NULL THEN '' ELSE t1.AccessPort END AS serviceAccessPort,
  NULL AS purchaseOrderId,
  CASE 
    WHEN t1.Remark IS NULL THEN ''
    ELSE t1.Remark
  END AS 'notes',
  0 AS power,
 CASE WHEN t1.CreatedBy IS NULL THEN '' ELSE t1.CreatedBy END AS createBy,
 CASE WHEN t1.CreateTime IS NULL THEN '2023-07-20 00:00' ELSE t1.CreateTime END AS createTime,
 CASE WHEN t1.UpdatedBy IS NULL THEN '' ELSE t1.UpdatedBy END AS updateBy,
 CASE WHEN t1.UpdateTime IS NULL THEN '2023-07-20 00:00' ELSE t1.UpdateTime END AS updateTime
FROM tbl_servers t1
LEFT JOIN tbl_services t2 ON t2.ServiceID = t1.RelatedService
LEFT JOIN tbl_customers t3 ON t3.CustomerID = t2.RelCustomerID
-- 为 op_trans_manual 表新增 journalStatus 字段
-- 执行日期: 2025-07-11
-- 描述: 新增记账状态字段，用于标记交易记录的记账状态

-- 新增字段
ALTER TABLE `op_trans_manual` 
ADD COLUMN `journalStatus` varchar(20) NOT NULL DEFAULT 'Pending' COMMENT '记账状态：Pending-待记账, Journaled-已记账, Classified-已分类' AFTER `cdWhTranId`;

-- 更新现有数据：如果已经有关联的 journal，则设置为 Journaled
UPDATE `op_trans_manual` m 
SET `journalStatus` = 'Journaled' 
WHERE EXISTS (
    SELECT 1 FROM `op_trans_journal` j 
    WHERE j.transManualId = m.id
);

-- 更新现有数据：如果 journal 状态为已分类，则设置为 Classified
UPDATE `op_trans_manual` m 
SET `journalStatus` = 'Classified' 
WHERE EXISTS (
    SELECT 1 FROM `op_trans_journal` j 
    WHERE j.transManualId = m.id 
    AND j.status = '已分类'
);

-- 验证字段是否添加成功
SELECT 'Field added successfully!' as status;

-- 显示表结构
DESCRIBE `op_trans_manual`;

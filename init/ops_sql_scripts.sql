-- 更新格式化服务描述，server类
UPDATE op_service_server s
JOIN op_product p ON s.networkProductId = p.id
SET s.serviceDetails = CONCAT(
    'Type: ', s.type,
    '; Net: ', p.name,
    '; BW: ', s.bandwidth, 'Mb',
    IF(s.burstBandwidth > 0, CONCAT('; Burst: ', s.burstBandwidth, 'Mb'), ''),
    IF(s.protectedBandwidth > 0, CONCAT('; Protected: ', s.protectedBandwidth, 'Gb'), ''),
    IF(s.cleanBandwidth > 0, CONCAT('; Clean: ', s.cleanBandwidth, 'Mb'), ''),
    '; IPs: ', s.ipAddress,
    '; Cores: ', s.cpuCores, 'c',
    '; Memory: ', s.memory, 'GB',
    '; Storage: ', s.storage
);


-- 更新格式化服务描述，colocation类
UPDATE op_service_colocation s
JOIN op_product p ON s.productId = p.id
SET s.serviceDetails = CONCAT(
    'Product: ', p.name,
    '; RU: ', s.rackUnit, 'U',
    '; Power: ', s.powerAmp, 'A'
);



-- 更新格式化服务描述，circuit类
UPDATE op_service_circuit s
JOIN op_product p ON s.networkProductId = p.id
SET s.serviceDetails = CONCAT(
    'Type: ', s.type,
    '; Net: ', p.name,
    '; BW: ', s.bandwidth, 'Mb',
    IF(s.burstBandwidth > 0, CONCAT('; Burst: ', s.burstBandwidth, 'Mb'), ''),
    IF(s.protectedBandwidth > 0, CONCAT('; Protected: ', s.protectedBandwidth, 'Gb'), ''),
    IF(s.cleanBandwidth > 0, CONCAT('; Clean: ', s.cleanBandwidth, 'Mb'), ''),
    '; IPs: ', s.ipAddress
);



-- 更新格式化服务描述，custom类
UPDATE op_service_custom s
JOIN op_product p ON s.productId = p.id
SET s.serviceDetails = CONCAT(
    'Type: ', s.type,
    '; BW: ', s.bandwidth, 'Mb',
    IF(s.burstBandwidth > 0, CONCAT('; Burst: ', s.burstBandwidth, 'Mb'), ''),
    IF(s.protectedBandwidth > 0, CONCAT('; Protected: ', s.protectedBandwidth, 'Gb'), ''),
    IF(s.cleanBandwidth > 0, CONCAT('; Clean: ', s.cleanBandwidth, 'Mb'), ''),
    '; IPs: ', s.ipAddress,
    '; Cores: ', s.cpuCores, 'c',
    '; Memory: ', s.memory, 'GB',
    '; Storage: ', s.storage
);


-- 更新billItem的dueDate
UPDATE op_expense_bill_item it
JOIN op_expense_bill bi ON it.billId = bi.id
SET it.dueDate = COALESCE(it.fromDate, bi.dueDate)
WHERE it.dueDate IS NULL;

-- 更新billItem的accoutId为expense的accountId
UPDATE op_expense_bill_item bii
JOIN op_expense ex ON bii.expenseId = ex.id
LEFT JOIN op_account_chart ac ON ex.chartOfAccountId = ac.id
SET bii.categorizedAccountId = ac.id
WHERE ac.id IS NOT NULL;



-- 1. op_service_server 转换为 op_service_option：
INSERT INTO op_service_option (serviceId, orderId, productId, networkProductId, description, location, status, billStatus, billCycle, billCurrency, billAmount, activeDate, terminateDate, billStartDate, billStopDate, createBy, createTime, updateBy, updateTime, optionValues, serviceDetails)
SELECT
    oss.serviceId AS serviceId,
    oss.orderId AS orderId,
    oss.productId AS productId,
    oss.networkProductId AS networkProductId,
    oss.description AS description,
    oss.location AS location,
    oss.status AS status,
    oss.billStatus AS billStatus,
    oss.billCycle AS billCycle,
    oss.billCurrency AS billCurrency,
    oss.billAmount AS billAmount,
    oss.activeDate AS activeDate,
    oss.terminateDate AS terminateDate,
    oss.billStartDate AS billStartDate,
    oss.billStopDate AS billStopDate,
    oss.createBy AS createBy,
    oss.createTime AS createTime,
    oss.updateBy AS updateBy,
    oss.updateTime AS updateTime,
    -- 将特定字段转换为 JSON 格式存储
    JSON_OBJECT(
        'cpuCores', oss.cpuCores,
        'cpuModel', oss.cpuModel,
        'memory', oss.memory,
        'storage', oss.storage,
        'os', oss.os,
        'bandwidth', oss.bandwidth,
        'burstBandwidth', oss.burstBandwidth,
        'protectedBandwidth', oss.protectedBandwidth,
        'cleanBandwidth', oss.cleanBandwidth,
        'ipAddress', oss.ipAddress
    ) AS optionValues,
    oss.serviceDetails AS serviceDetails
FROM
    op_service_server oss;


-- 2. op_service_colocation 转换为 op_service_option：
INSERT INTO op_service_option (serviceId, orderId, description, location, status, billStatus, billCycle, billCurrency, billAmount, activeDate, terminateDate, billStartDate, billStopDate, createBy, createTime, updateBy, updateTime, optionValues, serviceDetails)
SELECT
    osc.serviceId AS serviceId,
    osc.orderId AS orderId,
    osc.description AS description,
    osc.location AS location,
    osc.status AS status,
    osc.billStatus AS billStatus,
    osc.billCycle AS billCycle,
    osc.billCurrency AS billCurrency,
    osc.billAmount AS billAmount,
    osc.activeDate AS activeDate,
    osc.terminateDate AS terminateDate,
    osc.billStartDate AS billStartDate,
    osc.billStopDate AS billStopDate,
    osc.createBy AS createBy,
    osc.createTime AS createTime,
    osc.updateBy AS updateBy,
    osc.updateTime AS updateTime,
    -- 将特定字段转换为 JSON 格式存储
    JSON_OBJECT(
        'rackUnit', osc.rackUnit,
        'powerAmp', osc.powerAmp
    ) AS optionValues,
    osc.serviceDetails AS serviceDetails
FROM
    op_service_colocation osc;

-- 3. op_service_circuit 转换为 op_service_option：
INSERT INTO op_service_option (serviceId, orderId, productId, networkProductId, description, location, status, billStatus, billCycle, billCurrency, billAmount, activeDate, terminateDate, billStartDate, billStopDate, createBy, createTime, updateBy, updateTime, optionValues, serviceDetails)
SELECT
    osc.serviceId AS serviceId,
    osc.orderId AS orderId,
    osc.productId AS productId,
    osc.networkProductId AS networkProductId,
    osc.description AS description,
    osc.location AS location,
    osc.status AS status,
    osc.billStatus AS billStatus,
    osc.billCycle AS billCycle,
    osc.billCurrency AS billCurrency,
    osc.billAmount AS billAmount,
    osc.activeDate AS activeDate,
    osc.terminateDate AS terminateDate,
    osc.billStartDate AS billStartDate,
    osc.billStopDate AS billStopDate,
    osc.createBy AS createBy,
    osc.createTime AS createTime,
    osc.updateBy AS updateBy,
    osc.updateTime AS updateTime,
    -- 将特定字段转换为 JSON 格式存储
    JSON_OBJECT(
        'bandwidth', osc.bandwidth,
        'burstBandwidth', osc.burstBandwidth,
        'protectedBandwidth', osc.protectedBandwidth,
        'cleanBandwidth', osc.cleanBandwidth,
        'ipAddress', osc.ipAddress,
        'asn', osc.asn,
        'prefixs', osc.prefixs,
        'asset', osc.asset,
        'accessRouter', osc.accessRouter,
        'accessSwitch', osc.accessSwitch,
        'circuitInfo', osc.circuitInfo
    ) AS optionValues,
    osc.serviceDetails AS serviceDetails
FROM
    op_service_circuit osc;


-- 4. op_service_custom 转换为 op_service_option：
INSERT INTO op_service_option (serviceId, orderId, description, location, status, billStatus, billCycle, billCurrency, billAmount, activeDate, terminateDate, billStartDate, billStopDate, createBy, createTime, updateBy, updateTime, serviceDetails)
SELECT
    osc.serviceId AS serviceId,
    osc.orderId AS orderId,
    osc.description AS description,
    osc.location AS location,
    osc.status AS status,
    osc.billStatus AS billStatus,
    osc.billCycle AS billCycle,
    osc.billCurrency AS billCurrency,
    osc.billAmount AS billAmount,
    osc.activeDate AS activeDate,
    osc.terminateDate AS terminateDate,
    osc.billStartDate AS billStartDate,
    osc.billStopDate AS billStopDate,
    osc.createBy AS createBy,
    osc.createTime AS createTime,
    osc.updateBy AS updateBy,
    osc.updateTime AS updateTime,
    osc.serviceDetails AS serviceDetails
FROM
    op_service_custom osc;

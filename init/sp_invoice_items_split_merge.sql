DROP PROCEDURE IF EXISTS `sp_invoice_items_split_merge`;

DELIMITER //
CREATE PROCEDURE sp_invoice_items_split_merge(
    IN p_itemIdsToMerge TEXT,     -- 需要合并的invoice item的ID列表，逗号分隔
    IN opUser VARCHAR(255),     -- 操作用户
    OUT out_newInvoiceId VARCHAR(255),       -- 新生成的invoice id
    OUT out_newInvoiceNo VARCHAR(255),       -- 新生成的invoice no
    OUT out_resultMessage VARCHAR(1024)  -- 结果信息
)
early_exit: BEGIN
    -- 更新时间：2024-10-18 14:50, by Sam
    DECLARE v_chargedInvoiceCount INT DEFAULT 0;
    DECLARE v_chargedInvoiceNos TEXT DEFAULT '';  -- 有过扣费记录的invoice编号列表
    DECLARE v_paidInvoiceCount INT DEFAULT 0;
    DECLARE v_paidInvoiceNos TEXT DEFAULT '';
    DECLARE v_totalAmount DECIMAL(10,2) DEFAULT 0.00;
    DECLARE v_sameCustomer INT DEFAULT 0;
    DECLARE v_sameCurrency INT DEFAULT 0;
    DECLARE v_customerId INT;
    DECLARE v_customerNo VARCHAR(255);
    DECLARE v_currency VARCHAR(255);
    DECLARE v_newInvoiceId VARCHAR(255);
    DECLARE v_customInvoiceNo VARCHAR(255);
    DECLARE v_earliestDueDate DATE;
    DECLARE v_itemInvoiceIds TEXT DEFAULT "";
    DECLARE v_error_message VARCHAR(255);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 获取最近的报错信息
        GET DIAGNOSTICS CONDITION 1
        @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @text = MESSAGE_TEXT;

        ROLLBACK;

        -- 把报错信息返回给输出变量
        SET out_resultMessage = CONCAT("Error ", @errno, ": ", @text);
    END;

    -- 获取被拆分的items对应的invoice ids供后面使用
    SELECT GROUP_CONCAT(DISTINCT invoiceId) 
    INTO v_itemInvoiceIds
    FROM op_invoice_item 
    WHERE FIND_IN_SET(id, p_itemIdsToMerge);

    -- 检查被分拆的账单是否有过扣费记录且paidAmount>0，如果有则不允许分拆
    SELECT COUNT(*), GROUP_CONCAT(DISTINCT inv.invoiceNo)
    INTO v_chargedInvoiceCount, v_chargedInvoiceNos
    FROM op_trans_manual_invoice AS tr
    JOIN op_invoice AS inv ON tr.invoiceId = inv.id
    WHERE FIND_IN_SET(tr.invoiceId, v_itemInvoiceIds) AND inv.paidAmount > 0;


    IF v_chargedInvoiceCount > 0 THEN
        SET out_resultMessage = CONCAT('错误：以下账单有过扣费记录，不允许进行分拆：', v_chargedInvoiceNos);
        ROLLBACK;
        LEAVE early_exit;
    END IF;

    -- 检查被分拆的账单paidAmount是否大于0，如果大于0则不允许分拆
    SELECT COUNT(*), GROUP_CONCAT(DISTINCT inv.invoiceNo)
    INTO v_paidInvoiceCount, v_paidInvoiceNos
    FROM op_invoice AS inv
    WHERE FIND_IN_SET(id, v_itemInvoiceIds) AND paidAmount > 0;


    IF v_paidInvoiceCount > 0 THEN
        SET out_resultMessage = CONCAT('错误：以下账单有过扣费，不允许进行分拆：', v_paidInvoiceNos);
        ROLLBACK;
        LEAVE early_exit;
    END IF;

    START TRANSACTION;

    -- 1. 检查所有传入的invoice item是否属于同一个客户且币种相同
    -- 1.1 先检查p_itemIdsToMerge是否属于同一个客户且币种相同
    SELECT COUNT(DISTINCT inv.customerId), COUNT(DISTINCT inv.currency)
    INTO v_sameCustomer, v_sameCurrency
    FROM op_invoice_item AS item
    JOIN op_invoice AS inv ON item.invoiceId = inv.id
    WHERE FIND_IN_SET(item.id, p_itemIdsToMerge);

    IF v_sameCustomer > 1 OR v_sameCurrency > 1 THEN
        SET out_resultMessage = '错误：invoice items不属于同一个客户或币种不同';
        ROLLBACK;
        LEAVE early_exit;
    END IF;

    -- 1.2 现在安全地获取customerId、currency和customerNo
    SELECT DISTINCT inv.customerId, inv.currency, cust.customerNo
    INTO v_customerId, v_currency, v_customerNo
    FROM op_invoice_item AS item
    JOIN op_invoice AS inv ON item.invoiceId = inv.id
    JOIN op_customer AS cust ON inv.customerId = cust.id
    WHERE FIND_IN_SET(item.id, p_itemIdsToMerge)
    LIMIT 1;

    -- 2. 插入新的记录到op_invoice
    -- 2.1 首先获取自定义账单编号
    SET v_customInvoiceNo = fn_custom_no_gen('invoice');
    SET out_newInvoiceNo = v_customInvoiceNo;
    

    -- 2.2 获取最早的duedate供后面使用
    SELECT MIN(dueDate)
    INTO v_earliestDueDate
    FROM op_invoice_item
    WHERE FIND_IN_SET(id, p_itemIdsToMerge);

    -- 2.3 插入新的记录到op_invoice
    INSERT INTO op_invoice(invoiceNo, customerId, description, status, issueDate, dueDate, currency, createBy, updateBy)
    VALUES(v_customInvoiceNo, v_customerId, CONCAT(v_customerNo, ' Invoice ', DATE_FORMAT(v_earliestDueDate, '%Y/%m/%d')), 'Unpaid', v_earliestDueDate, v_earliestDueDate, v_currency, opUser, opUser);

    -- 2.4 获取新插入的invoice id供后面使用
    SET v_newInvoiceId = LAST_INSERT_ID();
    SET out_newInvoiceId = LAST_INSERT_ID();

    -- 3. 更新需要拆分的invoice items的invoiceId
    UPDATE op_invoice_item
    SET invoiceId = v_newInvoiceId
    WHERE FIND_IN_SET(id, p_itemIdsToMerge);

    -- 4. 更新op_invoice中的totalAmount为所有invoice item的itemAmount总和
    SELECT SUM(itemAmount) INTO v_totalAmount
    FROM op_invoice_item
    WHERE invoiceId = v_newInvoiceId;

    UPDATE op_invoice
    SET totalAmount = v_totalAmount
    WHERE id = v_newInvoiceId;

    -- 5. 更新被拆分的invoice items对应的invoice的totalAmount
    UPDATE op_invoice oi
    INNER JOIN (SELECT invoiceId, SUM(itemAmount) AS total FROM op_invoice_item GROUP BY invoiceId) it ON oi.id = it.invoiceId
    SET oi.totalAmount = it.total;

    -- 6. 检查被拆分的invoice items对应的invoice是否还有其它invoice items，如果没有则删除该invoice
    DELETE oinv
    FROM op_invoice oinv
    LEFT JOIN op_invoice_item oitem ON oinv.id = oitem.invoiceId
    WHERE FIND_IN_SET(oinv.id, v_itemInvoiceIds) 
    AND oitem.invoiceId IS NULL;

    -- 7. 返回结果
    SET out_resultMessage = CONCAT('Success: items ', p_itemIdsToMerge, ' merged to ', v_customInvoiceNo);

    COMMIT;
END //
DELIMITER ;


-- 测试方法：
-- CALL sp_invoice_items_split_merge('1,2,3', 'opUser1', @out_newInvoiceId, @out_newInvoiceNo, @out_resultMessage);
-- SELECT @out_newInvoiceId, @out_newInvoiceNo, @out_resultMessage;
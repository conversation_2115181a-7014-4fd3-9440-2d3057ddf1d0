-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_users表，并转换为base_sys_user表对应结构
SELECT 
 t1.id AS id,
 1 AS departmentId,
 t1.FullName AS name,
 LOWER(REPLACE(t1.FullName, ' ', '')) AS username,
 'e10adc3949ba59abbe56e057f20f883e' AS `password`,
 1 AS passwordV,
 FullName AS nickName,
 NULL AS headImg,
 '' AS phone,
 Email as email,
 1 AS status,
 '' AS remark,
 NULL AS socketId,
 '2023-07-20 00:00' AS createTime,
 '2023-07-20 00:00' AS updateTime
FROM tbl_users t1
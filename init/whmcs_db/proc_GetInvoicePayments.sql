DELIMITER //

CREATE PROCEDURE GetPaymentsByInvoiceId(IN targetInvoiceId INT)
BEGIN
    -- 获取特定账单的所有付款记录
    -- 第一个查询
    SELECT 
        invoiceid,
        (amountin - amountout) as amount, 
        `date`, 
        description, 
        id as accountid, 
        NULL as creditid,
        userid as clientid
    FROM `tblaccounts` 
    WHERE invoiceid = targetInvoiceId

    UNION ALL

    -- 第二个查询
    SELECT 
        CAST(TRIM(LEADING 'Invoice #' FROM REGEXP_SUBSTR(description, CONCAT('Invoice #', targetInvoiceId))) AS UNSIGNED) AS invoiceid,
        amount,
        `date`, 
        description,  
        NULL as accountid,
        id as creditid, 
        clientid
    FROM `tblcredit` 
    WHERE description LIKE CONCAT('%Invoice #', targetInvoiceId, '%');

END //

DELIMITER ;

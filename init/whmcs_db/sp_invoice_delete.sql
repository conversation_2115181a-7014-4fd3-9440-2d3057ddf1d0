DROP PROCEDURE IF EXISTS `sp_invoice_delete`;

DELIMITER //

CREATE PROCEDURE `sp_invoice_delete`(
    IN invoiceIds TEXT,
    IN deleteInvoiceItems BOOLEAN DEFAULT FALSE,
    IN opUser VARCHAR(255),
    OUT deletedInvoiceIds TEXT
)
BEGIN
    DECLARE v_invoiceId INT;
    DECLARE finished INT DEFAULT 0;
    DECLARE invoices CURSOR FOR SELECT id FROM tblinvoices WHERE FIND_IN_SET(id, invoiceIds);
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = 1;

    SET deletedInvoiceIds = '';

    OPEN invoices;

    deletion_loop: LOOP
        FETCH invoices INTO v_invoiceId;
        IF finished = 1 THEN 
            LEAVE deletion_loop;
        END IF;

        -- 检查credit记录中是否含有invoiceId
        IF (SELECT COUNT(*) FROM tblcredit WHERE description REGEXP CONCAT('Invoice #', v_invoiceId)) > 0 THEN
            ITERATE deletion_loop;
        END IF;

        -- 检查付款记录
        IF (SELECT COUNT(*) FROM tblaccounts WHERE invoiceid = v_invoiceId) > 0 THEN
            ITERATE deletion_loop;
        END IF;

        -- 检查账单支付状态
        IF (SELECT COUNT(*) FROM tblinvoices WHERE id = v_invoiceId AND (status = 'Paid' OR credit > 0)) > 0 THEN
            ITERATE deletion_loop;
        END IF;

        -- 删除账单项
        IF deleteInvoiceItems THEN
            DELETE FROM tblinvoiceitems WHERE invoiceid = v_invoiceId;
        END IF;

        -- 删除账单
        DELETE FROM tblinvoices WHERE id = v_invoiceId;

        -- 记录已删除的invoiceId
        SET deletedInvoiceIds = CONCAT_WS(',', deletedInvoiceIds, v_invoiceId);

        -- 插入删除日志
        INSERT INTO tblactivitylog (`date`,`description`,`user`,`userid`,`ipaddr`,`user_id`,`admin_id`) 
        VALUES (NOW(), CONCAT('Invoice Deleted: Invoice ID ', v_invoiceId, ' deleted by ', opUser), opUser, clientId, '127.0.0.1', '0', '1');

    END LOOP;

    CLOSE invoices;
END //

DELIMITER ;

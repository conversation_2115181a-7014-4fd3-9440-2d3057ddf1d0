DROP PROCEDURE IF EXISTS `sp_cancel_invalid_invoices`;

DELIMITER //

CREATE PROCEDURE `sp_cancel_invalid_invoices`(
    IN p_cdWhServiceId INT,
    IN p_billStopDate DATE,
    OUT out_cancelledInvoiceIds TEXT,
    OUT out_resultMessage VARCHAR(1024)
)
BEGIN
    -- 更新时间：2023-11-29 11:04, by Sam
    -- 该存储过程的作用是检查指定服务早于billStopDate的Invoice Item
    -- 然后按需分拆并执行Cancel操作
    DECLARE v_invoiceItemId INT;
    DECLARE v_invoiceId INT;
    DECLARE v_itemCount INT;
    DECLARE finished INT DEFAULT 0;
    -- 定义游标：获取无效的账单
    DECLARE invoiceItems CURSOR FOR 
        SELECT id, invoiceid 
        FROM tblinvoiceitems 
        WHERE relid = p_cdWhServiceId 
        AND duedate >= p_billStopDate
        AND EXISTS (SELECT 1 FROM tblinvoices WHERE id = invoiceid AND status = 'Unpaid');
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = 1;

    SET out_cancelledInvoiceIds = '';

    OPEN invoiceItems;

    cancel_loop: LOOP
        FETCH invoiceItems INTO v_invoiceItemId, v_invoiceId;
        IF finished = 1 THEN 
            LEAVE cancel_loop;
        END IF;

        -- 检查关联的invoice是否存在多条invoice items
        SELECT COUNT(*) INTO v_itemCount FROM tblinvoiceitems WHERE invoiceid = v_invoiceId;

        IF v_itemCount = 1 THEN
            -- 如果只有1条，直接更新tblinvoices表的状态为Cancelled
            UPDATE tblinvoices SET status = 'Cancelled', notes = CONCAT(notes, ' Cancelled due to service stop on ', p_billStopDate) WHERE id = v_invoiceId;

            -- 记录已取消的invoiceId
            SET out_cancelledInvoiceIds = TRIM(LEADING ',' FROM CONCAT_WS(',', out_cancelledInvoiceIds, v_invoiceId));
        ELSE
            -- 调用SplsitMergeInvoiceItem拆分invoice item
            CALL SplitMergeInvoiceItems(CONCAT(v_invoiceItemId), 'System', @newInvoiceId, @splitResultMessage);

            -- 更新拆分出的invoice的状态为Cancelled
            UPDATE tblinvoices SET status = 'Cancelled', notes = CONCAT(notes, ' Cancelled due to service stop on ', p_billStopDate) WHERE id = @newInvoiceId;

            -- 记录已取消的invoiceId，并移除前导逗号
            SET out_cancelledInvoiceIds = TRIM(LEADING ',' FROM CONCAT_WS(',', out_cancelledInvoiceIds, v_invoiceId));
        END IF;

        -- 插入取消日志
        INSERT INTO tblactivitylog (`date`,`description`,`user`,`userid`,`ipaddr`,`user_id`,`admin_id`) 
        VALUES (NOW(), CONCAT('Invoice Cancelled, Invoice ID: ', v_invoiceId, ' cancelled due to service stop on ', p_billStopDate), 'System', 0, '127.0.0.1', '0', '1');

    END LOOP;

    CLOSE invoiceItems;

    IF out_cancelledInvoiceIds = '' THEN
        SET out_resultMessage = 'No invalid invoices found for the provided service ID and bill stop date.';
    ELSE
        SET out_resultMessage = CONCAT('Cancelled invoices: ', out_cancelledInvoiceIds);
    END IF;
END //

DELIMITER ;


-- CALL sp_cancel_invalid_invoices(1234, '2023-01-01', @out_cancelledInvoiceIds, @out_resultMessage);
-- SELECT @out_cancelledInvoiceIds, @out_resultMessage;
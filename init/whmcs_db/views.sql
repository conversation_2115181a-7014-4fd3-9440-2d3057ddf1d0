-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- 主机： localhost:3306
-- 生成日期： 2023-09-26 00:56:44
-- 服务器版本： 8.0.34
-- PHP 版本： 8.1.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `cloudddos_whmcs01`
--

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_clients`
-- （参见下面的实际视图）
--
CREATE TABLE `view_clients` (
`id` int
,`firstname` text
,`lastname` text
,`companyname` text
,`email` text
,`country` text
,`datecreated` date
,`currency` int
,`startdate` blob
,`expdate` blob
,`status` enum('Active','Inactive','Closed')
,`language` text
,`service_total` bigint
,`service_active` bigint
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_invoices`
-- （参见下面的实际视图）
--
CREATE TABLE `view_invoices` (
`svc_id` int
,`service_id` text
,`service_status` enum('Pending','Active','Suspended','Terminated','Cancelled','Fraud','Completed')
,`reg_date` date
,`termination_date` date
,`invoiceid` int
,`item_id` int
,`userid` int
,`relserviceid` int
,`item_amount` decimal(16,2)
,`date` date
,`duedate` date
,`inv_status` text
,`paymentmethod` text
,`item_description` text
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_invoice_export`
-- （参见下面的实际视图）
--
CREATE TABLE `view_invoice_export` (
`invoiceid` int
,`item_id` int
,`userid` int
,`type` varchar(30)
,`relserviceid` int
,`service_id` mediumtext
,`item_description` text
,`item_amount` decimal(16,2)
,`date` date
,`duedate` date
,`datepaid` datetime
,`date_refunded` timestamp
,`date_cancelled` timestamp
,`subtotal` decimal(16,2)
,`credit` decimal(16,2)
,`status` text
,`paymentmethod` text
,`created_at` timestamp
,`updated_at` timestamp
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_invoice_items`
-- （参见下面的实际视图）
--
CREATE TABLE `view_invoice_items` (
`invoiceid` int
,`item_id` int
,`userid` int
,`type` varchar(30)
,`relserviceid` int
,`item_description` text
,`item_amount` decimal(16,2)
,`date` date
,`duedate` date
,`datepaid` datetime
,`date_refunded` timestamp
,`date_cancelled` timestamp
,`subtotal` decimal(16,2)
,`credit` decimal(16,2)
,`status` text
,`paymentmethod` text
,`created_at` timestamp
,`updated_at` timestamp
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_invoice_sum`
-- （参见下面的实际视图）
--
CREATE TABLE `view_invoice_sum` (
`id` int
,`paid_amount` decimal(40,2)
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_products`
-- （参见下面的实际视图）
--
CREATE TABLE `view_products` (
`id` int
,`userid` int
,`orderid` int
,`packageid` int
,`companyname` text
,`server` int
,`regdate` date
,`domain` text
,`dedicatedip` text
,`amount` decimal(16,2)
,`currency` int
,`billingcycle` text
,`nextduedate` date
,`nextinvoicedate` date
,`termination_date` date
,`domainstatus` enum('Pending','Active','Suspended','Terminated','Cancelled','Fraud','Completed')
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_services`
-- （参见下面的实际视图）
--
CREATE TABLE `view_services` (
`id` int
,`userid` int
,`orderid` int
,`packageid` int
,`companyname` text
,`server` int
,`regdate` date
,`domain` text
,`dedicatedip` text
,`amount` decimal(16,2)
,`currency` int
,`billingcycle` text
,`nextduedate` date
,`nextinvoicedate` date
,`termination_date` date
,`domainstatus` enum('Pending','Active','Suspended','Terminated','Cancelled','Fraud','Completed')
,`created_at` timestamp
,`updated_at` timestamp
);

-- --------------------------------------------------------

--
-- 替换视图以便查看 `view_trans`
-- （参见下面的实际视图）
--
CREATE TABLE `view_trans` (
`id` int
,`userid` int
,`currency` int
,`gateway` text
,`date` datetime
,`description` text
,`amountin` decimal(16,2)
,`fees` decimal(16,2)
,`amountout` decimal(16,2)
,`rate` decimal(16,5)
,`transid` text
,`invoiceid` int
,`refundid` int
,`companyname` text
,`email` text
);

-- --------------------------------------------------------

--
-- 视图结构 `view_clients`
--
DROP TABLE IF EXISTS `view_clients`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_clients`  AS SELECT `C`.`id` AS `id`, `C`.`firstname` AS `firstname`, `C`.`lastname` AS `lastname`, `C`.`companyname` AS `companyname`, `C`.`email` AS `email`, `C`.`country` AS `country`, `C`.`datecreated` AS `datecreated`, `C`.`currency` AS `currency`, `C`.`startdate` AS `startdate`, `C`.`expdate` AS `expdate`, `C`.`status` AS `status`, `C`.`language` AS `language`, count(0) AS `service_total`, count(((`H`.`domainstatus` = 'Active') or (0 <> NULL))) AS `service_active` FROM (`tblclients` `C` left join `tblhosting` `H` on((`C`.`id` = `H`.`userid`))) GROUP BY `C`.`id` ;

-- --------------------------------------------------------

--
-- 视图结构 `view_invoices`
--
DROP TABLE IF EXISTS `view_invoices`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_invoices`  AS SELECT `svc`.`id` AS `svc_id`, `svc`.`domain` AS `service_id`, `svc`.`domainstatus` AS `service_status`, `svc`.`regdate` AS `reg_date`, `svc`.`termination_date` AS `termination_date`, `item`.`invoiceid` AS `invoiceid`, `item`.`id` AS `item_id`, `item`.`userid` AS `userid`, `item`.`relid` AS `relserviceid`, `item`.`amount` AS `item_amount`, `inv`.`date` AS `date`, `inv`.`duedate` AS `duedate`, `inv`.`status` AS `inv_status`, `inv`.`paymentmethod` AS `paymentmethod`, `item`.`description` AS `item_description` FROM ((`tblinvoiceitems` `item` join `tblinvoices` `inv` on((`item`.`invoiceid` = `inv`.`id`))) join `tblhosting` `svc` on((`svc`.`id` = `item`.`relid`))) WHERE ((`inv`.`status` = 'Paid') OR (`inv`.`status` = 'Unpaid')) ORDER BY `item`.`invoiceid` DESC ;

-- --------------------------------------------------------

--
-- 视图结构 `view_invoice_export`
--
DROP TABLE IF EXISTS `view_invoice_export`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_invoice_export`  AS SELECT `item`.`invoiceid` AS `invoiceid`, `item`.`id` AS `item_id`, `item`.`userid` AS `userid`, `item`.`type` AS `type`, `item`.`relid` AS `relserviceid`, upper(regexp_substr(`item`.`description`,'CD[[:digit:]]{4,5}-[[:digit:]]{3}')) AS `service_id`, `item`.`description` AS `item_description`, `item`.`amount` AS `item_amount`, `inv`.`date` AS `date`, `item`.`duedate` AS `duedate`, `inv`.`datepaid` AS `datepaid`, `inv`.`date_refunded` AS `date_refunded`, `inv`.`date_cancelled` AS `date_cancelled`, `inv`.`subtotal` AS `subtotal`, `inv`.`credit` AS `credit`, `inv`.`status` AS `status`, `inv`.`paymentmethod` AS `paymentmethod`, `inv`.`created_at` AS `created_at`, `inv`.`updated_at` AS `updated_at` FROM (`tblinvoiceitems` `item` left join `tblinvoices` `inv` on((`item`.`invoiceid` = `inv`.`id`))) ORDER BY `item`.`invoiceid` DESC ;

-- --------------------------------------------------------

--
-- 视图结构 `view_invoice_items`
--
DROP TABLE IF EXISTS `view_invoice_items`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_invoice_items`  AS SELECT `item`.`invoiceid` AS `invoiceid`, `item`.`id` AS `item_id`, `item`.`userid` AS `userid`, `item`.`type` AS `type`, `item`.`relid` AS `relserviceid`, `item`.`description` AS `item_description`, `item`.`amount` AS `item_amount`, `inv`.`date` AS `date`, `item`.`duedate` AS `duedate`, `inv`.`datepaid` AS `datepaid`, `inv`.`date_refunded` AS `date_refunded`, `inv`.`date_cancelled` AS `date_cancelled`, `inv`.`subtotal` AS `subtotal`, `inv`.`credit` AS `credit`, `inv`.`status` AS `status`, `inv`.`paymentmethod` AS `paymentmethod`, `inv`.`created_at` AS `created_at`, `inv`.`updated_at` AS `updated_at` FROM (`tblinvoiceitems` `item` join `tblinvoices` `inv` on((`item`.`invoiceid` = `inv`.`id`))) ORDER BY `item`.`invoiceid` DESC ;

-- --------------------------------------------------------

--
-- 视图结构 `view_invoice_sum`
--
DROP TABLE IF EXISTS `view_invoice_sum`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_invoice_sum`  AS SELECT `i`.`id` AS `id`, (coalesce((select (sum(`tblaccounts`.`amountin`) - sum(`tblaccounts`.`amountout`)) from `tblaccounts` where (`tblaccounts`.`invoiceid` = `i`.`id`)),0) - coalesce((select sum(`tblcredit`.`amount`) from `tblcredit` where (`tblcredit`.`description` like concat('%Invoice #',`i`.`id`))),0)) AS `paid_amount` FROM `tblinvoices` AS `i` ;

-- --------------------------------------------------------

--
-- 视图结构 `view_products`
--
DROP TABLE IF EXISTS `view_products`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_products`  AS SELECT `h`.`id` AS `id`, `h`.`userid` AS `userid`, `h`.`orderid` AS `orderid`, `h`.`packageid` AS `packageid`, `c`.`companyname` AS `companyname`, `h`.`server` AS `server`, `h`.`regdate` AS `regdate`, `h`.`domain` AS `domain`, `h`.`dedicatedip` AS `dedicatedip`, `h`.`amount` AS `amount`, `c`.`currency` AS `currency`, `h`.`billingcycle` AS `billingcycle`, `h`.`nextduedate` AS `nextduedate`, `h`.`nextinvoicedate` AS `nextinvoicedate`, `h`.`termination_date` AS `termination_date`, `h`.`domainstatus` AS `domainstatus` FROM (`tblhosting` `h` left join `tblclients` `c` on((`h`.`userid` = `c`.`id`))) ;

-- --------------------------------------------------------

--
-- 视图结构 `view_services`
--
DROP TABLE IF EXISTS `view_services`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_services`  AS SELECT `h`.`id` AS `id`, `h`.`userid` AS `userid`, `h`.`orderid` AS `orderid`, `h`.`packageid` AS `packageid`, `c`.`companyname` AS `companyname`, `h`.`server` AS `server`, `h`.`regdate` AS `regdate`, `h`.`domain` AS `domain`, `h`.`dedicatedip` AS `dedicatedip`, `h`.`amount` AS `amount`, `c`.`currency` AS `currency`, `h`.`billingcycle` AS `billingcycle`, `h`.`nextduedate` AS `nextduedate`, `h`.`nextinvoicedate` AS `nextinvoicedate`, `h`.`termination_date` AS `termination_date`, `h`.`domainstatus` AS `domainstatus`, `h`.`created_at` AS `created_at`, `h`.`updated_at` AS `updated_at` FROM (`tblhosting` `h` left join `tblclients` `c` on((`h`.`userid` = `c`.`id`))) ;

-- --------------------------------------------------------

--
-- 视图结构 `view_trans`
--
DROP TABLE IF EXISTS `view_trans`;

CREATE ALGORITHM=UNDEFINED DEFINER=`cloudddos`@`localhost` SQL SECURITY DEFINER VIEW `view_trans`  AS SELECT `a`.`id` AS `id`, `a`.`userid` AS `userid`, `a`.`currency` AS `currency`, `a`.`gateway` AS `gateway`, `a`.`date` AS `date`, `a`.`description` AS `description`, `a`.`amountin` AS `amountin`, `a`.`fees` AS `fees`, `a`.`amountout` AS `amountout`, `a`.`rate` AS `rate`, `a`.`transid` AS `transid`, `a`.`invoiceid` AS `invoiceid`, `a`.`refundid` AS `refundid`, `c`.`companyname` AS `companyname`, `c`.`email` AS `email` FROM (`tblaccounts` `a` left join `tblclients` `c` on((`a`.`userid` = `c`.`id`))) ;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

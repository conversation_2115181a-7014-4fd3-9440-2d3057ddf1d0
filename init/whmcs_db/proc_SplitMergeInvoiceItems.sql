DROP PROCEDURE IF EXISTS `SplitMergeInvoiceItems`;

DELIMITER //

CREATE PROCEDURE SplitMergeInvoiceItems(
    itemIdsToMerge TEXT,
    opUser VARCHAR(255),
    OUT newInvoiceId INT,
    OUT resultMessage TEXT
)
early_exit: BEGIN
    -- 更新时间：2024-04-16 1242, by Sam
    -- 声明局部变量用于存储invoice items对应的invoice id
    DECLARE itemInvoiceIds TEXT DEFAULT "";
    DECLARE clientId INT;
    DECLARE earliestDueDate DATE;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- 获取最近的报错信息
        GET DIAGNOSTICS CONDITION 1
        @sqlstate = RETURNED_SQLSTATE, @errno = MYSQL_ERRNO, @text = MESSAGE_TEXT;

        ROLLBACK;
        -- 把报错信息返回给输出变量
        SET resultMessage = CONCAT("Error ", @errno, ": ", @text);
    END;

    SET resultMessage = 'Operation initialized.';

    -- 检查这些itemIdsToMerge是否属于同一个客户
    IF (SELECT COUNT(DISTINCT userid) 
        FROM tblinvoiceitems 
        WHERE FIND_IN_SET(id, itemIdsToMerge)) > 1 THEN
            SET resultMessage = '错误: 选择的items不属于同一个客户。';
            LEAVE early_exit;
    END IF;

     -- 根据itemIdsToMerge所提供的invoice item ids获取对应的invoice ids
    SET @sql = CONCAT("SELECT GROUP_CONCAT(DISTINCT(invoiceid) SEPARATOR ',') INTO @itemInvoiceIds FROM tblinvoiceitems WHERE id IN (", itemIdsToMerge, ")");
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET itemInvoiceIds = @itemInvoiceIds;   

    -- 根据itemIdsToMerge所提供的invoice item ids获取对应的clientId/userId
    SET @sql = CONCAT("SELECT DISTINCT(userid) INTO @clientId FROM tblinvoiceitems WHERE id IN (", itemIdsToMerge, ") LIMIT 1");
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET clientId = @clientId;

    -- 根据itemIdsToMerge所提供的invoice item ids获取对应的最早的duedate
    SET @sql = CONCAT("SELECT MIN(duedate) INTO @earliestDueDate FROM tblinvoiceitems WHERE id IN (", itemIdsToMerge, ")");
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SET earliestDueDate = @earliestDueDate;

    -- 检查tblcredit中是否有相关记录
    IF EXISTS (
        SELECT 1
        FROM tblcredit
        WHERE description REGEXP CONCAT('Invoice #(', REPLACE(itemInvoiceIds, ',', '|'), ')')
    ) THEN
        -- 抛出异常，并终止存储过程
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Error: 操作的账单有关联的credit扣费记录，不允许进行分拆合并！';
    END IF;

    -- 检查tblaccounts中是否有相关记录
    IF EXISTS (
        SELECT 1
        FROM tblaccounts
        WHERE invoiceid IN (itemInvoiceIds)
    ) THEN
        -- 抛出异常，并终止存储过程
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Error: 操作的账单有关联的付款记录，不允许进行分拆合并！';
    END IF;

    -- 检查tblinvoices相关记录是否已经Paid或者已经有credit金额
    IF EXISTS (
        SELECT 1
        FROM tblinvoices
        WHERE id IN (itemInvoiceIds)
        AND (credit > 0)
    ) THEN
        -- 抛出异常，并终止存储过程
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Error: 操作的账单已经产生了credit扣费，不允许进行分拆合并！';
    END IF;   

    START TRANSACTION;

    -- 1. 在tblinvoices插入新账单记录，在notes字段插入一个按特定格式生成的唯一标识。
    INSERT INTO `tblinvoices` (`date`, `duedate`, `invoicenum`,`userid`, `status`, `paymentmethod`, `subtotal`, `total`, `credit`, `tax`, `tax2`, `taxrate`, `taxRate2`, `notes`, `updated_at`, `created_at`) 
    VALUES (DATE_SUB(earliestDueDate, INTERVAL 5 DAY), earliestDueDate, '', clientId, 'Unpaid', 'banktransfer', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '0.00', '', NOW(), NOW());

    -- 获取新生成的invoiceid
    SET newInvoiceId = LAST_INSERT_ID();

    -- 2. 在tblinvoicedata表插入记录
    INSERT INTO `tblinvoicedata` (`country`, `invoice_id`, `updated_at`, `created_at`) 
    VALUES ('US', newInvoiceId, NOW(), NOW());

    -- 3. 更新tblaccount表相关付款记录的invoiceid为新的invoiceid
    UPDATE tblaccounts 
    SET `invoiceid` = newInvoiceId 
    WHERE FIND_IN_SET(invoiceid, itemInvoiceIds);


    -- 4. 更新tblcredit表相关记录的description字段，附加上描述
    SET @sql = CONCAT("UPDATE tblcredit SET description=CONCAT(description, '. Merged to Invoice #", newInvoiceId, "') WHERE description REGEXP 'Invoice #(", REPLACE(itemInvoiceIds, ',', '|'), ")'");
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- 5. 把tblinvoiceitems表中被拆分invoice items的invoiceid更新为新账单id
    SET @sql = CONCAT("UPDATE tblinvoiceitems SET `invoiceid`=", newInvoiceId, " WHERE id IN (", itemIdsToMerge, ")");
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- 5. 更新tblinvoices表中所有相关invoices的subtotal和total
    SET @sql = CONCAT(
        "UPDATE tblinvoices i ",
        "JOIN (",
        "    SELECT invoiceid, SUM(amount) AS sum_amount ",
        "    FROM tblinvoiceitems ",
        "    WHERE FIND_IN_SET(invoiceid, '", newInvoiceId, ",", itemInvoiceIds, "')",
        "    GROUP BY invoiceid",
        ") ii ON i.id = ii.invoiceid ",
        "SET i.subtotal = ii.sum_amount, ",
        "    i.total = ii.sum_amount ",
        "WHERE FIND_IN_SET(i.id, '", newInvoiceId, ",", itemInvoiceIds, "')"
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    -- 6. 把tblinvoices中被合并、且没有其它关联invoice item的账单记录给删除
    DELETE FROM tblinvoices
    WHERE FIND_IN_SET(id, itemInvoiceIds)
    AND id NOT IN (
        SELECT DISTINCT(invoiceid)
        FROM tblinvoiceitems
        WHERE FIND_IN_SET(invoiceid, itemInvoiceIds)
    );

    -- 7. 插入一条日志
    INSERT INTO tblactivitylog (`date`,`description`,`user`,`userid`,`ipaddr`,`user_id`,`admin_id`) 
    VALUES (NOW(), CONCAT('Split Merge: (Invoice ', itemInvoiceIds, ', Item ', itemIdsToMerge, ') merged to Invoice ID: ', newInvoiceId), opUser, clientId, '127.0.0.1', '0', '1');

    COMMIT;

    SET resultMessage = CONCAT('Success, Invoice: ', itemInvoiceIds, ', Items: ', itemIdsToMerge, ' merged to Invoice: #', newInvoiceId);
END //

DELIMITER ;

-- 测试方式：
-- CALL SplitMergeInvoiceItems('1,2,3,4,5', 'sam', @newInvoiceId, @resultMessage); 
-- SELECT @newInvoiceId, @resultMessage;
-- 设置把tblinvoiceitems中duedate为NULL的记录
UPDATE tblinvoiceitems tii
JOIN tblinvoices ti ON tii.invoiceid = ti.id
SET tii.duedate = CASE
    WHEN REGEXP_SUBSTR(tii.description, '[0-9]{4}-[0-9]{2}-[0-9]{2}') IS NOT NULL THEN
        STR_TO_DATE(REGEXP_SUBSTR(tii.description, '[0-9]{4}-[0-9]{2}-[0-9]{2}'), '%Y-%m-%d')
    WHEN REGEXP_SUBSTR(tii.description, '[0-9]{4}/[0-9]{2}/[0-9]{2}') IS NOT NULL THEN
        STR_TO_DATE(REGEXP_SUBSTR(tii.description, '[0-9]{4}/[0-9]{2}/[0-9]{2}'), '%Y/%m/%d')
    ELSE ti.duedate
END
WHERE tii.duedate IS NULL;

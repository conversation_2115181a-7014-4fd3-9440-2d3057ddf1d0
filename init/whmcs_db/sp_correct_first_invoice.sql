DELIMITER //

CREATE PROCEDURE sp_correct_first_invoice(
    IN p_cdWhServiceId INT,
    IN p_billStartDate DATE,
    IN p_itemDescription VARCHAR(255),
    OUT out_resultMessage VARCHAR(255)
)
early_exit: BEGIN
    /*
    =============================================
    存储过程名称: sp_correct_first_invoice
    =============================================

    更新时间：2023-11-14 1140, by Sam 

    描述:
    此存储过程用于修正新增的服务因为信息变化导致自动生成的首个账单信息不准确问题。
    */
    DECLARE v_invoiceItemsCount INT;
    DECLARE v_domain VARCHAR(255);
    DECLARE v_serviceDescription TEXT;
    DECLARE v_billToDate DATE;
    DECLARE v_itemDescription TEXT;
    DECLARE v_dueDate DATE;
    DECLARE v_invoiceItemId INT;

    -- 1. 查询记录数
    SELECT COUNT(*) INTO v_invoiceItemsCount
    FROM tblinvoiceitems
    WHERE relid = p_cdWhServiceId;

    -- 如果记录数不为1，则返回信息
    IF v_invoiceItemsCount <> 1 THEN
        SET out_resultMessage = '没有已生成的账单，不需要进行调整。';
        LEAVE early_exit;
    END IF;

    -- 2. 检查due date是否一致
    SELECT id, duedate INTO v_invoiceItemId, v_dueDate
    FROM tblinvoiceitems
    WHERE relid = p_cdWhServiceId;

    IF v_dueDate = p_billStartDate THEN
        SET out_resultMessage = '账单的due date已经是正确的。';
        LEAVE early_exit;
    END IF;

    -- 3. 获取服务的domain信息
    SELECT domain INTO v_domain
    FROM tblhosting
    WHERE id = p_cdWhServiceId;

    -- 4. 获取服务的service_description信息
    SELECT GROUP_CONCAT(value SEPARATOR '; ') INTO v_serviceDescription
    FROM tblcustomfieldsvalues
    WHERE relid = p_cdWhServiceId;

    -- 5. 根据billStartDate和billCycle计算billToDate
    SET v_billToDate = 
        CASE p_billCycle
            WHEN 'Monthly' THEN LAST_DAY(p_billStartDate + INTERVAL 1 MONTH) - INTERVAL 1 DAY
            WHEN 'Quarterly' THEN LAST_DAY(p_billStartDate + INTERVAL 3 MONTH) - INTERVAL 1 DAY
            WHEN 'Semi-Annually' THEN LAST_DAY(p_billStartDate + INTERVAL 6 MONTH) - INTERVAL 1 DAY
            WHEN 'Annually' THEN LAST_DAY(p_billStartDate + INTERVAL 12 MONTH) - INTERVAL 1 DAY
        END;

    -- 6. 生成item_description信息
    SET v_itemDescription = CONCAT(v_domain, ' (', p_billStartDate, ' - ', v_billToDate, ')', '\n', v_serviceDescription);

    -- 7. 更新tblinvoiceitems找到的唯一一个item
    UPDATE tblinvoiceitems
    SET duedate = p_billStartDate, 
        description = v_itemDescription
    WHERE id = v_invoiceItemId;

    -- 设置成功消息
    SET out_resultMessage = '账单信息已经更新。';
END //

DELIMITER ;

DROP PROCEDURE IF EXISTS `sp_invoice_gen_for_customer`;


DELIMITER //
CREATE PROCEDURE sp_invoice_gen_for_customer(
    p_customerId INT, 
    OUT out_resultMessage VARCHAR(1000),
    p_currentDate DATE  -- 添加这个参数来接受当前日期，默认为NULL
    )
early_exit: BEGIN
/*
=============================================
存储过程名称: sp_invoice_gen_for_customer
=============================================

更新时间：2023-11-14 1140, by Sam

描述:
此存储过程为指定的客户在预设时间范围内生成账单。

操作流程:
1. 接收一个客户ID和当前日期作为输入参数。
2. 检查客户是否使用OPS平台进行账单处理。
3. 创建一个临时表来存储满足条件的服务的nextInvoiceDate和billCurrency。
4. 遍历满足条件的服务，并根据其nextInvoiceDate和billCurrency进行分组。
5. 为每一个满足条件的服务组合生成账单。此操作是通过调用sp_invoice_gen_for_services存储过程实现的。特别注意，具有相同nextInvoiceDate和billCurrency的服务账单将被合并在同一个账单内。需要留意该存储过程的逻辑，以确保两者可以互相配合工作。
6. 如果账单成功生成，记录账单ID和账单编号；如有错误，记录错误信息。

参数:
- p_customerId: 要生成账单的客户ID。
- out_resultMessage: 返回的操作结果消息。
- p_currentDate: 当前日期。如果未提供，默认为系统当前日期。

返回:
此存储过程将返回操作结果消息，包括成功生成的账单ID和账单编号。如果在过程中发生任何错误，错误消息也会返回。

注意:
1. 为了避免死循环，存储过程内部使用了游标来遍历临时表中的记录。
2. 客户必须使用OPS平台进行账单处理，否则存储过程会返回错误。
3. 存储过程使用了事务来确保数据的完整性和一致性。

=============================================
*/
    DECLARE v_advanceInvoiceDays INT DEFAULT 1;
    DECLARE v_serviceIds TEXT;
    DECLARE v_platform VARCHAR(255);
    DECLARE v_currentNextInvoiceDate DATE;
    DECLARE v_currentCurrency VARCHAR(10);
    DECLARE v_invoiceId INT;
    DECLARE v_invoiceNo VARCHAR(255);
    -- DECLARE v_generatedInvoiceIds TEXT DEFAULT '';
    DECLARE v_generatedInvoiceNos TEXT DEFAULT '';
    DECLARE v_errorMessages TEXT DEFAULT '';

    -- 定义游标变量
    DECLARE done INT DEFAULT 0;
    DECLARE cur CURSOR FOR SELECT nextInvoiceDate, billCurrency FROM temp_dates;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    -- 如果没有传递p_currentDate参数，就使用系统的当前日期
    IF p_currentDate IS NULL THEN
        SET p_currentDate = CURDATE();
    END IF;

    -- 检查是否客户使用OPS平台进行账单处理
    SELECT platform INTO v_platform FROM op_customer WHERE id = p_customerId;
    IF v_platform != 'OPS' THEN
        SET out_resultMessage = 'Error: This customer does not use the OPS platform for billing.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    END IF;

    -- 创建一个临时表
    CREATE TEMPORARY TABLE temp_dates (nextInvoiceDate DATE, billCurrency VARCHAR(10));

    -- 根据条件查询出需要处理的服务的nextInvoiceDate和billCurrency，并插入到临时表中
    -- 这么做的目的是为了把相同nextInvoiceDate和billCurrency的服务放在一起生成账单
    INSERT INTO temp_dates
    SELECT DISTINCT nextInvoiceDate, billCurrency
    FROM op_service
    WHERE customerId = p_customerId 
    AND billStatus = 'Active'
    AND nextInvoiceDate <= DATE_ADD(p_currentDate, INTERVAL v_advanceInvoiceDays DAY);

    -- 打开游标
    OPEN cur;
    fetch_loop: LOOP
        FETCH cur INTO v_currentNextInvoiceDate, v_currentCurrency;

        IF done THEN
            LEAVE fetch_loop;
        END IF;

        -- 获取同一nextInvoiceDate和billCurrency的服务的ID列表
        SELECT GROUP_CONCAT(s.id) INTO v_serviceIds 
        FROM op_service s
        WHERE s.customerId = p_customerId -- 同一客户
        AND s.billStatus = 'Active'  -- 计费状态为Active
        AND s.nextInvoiceDate = v_currentNextInvoiceDate  -- 同一账单日
        AND s.billCurrency = v_currentCurrency;  -- 同一币种

        -- 如果找到需要处理的服务
        IF v_serviceIds IS NOT NULL THEN
            BEGIN
                -- 定义错误处理器，捕获并保存异常信息
                DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
                BEGIN
                    GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
                    SET v_errorMessages = CONCAT(v_errorMessages, @p2, '; ');
                END;

                -- 开启独立的事务来处理sp_invoice_gen_for_services
                START TRANSACTION;

                -- 调用sp_invoice_gen_for_services为此日期内的服务生成账单
                CALL sp_invoice_gen_for_services(v_serviceIds, v_invoiceId, v_invoiceNo, @tmpResultMessage);

                -- 提交内部事务
                COMMIT;

                -- 如果生成成功，则记录生成的账单ID和账单编号
                IF v_invoiceId IS NOT NULL AND v_invoiceNo IS NOT NULL THEN
                    IF v_generatedInvoiceNos = '' THEN
                        SET v_generatedInvoiceNos = v_invoiceNo;
                    ELSE
                        SET v_generatedInvoiceNos = CONCAT_WS(', ', v_generatedInvoiceNos, v_invoiceNo);
                    END IF;
                END IF;

            END;
        END IF;

    END LOOP;

    CLOSE cur;

    DROP TABLE temp_dates;

    -- 根据处理过程中发生的错误和成功的账单生成，构建最终的返回消息
    IF LENGTH(v_errorMessages) > 0 THEN
        SET out_resultMessage = CONCAT('Errors: ', v_errorMessages, ' Generated Invoices: ', v_generatedInvoiceNos);
    ELSE
        SET out_resultMessage = CONCAT('Generated Invoices: ', v_generatedInvoiceNos);
    END IF;

    -- 提交事务
    COMMIT;
END //

DELIMITER ;



-- 测试方法：
-- CALL sp_invoice_gen_for_customer(1, @resultMessage, NULL); 
-- SELECT @resultMessage;

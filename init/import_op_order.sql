-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一一对应
-- 导出tbl_orders表，并转换为op_order表对应结构
SELECT
 t1.id,
 t1.OrderNum AS orderNo,
 t2.id AS customerId,
 t3.id AS serviceId,
 CASE 
    WHEN t1.OrderType = '新增' THEN 'New'
    WHEN t1.OrderType = '取消' THEN 'Cancel'
    WHEN t1.OrderType = '变更' THEN 'Change'
    WHEN t1.OrderType = '测试' THEN 'Testing'
    ELSE 'New'
 END AS 'orderType',
 OrderDate AS orderDate,
 CASE 
    WHEN t3.ServiceType = 'Server' THEN 'Server'
    WHEN t3.ServiceType = 'IDC' THEN 'Server'
    WHEN t3.ServiceType = 'IP Transit' THEN 'IP Transit'
    WHEN t3.ServiceType = 'Colocation' THEN 'Colocation'
    WHEN t3.ServiceType = 'Others' THEN 'Server'
    ELSE 'Server'
 END AS 'ServiceType',
 'Normal' AS priority,
 t1.OrderDesc AS description,
 CASE 
    WHEN t1.OrderStatus = '待计费' THEN 'Bill Pending'
    WHEN t1.OrderStatus = '待交付' THEN 'Provision Pending'
    WHEN t1.OrderStatus = '待审核' THEN 'Review Pending'
    WHEN t1.OrderStatus = '已完成' THEN 'Completed'
    ELSE 'Pending'
 END AS 'orderStatus',
 t1.OrderCompleteDate AS closeDate,
 NULL AS salesManagerId,
 NULL AS assignedRoleId,
 NULL AS assignedStaffId,
 '' AS involvedTeams,
 '1,19,20,21' AS visibleRoleId,
 '' AS notes,
 '' AS attachments,
 CASE WHEN t1.CreateBy IS NULL THEN '' ELSE t1.CreateBy END AS createBy,
 t1.CreateDate AS createTime,
 CASE WHEN t1.UpdateBy IS NULL THEN '' ELSE t1.UpdateBy END AS updateBy,
 t1.LastUpdate AS updateTime
FROM tbl_orders t1 
JOIN tbl_customers t2 ON t1.RelCustomerID=t2.CustomerID
JOIN tbl_services t3 ON t1.RelServiceID = t3.ServiceID
-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_racks表，并转换为op_tec_rack表对应结构
SELECT 
 t1.id AS id,
 NULL AS vendorId,
  CASE 
    WHEN t1.RackLocation = 'HKG1' THEN 1
    WHEN t1.RackLocation  = 'HKG3' THEN 4
    WHEN t1.RackLocation  = 'HKG4' THEN 5
    WHEN t1.RackLocation  = 'LAX1' THEN 6
    WHEN t1.RackLocation  = 'LAX2' THEN 7
    WHEN t1.RackLocation  = 'SGP1' THEN 8
    WHEN t1.RackLocation  = 'SGP2' THEN 9
    ELSE NULL
  END AS 'locationId', 
  t1.RackID AS rackNo,
  t1.RackVdID AS vendorRackNo,
  CASE WHEN t1.RackUnit > 0 THEN t1.RackUnit ELSE 0 END AS rackUnit,
  CASE WHEN t1.RackPower > 0 THEN t1.RackPower ELSE 0 END AS rackPower,
  CASE WHEN t1.Remark IS NULL THEN '' ELSE t1.Remark END AS notes,
  '' AS attachments,
  'Sam Gu' AS createBy,
  '2023-07-20 00:00' AS createTime,
  'Sam Gu' AS updateBy,
  '2023-07-20 00:00' AS updateTime  
FROM tbl_racks t1
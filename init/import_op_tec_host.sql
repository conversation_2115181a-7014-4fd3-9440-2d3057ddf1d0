-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_internal_systems表，并转换为op_tec_host表对应结构
SELECT 
 t1.id AS id,
 NULL AS serverId,
 NULL AS parentHostId,
 CONCAT('HS', id) AS hostNo,
 t1.HostName AS hostName,
 t1.Description AS usageDescription,
 'System' AS hostType,
 t1.HostType AS tags,
 t1.OS AS os,
 0 AS cpuCores,
 0 AS memorySize,
 0 AS storageSize,
 t1.MgtIP AS ipmiIp,
 t1.PublicIP AS publicIp,
 t1.MgtIP AS mgmtIp,
 t1.DomainName1 AS domainName,
 NULL AS adminStaffId,
 t1.SshAccess AS loginAccount,
 '' AS loginPassword,
 t1.Remark AS notes,
 1 AS visibleRoleId,
 'Admin' AS createBy,
 '2023-07-20 00:00' AS createTime,
 'Admin' AS updateBy,
 '2023-07-20 00:00' AS updateTime,
 t1.HostName AS temp
FROM tbl_internal_systems t1
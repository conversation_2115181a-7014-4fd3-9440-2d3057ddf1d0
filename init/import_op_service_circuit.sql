-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_serviceoptions表，并转换为op_service_circuit表对应结构
SELECT 
 t1.id,
 t2.id AS serviceId,
 t3.id AS orderId,
 CASE 
   WHEN t1.ServiceType = 'Server' THEN 'Dedicated'
   ELSE t1.ServiceType
 END AS 'type',
 t2.ServiceDesc AS description,
 t1.Location AS location,
 CASE 
    WHEN t1.Status = '已开通' THEN 'Active'
    WHEN t1.Status = '已回收' THEN 'Terminated'
    WHEN t1.Status = '已暂停' THEN 'Suspended'
    WHEN t1.Status = '测试中' THEN 'Testing'
    WHEN t1.Status = '待开通' THEN 'Pending'
    WHEN t1.Status = '有效的' THEN 'Active'
    WHEN t1.Status = '无效的' THEN 'Terminated'
    ELSE t1.Status
 END AS 'status',
  CASE 
    WHEN t1.Status = '已开通' THEN 'Active'
    WHEN t1.Status = '已回收' THEN 'Terminated'
    WHEN t1.Status = '已暂停' THEN 'Suspended'
    WHEN t1.Status = '测试中' THEN 'Testing'
    WHEN t1.Status = '待开通' THEN 'Pending'
    WHEN t1.Status = '有效的' THEN 'Active'
    WHEN t1.Status = '无效的' THEN 'Terminated'
    ELSE t1.Status
 END AS 'billStatus',
  CASE 
    WHEN t1.BillCycle = 1 THEN 'Monthly'
    WHEN t1.BillCycle = 3 THEN 'Quarterly'
    WHEN t1.BillCycle = 6 THEN 'Semi-Annually'
    WHEN t1.BillCycle = 12 THEN 'Annually'
    WHEN t1.BillCycle = 0 THEN 'Free'
    ELSE t1.BillCycle
  END AS 'billCycle',
  t1.BillCurrency AS billCurrency,
  t1.BillAmount AS billAmount,
  CASE 
  	WHEN t2.ActiveDate IS NULL THEN '1000-01-01'
    WHEN t2.ActiveDate = '1999-09-09' THEN '1000-01-01' 
    ELSE t2.ActiveDate 
  END AS activeDate,
  CASE 
  	WHEN t1.BillStartDate IS NULL THEN '1000-01-01'
    WHEN t1.BillStartDate = '1999-09-09' THEN '1000-01-01' 
    ELSE t1.BillStartDate
  END AS billStartDate,
  CASE 
  	WHEN t2.TerminateDate IS NULL THEN '1000-01-01'
    WHEN t2.TerminateDate = '1999-09-09' THEN '1000-01-01' 
    ELSE t2.TerminateDate 
  END AS terminateDate,
  t1.BillEndDate AS `billStopDate`,
  -- Network
  NULL AS productId,
  CASE 
  	WHEN NetworkOption = 'CNO' THEN 101
    WHEN NetworkOption = 'GLO' THEN 102
    WHEN NetworkOption = 'CND' THEN 103
    ELSE NULL
  END AS networkProductId,
  Bandwidth AS bandwidth,
  CASE WHEN BurstBW IS NULL THEN 0 ELSE BurstBW END AS burstBandwidth,
  CASE WHEN Protection IS NULL THEN 0 ELSE Protection END AS protectedBandwidth,
  CASE WHEN CleanBW IS NULL THEN 0 ELSE CleanBW END AS cleanBandwidth,
  CASE WHEN IPs IS NULL THEN 0 ELSE IPs END AS ipAddress,
  -- IP Transit
  '' AS asn,
  '' AS prefix,
  '' AS asset,
  '' AS accessRouter,
  '' AS accessSwitch,
  '' AS circuitInfo,
  -- Other General Fields
  CASE 
    WHEN t1.ServiceOptions IS NULL THEN ''
    ELSE t1.ServiceOptions
  END AS 'notes',
  '' AS createBy,
  '2023-07-20 00:00:00' AS createTime,
  '' AS updateBy,
  CASE 
    WHEN t1.UpdateTime IS NULL THEN '2023-07-20 00:00:00'
    ELSE t1.UpdateTime
  END AS 'updateTime'  
FROM 
 tbl_serviceoptions t1 
 JOIN tbl_services t2 ON t1.RelServiceID = t2.ServiceID
 JOIN tbl_orders t3 ON t1.RelOrderNum = t3.OrderNum
WHERE t1.ServiceType = 'IP Transit' OR t1.id=3440 OR t1.id=3448 OR t1.id=4225
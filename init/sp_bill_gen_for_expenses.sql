DROP PROCEDURE IF EXISTS `sp_bill_gen_for_expenses`;

DELIMITER //

CREATE PROCEDURE `sp_bill_gen_for_expenses`(p_expenseIds TEXT, OUT out_billId INT, OUT out_billNo VARCHAR(255), OUT out_resultMessage VARCHAR(1000))
early_exit: BEGIN
/*
======================================================================
存储过程名称：sp_bill_gen_for_expenses

更新时间：2024-09-19 10:30, by Sam

描述：
此存储过程用于为指定的一组费用生成账单（bill）。

操作流程：
1. 验证所提供的费用ID，确保它们存在且相关。
2. 确保所有费用项都属于同一供应商且使用相同的货币单位，如果不同则终止并返回错误信息。
3. 获取expenses对应的供应商ID和货币类型。
4. 使用fn_custom_no_gen函数生成一个自定义的账单编号。
5. 计算expenses的总金额和最小到期日
6. 创建Bill Item，计算费用周期并关联Bill
7. 更新Bill的总金额为所有Bill items的金额总和
8. 把expense的nextBillDate延期1个周期
9. 返回创建的bill的ID、编号以及状态消息。

======================================================================
*/
    DECLARE v_newBillId INT;
    DECLARE v_customBillNo VARCHAR(255);
    DECLARE v_billCurrency VARCHAR(255);
    DECLARE v_vendorId INT;
    DECLARE v_vendorNo VARCHAR(255);
    DECLARE v_vendorAlias VARCHAR(255);
    DECLARE v_minNextBillDate DATE;
    
    -- 开启事务处理
    START TRANSACTION;
    
    -- 1. 确保传入的p_expenseIds参数不为空或者不只包含无效的id
    SELECT COUNT(*) INTO @v_validExpensesCount FROM op_expense WHERE FIND_IN_SET(id, p_expenseIds);
    IF p_expenseIds IS NULL OR @v_validExpensesCount = 0 THEN
        SET out_resultMessage = 'Error: Invalid expense IDs provided.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    END IF;

    -- 2. 检查这些费用是否同属于一个供应商并使用相同的货币
    SELECT COUNT(DISTINCT vendorId), COUNT(DISTINCT billCurrency) INTO @v_vendorsCount, @v_currenciesCount FROM op_expense WHERE FIND_IN_SET(id, p_expenseIds);
    IF @v_vendorsCount <> 1 OR @v_currenciesCount <> 1 THEN
        SET out_resultMessage = 'Error: Provided expenses do not belong to the same vendor or have different currencies.';
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = out_resultMessage;
        LEAVE early_exit;
    END IF;

    -- 3. 获取供应商ID、货币类型和最小到期日
    SELECT vendorId, vendorNo, alias, billCurrency, MIN(nextBillDate) INTO v_vendorId, v_vendorNo, v_vendorAlias, v_billCurrency, v_minNextBillDate 
    FROM op_expense e JOIN op_vendor v ON v.id = e.vendorId
    WHERE FIND_IN_SET(e.id, p_expenseIds) GROUP BY vendorId, vendorNo, alias, billCurrency;
    
    -- 4. 获取自定义账单编号
    SET v_customBillNo = fn_custom_no_gen('bill');


    -- 5. 创建新的bill记录，并获取id
    INSERT INTO op_expense_bill (billNo, vendorId, description, currency, totalAmount, status, issueDate, dueDate, createBy, updateBy) 
    VALUES (v_customBillNo, v_vendorId, CONCAT('Bill-', v_vendorAlias, '-', DATE_FORMAT(v_minNextBillDate, '%Y/%m/%d')), v_billCurrency, 0, 'Unpaid', v_minNextBillDate, v_minNextBillDate, 'OPS', 'OPS');

    SET v_newBillId = LAST_INSERT_ID();

    -- 6. 创建Bill Item，计算费用周期并关联Bill
    INSERT INTO op_expense_bill_item (billId, expenseId, itemDetails, billCycle, itemAmount, taxRate, fromDate, toDate, dueDate, categorizedAccountId, createBy)
    SELECT 
        v_newBillId AS billId, 
        e.id AS expenseId, 
        e.description AS itemDetails,
        e.billCycle AS billCycle,
        e.billAmount AS itemAmount,
        e.taxRate AS taxRate,
        e.nextBillDate AS fromDate,
        CASE e.billCycle
            WHEN 'Monthly' THEN DATE_ADD(e.nextBillDate, INTERVAL 1 MONTH) - INTERVAL 1 DAY
            WHEN 'Quarterly' THEN DATE_ADD(e.nextBillDate, INTERVAL 3 MONTH) - INTERVAL 1 DAY
            WHEN 'Semi-Annually' THEN DATE_ADD(e.nextBillDate, INTERVAL 6 MONTH) - INTERVAL 1 DAY
            WHEN 'Annually' THEN DATE_ADD(e.nextBillDate, INTERVAL 1 YEAR) - INTERVAL 1 DAY
            ELSE e.nextBillDate
        END AS toDate,
        e.nextBillDate AS dueDate,
        chartOfAccountId AS categorizedAccountId,
        'OPS' AS createBy
    FROM op_expense e
    WHERE FIND_IN_SET(e.id, p_expenseIds);


    -- 7. 更新Bill的总金额为所有Bill items的金额总和
    UPDATE op_expense_bill SET totalAmount = (SELECT SUM(itemAmount) FROM op_expense_bill_item WHERE billId = v_newBillId) WHERE id = v_newBillId;

    -- 8. 把expense的nextBillDate延期1个周期
    UPDATE op_expense SET nextBillDate = CASE billCycle
        WHEN 'Monthly' THEN DATE_ADD(nextBillDate, INTERVAL 1 MONTH)
        WHEN 'Quarterly' THEN DATE_ADD(nextBillDate, INTERVAL 3 MONTH)
        WHEN 'Semi-Annually' THEN DATE_ADD(nextBillDate, INTERVAL 6 MONTH)
        WHEN 'Annually' THEN DATE_ADD(nextBillDate, INTERVAL 1 YEAR)
        ELSE nextBillDate
    END WHERE FIND_IN_SET(id, p_expenseIds);

    -- 提交事务
    COMMIT;

    -- 9. 返回创建的bill的ID、编号以及状态消息
    SET out_billId = v_newBillId;
    SET out_billNo = v_customBillNo;
    SET out_resultMessage = CONCAT('Success: Bill ', v_customBillNo, ' has been generated with ID ', v_newBillId);
END //

DELIMITER ;

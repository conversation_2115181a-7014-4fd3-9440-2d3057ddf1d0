-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_bills表，并转换为op_expense_bill_item表对应结构
SELECT 
 NULL AS id,
 t1.id AS billId,
 t2.id AS expenseId,
 t1.BillDesc AS itemDetails,
 t1.BillAmount AS itemAmount,
 59 AS categorizedAccountId,
 t1.BillFrom AS fromDate,
 t1.BillTo AS toDate,
 t1.BillRemark AS notes,
 t1.CreateBy AS createBy,
 t1.CreateDate AS createDate,
 CASE 
  WHEN t1.UpdateBy IS NULL THEN t1.CreateBy
  WHEN t1.UpdateBy = '' THEN t1.CreateBy
  ELSE t1.UpdateBy 
 END AS updateBy,
 CASE WHEN t1.UpdateTime IS NULL THEN t1.CreateDate ELSE t1.CreateDate END AS updateTime 
FROM tbl_bills t1
LEFT JOIN tbl_opex t2 ON t1.RelOpexID = t2.OpexID
WHERE BillType='Bill'

-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_ipadd表，并转换为op_tec_ipadd表对应结构
SELECT 
 t1.id AS id,
 t2.id AS ipsubnetId,
 t3.id AS serviceId,
 t4.id AS serverId,
 t1.IP AS ipStr,
 t1.IPNum AS ipNo,
 NULL AS subnet2,
 '' AS tags,
 CASE 
  WHEN t1.IPStatus = '可分配' THEN 'Available'
  WHEN t1.IPStatus = '保留' THEN 'Reserved'
  WHEN t1.IPStatus = '已分配' THEN 'Assigned'
  ELSE 'Available' 
 END AS `status`,
 t1.UsageDesc AS usageDesc,
 CASE WHEN t1.VLAN IS NULL THEN '' ELSE t1.VLAN END AS vlanId,
 CASE WHEN t1.UpdateBy IS NULL THEN '' ELSE t1.UpdateBy END AS createBy,
 CASE WHEN t1.IPLastUpdate IS NULL THEN '2023-07-20 00:00' ELSE t1.IPLastUpdate END AS createTime,
 CASE WHEN t1.UpdateBy IS NULL THEN '' ELSE t1.UpdateBy END AS updateBy,
 CASE WHEN t1.IPLastUpdate IS NULL THEN '2023-07-20 00:00' ELSE t1.IPLastUpdate END AS updateTime
FROM tbl_ipadd t1
LEFT JOIN tbl_ipsubnets t2 ON t2.SubnetC = t1.SubnetC
LEFT JOIN tbl_services t3 ON t3.ServiceID = t1.RelServiceID
LEFT JOIN tbl_servers t4 ON t4.ServerID = t1.RelServerID
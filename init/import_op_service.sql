-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_services表，并转换为op_service表对应结构
SELECT 
  t1.id,
  t2.id AS customerId,
  ServiceID AS serviceNo,
  ServiceType AS serviceType,
  ServiceDesc AS serviceDesc,
  CASE 
    WHEN MainIP IS NULL THEN ''
    ELSE MainIP
  END AS 'primaryIp',
  CASE 
    WHEN t1.Status = '已开通' THEN 'Active'
    WHEN t1.Status = '已回收' THEN 'Terminated'
    WHEN t1.Status = '已暂停' THEN 'Suspended'
    WHEN t1.Status = '测试中' THEN 'Testing'
    WHEN t1.Status = '待开通' THEN 'Pending'
    ELSE t1.Status
  END AS 'serviceStatus',  
  CASE 
    WHEN t1.billStatus = '计费中' THEN 'Active'
    WHEN t1.billStatus = '已终止' THEN 'Terminated'
    WHEN t1.billStatus = '免计费' THEN 'Active'
    WHEN t1.billStatus = '待计费' THEN 'Pending'
    ELSE t1.billStatus
  END AS 'billStatus',  
  BillDueDate AS billDueDate,
  CASE 
    WHEN billCycle = 1 THEN 'Monthly'
    WHEN billCycle = 3 THEN 'Quarterly'
    WHEN billCycle = 6 THEN 'Semi-Annually'
    WHEN billCycle = 12 THEN 'Annually'
    WHEN billCycle = 0 THEN 'Free'
    ELSE billCycle
  END AS 'billCycle',
  BillCurrency AS billCurrency,
  BillAmount AS billAmount,
  BillDueDate AS nextInvoiceDate,
  CASE 
    WHEN FirstActiveDate IS NULL THEN '1000-01-01'
    ELSE FirstActiveDate
  END AS 'firstActiveDate',
  CASE WHEN BillStartDate IS NULL THEN '1000-01-01'
    ELSE BillStartDate
  END AS 'firstBillDate',
  CASE 
    WHEN TerminateDate IS NULL THEN '1000-01-01'
    ELSE TerminateDate
  END AS 'terminateDate',
  CASE 
    WHEN BillEndDate IS NULL THEN '1000-01-01'
    ELSE BillEndDate
  END AS 'billStopDate',
  NULL AS salesManagerId,
  RelWhServiceID AS cdWhServiceId,
  '' AS attachments,
  CASE 
    WHEN t1.Remark IS NULL THEN ''
    ELSE t1.Remark
  END AS 'notes',
  '' AS createBy,
  '2023-07-20 00:00:00' AS createTime,
  '' AS updateBy,
  CASE 
    WHEN t1.UpdateTime IS NULL THEN '2023-07-20 00:00:00'
    ELSE t1.UpdateTime
  END AS 'updateTime'  
FROM 
tbl_services t1 JOIN tbl_customers t2 ON t1.RelCustomerID=t2.CustomerID
ORDER BY t1.id  DESC
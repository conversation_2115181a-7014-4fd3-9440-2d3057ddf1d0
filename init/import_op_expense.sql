-- 使用方法：用下面的查询语句在OPS1数据库执行查询（通过phpmyadmin），然后把查询结果导出为CSV，然后在OPS3数据库对应的表中导入CSV文件即可
-- 因数据库字段有可能在后期更新或者调整顺序，导入导出时请留意字段名称/类型/顺序是否一致
-- 导出tbl_opex表，并转换为op_expense表对应结构
SELECT 
 t1.id AS id,
 t2.id AS vendorId,
 t3.id AS serviceId,
 NULL AS purchaseOrderId,
 t1.OpexID AS expenseNo,
 t1.Description AS description,
 64 AS chartOfAccountId,
 t1.Location AS location,
  CASE 
    WHEN t1.BillCycle = 1 THEN 'Monthly'
    WHEN t1.BillCycle  = 3 THEN 'Quarterly'
    WHEN t1.BillCycle  = 6 THEN 'Semi-Annually'
    WHEN t1.BillCycle  = 12 THEN 'Annually'
    WHEN t1.BillCycle  = 0 THEN 'Free'
    ELSE t1.BillCycle
  END AS 'billCycle', 
  t1.BillCurrency AS billCurrency,
  t1.BillAmount AS billAmount,
  'No' AS depositPaid,
  t1.BillNRC AS billNrc,
  t1.CtrOrderNo AS vendorOrderNo,
  t1.CtrOrderNo AS vendorServiceId,
  t1.CtrSignBy AS contractEntity,
  t1.CtrSignTerm AS contractTerms,
  CASE 
    WHEN t1.BillStatus = '计费中' THEN 'Active'
    WHEN t1.BillStatus = '已取消' THEN 'Terminated'
  END AS 'status',
  t1.BillStartDate AS activeDate,
  CASE 
    WHEN t1.BillStatus = '计费中' THEN 'Active'
    WHEN t1.BillStatus = '已取消' THEN 'Terminated'
  END AS 'billStatus',
  t1.BillLastDate AS billDueDate,
  t1.BillStartDate AS billStartDate,
  t1.BillEndDate AS billEndDate,
  '' AS attachments,
  t1.Remark AS notes,
  1 AS visibleRoleId,
  'Sam Gu' AS createBy,
  t1.BillStartDate AS createTime,
  'Sam Gu' AS updateBy,
  t1.BillStartDate AS updateTime
FROM tbl_opex t1
LEFT JOIN tbl_vendors t2 ON t1.RelVendorID = t2.VendorID
LEFT JOIN tbl_services t3 ON t1.RelServiceID = t3.ServiceID



/*
导出数据前，把字段的NULL先进行处理
UPDATE `tbl_opex` 
SET 
    `Location` = COALESCE(`Location`, ''),
    `Description` = COALESCE(`Description`, ''),
    `Category` = COALESCE(`Category`, ''),
    `SubCategory` = COALESCE(`SubCategory`, ''),
    `BillStatus` = COALESCE(`BillStatus`, ''),
    `BillCycle` = COALESCE(`BillCycle`, 0),
    `BillCurrency` = COALESCE(`BillCurrency`, ''),
    `BillAmount` = COALESCE(`BillAmount`, 0),
    `BillNRC` = COALESCE(`BillNRC`, 0),
    `RelVendorID` = COALESCE(`RelVendorID`, ''),
    `CtrOrderNo` = COALESCE(`CtrOrderNo`, ''),
    `CtrSignBy` = COALESCE(`CtrSignBy`, ''),
    `CtrSignTerm` = COALESCE(`CtrSignTerm`, 0),
    `RelCustomerID` = COALESCE(`RelCustomerID`, ''),
    `RelServiceID` = COALESCE(`RelServiceID`, ''),
    `Remark` = COALESCE(`Remark`, '');
*/
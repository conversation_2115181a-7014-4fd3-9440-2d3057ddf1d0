{
	// OPS精确匹配字段对象模式
	"ops-fieldEq-object": {
		"prefix": "ops-fieldEq-object",
		"scope": "typescript",
		"body": [
			"fieldEq: [",
			"	{ column: 'id', requestParam: 'id' },",
			"	{ column: 'location', requestParam: 'location' },",
			"	{ column: 'serverNo', requestParam: 'serverNo' },",
			"	{ column: 'a.customerId', requestParam: 'customerId' },",
			"],"
		],
		"description": "OPS精确匹配字段对象模式"
	}
}

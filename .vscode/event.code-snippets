{"event": {"prefix": "event", "body": ["import { Provide, <PERSON>ope, <PERSON>opeEnum } from '@midwayjs/decorator';", "import { CoolEvent, Event } from '@cool-midway/core';", "", "/**", " * 接收事件", " */", "@CoolEvent()", "export class xxxEvent {", "  @Event('updateUser')", "  async updateUser(msg, a) {", "    console.log('ImEvent', 'updateUser', msg, a);", "  }", "}", ""], "description": "cool-admin event代码片段"}}
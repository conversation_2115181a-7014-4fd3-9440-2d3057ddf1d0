{"liveServer.settings.port": 5501, "editor.tabSize": 2, "editor.detectIndentation": false, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"*.ts": "$(capture).test.ts, $(capture).test.tsx", "*.tsx": "$(capture).test.ts, $(capture).test.tsx", "*.env": "$(capture).env.*", "CHANGELOG.md": "CHANGELOG*", "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,LICENSE,.gitattributes,.gitignore,.gitpod.yml,CNAME,README*,.npmrc,.browserslistrc", ".eslintrc.*": ".<PERSON><PERSON><PERSON><PERSON>,.prettieri<PERSON><PERSON>,.style<PERSON><PERSON><PERSON>,.commitlintrc.js,.prettierrc.js,.prettierrc,.stylelintrc.js"}, "terminal.integrated.scrollback": 10000, "cSpell.words": ["assignedips", "billingcycle", "clientid", "customfields", "dedicatedip", "domain", "firstpaymentamount", "hostname", "nextduedate", "noinvoice", "paymentmethod", "pid", "priceoverride", "recurringamount", "regdate", "serviceid", "vmid"]}
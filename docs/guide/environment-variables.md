# 环境变量配置指南

本文档详细说明了 OPS 项目中使用的所有环境变量配置。

## 目录

- [基础配置](#基础配置)
- [数据库配置](#数据库配置)
- [Redis 配置](#redis-配置)
- [JWT 配置](#jwt-配置)
- [应用服务配置](#应用服务配置)
- [第三方服务配置](#第三方服务配置)
- [Discord 配置](#discord-配置)
- [WHMCS 配置](#whmcs-配置)
- [PVE 配置](#pve-配置)
- [ADS 服务配置](#ads-服务配置)
- [WebScript 配置](#webscript-配置)
- [ChatBot 配置](#chatbot-配置)
- [Gmail 配置](#gmail-配置)
- [其他配置](#其他配置)

## 基础配置

### 应用环境

| 变量名                | 说明             | 默认值                  | 必需 |
| --------------------- | ---------------- | ----------------------- | ---- |
| `NODE_ENV`            | 运行环境         | -                       | 是   |
| `APP_HOST`            | 应用主机地址     | -                       | 是   |
| `APP_BACKEND_ADDRESS` | 后端服务地址     | `http://127.0.0.1:8001` | 否   |
| `APP_TASK_EXECUTE`    | 是否启用任务执行 | `true`                  | 否   |

## 数据库配置

### 主数据库

| 变量名                 | 说明                   | 默认值  | 必需 |
| ---------------------- | ---------------------- | ------- | ---- |
| `DATABASE_URL`         | 数据库连接 URL         | -       | 是   |
| `DATABASE_SYNCHRONIZE` | 是否自动同步数据库结构 | `false` | 否   |

### ADS 数据库

| 变量名               | 说明               | 默认值      | 必需 |
| -------------------- | ------------------ | ----------- | ---- |
| `ADS_DB_HOST`        | ADS 数据库主机     | `127.0.0.1` | 否   |
| `ADS_DB_PORT`        | ADS 数据库端口     | `3306`      | 否   |
| `ADS_DB_USERNAME`    | ADS 数据库用户名   | -           | 是   |
| `ADS_DB_PASSWORD`    | ADS 数据库密码     | -           | 是   |
| `ADS_DB_DATABASE`    | ADS 数据库名称     | -           | 是   |
| `ADS_DB_SYNCHRONIZE` | ADS 数据库自动同步 | `false`     | 否   |

## Redis 配置

| 变量名           | 说明                  | 默认值      | 必需 |
| ---------------- | --------------------- | ----------- | ---- |
| `REDIS_HOST`     | Redis 主机地址        | `localhost` | 是   |
| `REDIS_PORT`     | Redis 端口            | `6379`      | 否   |
| `REDIS_PASSWORD` | Redis 密码            | -           | 否   |
| `REDIS_DB`       | Redis 数据库索引      | `0`         | 否   |
| `REDIS_DATABASE` | Redis 数据库索引(ADS) | `0`         | 否   |

## JWT 配置

| 变量名           | 说明         | 默认值 | 必需 |
| ---------------- | ------------ | ------ | ---- |
| `JWT_SECRET`     | JWT 密钥     | -      | 是   |
| `JWT_EXPIRES_IN` | JWT 过期时间 | `1d`   | 否   |

## 应用服务配置

| 变量名                  | 说明         | 默认值 | 必需 |
| ----------------------- | ------------ | ------ | ---- |
| `PORT`                  | 服务端口     | `3000` | 否   |
| `CREDENTIAL_SECRET_KEY` | 凭证加密密钥 | -      | 是   |

## 第三方服务配置

### DCIM 服务

| 变量名              | 说明            | 默认值 | 必需 |
| ------------------- | --------------- | ------ | ---- |
| `DCIM_API_URL`      | DCIM API 地址   | -      | 是   |
| `DCIM_API_USERNAME` | DCIM API 用户名 | -      | 是   |
| `DCIM_API_PASSWORD` | DCIM API 密码   | -      | 是   |

### LBNM 服务

| 变量名             | 说明          | 默认值 | 必需 |
| ------------------ | ------------- | ------ | ---- |
| `LBNM95_API_URL`   | LBNM API 地址 | -      | 是   |
| `LBNM95_API_TOKEN` | LBNM API 令牌 | -      | 是   |

### WebScript 服务

| 变量名                  | 说明               | 默认值                  | 必需 |
| ----------------------- | ------------------ | ----------------------- | ---- |
| `WEBSCRIPT_REQUEST_URL` | WebScript 请求地址 | `http://127.0.0.1:8007` | 否   |

### ADS 服务

| 变量名              | 说明         | 默认值                  | 必需 |
| ------------------- | ------------ | ----------------------- | ---- |
| `ADS_REQUEST_URL`   | ADS 请求地址 | `http://127.0.0.1:8004` | 否   |
| `ADS_REQUEST_TOKEN` | ADS 请求令牌 | -                       | 是   |
| `OPS_REQUEST_URL`   | OPS 请求地址 | `http://127.0.0.1:8001` | 否   |
| `AUTH_TOKEN`        | 认证令牌     | -                       | 是   |

## Discord 配置

| 变量名                      | 说明               | 默认值 | 必需 |
| --------------------------- | ------------------ | ------ | ---- |
| `DISCORD_API_URL`           | Discord API 地址   | -      | 是   |
| `DISCORD_BOT_TOKEN`         | Discord 机器人令牌 | -      | 是   |
| `DISCORD_WEBHOOK_OPS_ALERT` | OPS 告警 Webhook   | -      | 是   |
| `DISCORD_WEBHOOK_ADS_ALERT` | ADS 告警 Webhook   | -      | 是   |
| `CHANNEL_URL`               | Discord 频道 URL   | -      | 否   |

## WHMCS 配置

| 变量名                     | 说明               | 默认值 | 必需 |
| -------------------------- | ------------------ | ------ | ---- |
| `WHMCS_CD_API_URL`         | WHMCS API 地址     | -      | 是   |
| `WHMCS_CD_API_IDENTIFIER`  | WHMCS API 标识符   | -      | 是   |
| `WHMCS_CD_API_SECRET`      | WHMCS API 密钥     | -      | 是   |
| `WHMCS_CD_AUTO_SYNC_TOKEN` | WHMCS 自动同步令牌 | -      | 是   |

## PVE 配置

| 变量名               | 说明           | 默认值 | 必需 |
| -------------------- | -------------- | ------ | ---- |
| `PVE_LOGIN_USERNAME` | PVE 登录用户名 | -      | 是   |
| `PVE_LOGIN_PASSWORD` | PVE 登录密码   | -      | 是   |
| `PVE_USERNAME`       | PVE 用户名     | -      | 是   |
| `PVE_PASSWORD`       | PVE 密码       | -      | 是   |

## ADS 服务配置

### InfluxDB 配置

| 变量名            | 说明            | 默认值 | 必需 |
| ----------------- | --------------- | ------ | ---- |
| `INFLUXDB_URL`    | InfluxDB 地址   | -      | 是   |
| `INFLUXDB_ORG`    | InfluxDB 组织   | -      | 是   |
| `INFLUXDB_BUCKET` | InfluxDB 存储桶 | -      | 是   |
| `INFLUXDB_TOKEN`  | InfluxDB 令牌   | -      | 是   |

### NFDUMP 配置

| 变量名                          | 说明               | 默认值 | 必需 |
| ------------------------------- | ------------------ | ------ | ---- |
| `NFDUMP_PATH`                   | NFDUMP 路径        | -      | 是   |
| `NFDUMP_FORMAT_IP_FLOW_CMD`     | IP 流量格式化命令  | -      | 是   |
| `NFDUMP_FORMAT_SUBNET_FLOW_CMD` | 子网流量格式化命令 | -      | 是   |
| `NFDUMP_ACTIVE_IPS`             | 活跃 IP 配置       | -      | 否   |

### ADS 调度配置

| 变量名                 | 说明              | 默认值  | 必需 |
| ---------------------- | ----------------- | ------- | ---- |
| `ADS_SCHEDULE_ENABLED` | 是否启用 ADS 调度 | `false` | 否   |

## WebScript 配置

| 变量名 | 说明           | 默认值 | 必需 |
| ------ | -------------- | ------ | ---- |
| `PORT` | WebScript 端口 | `3000` | 否   |

## ChatBot 配置

| 变量名                | 说明         | 默认值        | 必需 |
| --------------------- | ------------ | ------------- | ---- |
| `OPS_BACKEND_ADDRESS` | OPS 后端地址 | -             | 是   |
| `CRON_RESTART`        | 定时重启配置 | `0 0 * * * *` | 否   |

## Gmail 配置

| 变量名                | 说明             | 默认值 | 必需 |
| --------------------- | ---------------- | ------ | ---- |
| `GMAIL_CLIENT_ID`     | Gmail 客户端 ID  | -      | 是   |
| `GMAIL_CLIENT_SECRET` | Gmail 客户端密钥 | -      | 是   |
| `GMAIL_REDIRECT_URI`  | Gmail 重定向 URI | -      | 是   |

## 其他配置

| 变量名                    | 说明         | 默认值 | 必需 |
| ------------------------- | ------------ | ------ | ---- |
| `QUEUE_CONCURRENCY_LIMIT` | 队列并发限制 | `8`    | 否   |
| `MON_PUSH_TOKEN`          | 监控推送令牌 | -      | 否   |

## 环境变量文件示例

### 主项目 .env 文件示例

```bash
# 基础配置
NODE_ENV=production
APP_HOST=your-app-host
APP_BACKEND_ADDRESS=http://your-backend-address:8001
APP_TASK_EXECUTE=true

# 数据库配置
DATABASE_URL=mysql://username:password@host:port/database
DATABASE_SYNCHRONIZE=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=1d

# 应用服务配置
PORT=8001
CREDENTIAL_SECRET_KEY=your-credential-secret-key

# 第三方服务配置
DCIM_API_URL=http://your-dcim-api-url
DCIM_API_USERNAME=your-dcim-username
DCIM_API_PASSWORD=your-dcim-password

LBNM95_API_URL=http://your-lbnm-api-url
LBNM95_API_TOKEN=your-lbnm-token

WEBSCRIPT_REQUEST_URL=http://127.0.0.1:8007

# Discord配置
DISCORD_API_URL=https://discord.com/api
DISCORD_BOT_TOKEN=your-discord-bot-token
DISCORD_WEBHOOK_OPS_ALERT=your-ops-webhook-url
DISCORD_WEBHOOK_ADS_ALERT=your-ads-webhook-url

# WHMCS配置
WHMCS_CD_API_URL=http://your-whmcs-api-url
WHMCS_CD_API_IDENTIFIER=your-whmcs-identifier
WHMCS_CD_API_SECRET=your-whmcs-secret
WHMCS_CD_AUTO_SYNC_TOKEN=your-sync-token

# PVE配置
PVE_LOGIN_USERNAME=your-pve-username
PVE_LOGIN_PASSWORD=your-pve-password

# 其他配置
QUEUE_CONCURRENCY_LIMIT=8
MON_PUSH_TOKEN=your-monitor-token
```

### ADS 服务 .env 文件示例

```bash
# ADS数据库配置
ADS_DB_HOST=127.0.0.1
ADS_DB_PORT=3306
ADS_DB_USERNAME=your-ads-db-username
ADS_DB_PASSWORD=your-ads-db-password
ADS_DB_DATABASE=your-ads-database
ADS_DB_SYNCHRONIZE=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DATABASE=0

# InfluxDB配置
INFLUXDB_URL=http://your-influxdb-url
INFLUXDB_ORG=your-influxdb-org
INFLUXDB_BUCKET=your-influxdb-bucket
INFLUXDB_TOKEN=your-influxdb-token

# NFDUMP配置
NFDUMP_PATH=/path/to/nfdump
NFDUMP_FORMAT_IP_FLOW_CMD=your-ip-flow-command
NFDUMP_FORMAT_SUBNET_FLOW_CMD=your-subnet-flow-command

# ADS服务配置
ADS_REQUEST_URL=http://127.0.0.1:8004
ADS_REQUEST_TOKEN=your-ads-token
OPS_REQUEST_URL=http://127.0.0.1:8001
AUTH_TOKEN=your-auth-token
ADS_SCHEDULE_ENABLED=true
```

## 注意事项

1. **安全性**: 所有包含敏感信息的环境变量（如密码、令牌、密钥等）都应该妥善保管，不要提交到版本控制系统中。

2. **环境区分**: 建议为不同环境（开发、测试、生产）创建不同的环境变量文件。

3. **默认值**: 某些环境变量提供了默认值，但在生产环境中建议明确设置所有必需的环境变量。

4. **验证**: 在启动应用前，请确保所有必需的环境变量都已正确配置。

5. **权限**: 确保应用有足够的权限访问配置的数据库、Redis 等服务。

## 故障排除

如果遇到环境变量相关的问题，请检查：

1. 环境变量文件是否正确加载
2. 变量名是否拼写正确
3. 必需的环境变量是否都已设置
4. 环境变量的值格式是否正确
5. 应用是否有权限访问相关的服务

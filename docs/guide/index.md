# OPS 项目指南

欢迎使用 OPS 项目指南文档。

## 文档目录

- [环境变量配置指南](./environment-variables.md) - 详细的环境变量配置说明

## 快速开始

### 启动文档项目

```bash
npm run docs:dev
```

## 项目结构

本项目基于 Cool-Admin-Node 二次开发，遵循以下结构：

- `src/modules/moduleXXX/` - 各模块代码
  - `controller/` - 接口入口，负责请求分发和响应
  - `entity/` - 数据结构定义，负责数据库表与对象的映射
  - `service/` - 业务逻辑实现，负责具体的功能处理
- `src/utils/` - 自定义工具函数，通用逻辑应沉淀于此
- `views/xxx.vue` - 页面主入口，只负责布局、模块组合和少量页面级逻辑

## 开发规范

1. 优先从本地代码中寻找参考范例并提供解决方案
2. 如本地没有合适的解决方案，参考官方文档：https://node.cool-admin.com/src/guide/quick.html
3. 页面内如需用到工具函数，优先从 `src/utils/` 引入
4. 避免在页面/模块内重复实现通用逻辑

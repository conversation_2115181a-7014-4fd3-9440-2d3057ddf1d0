# 产品

## 客户在线查询价格

### 工作流程

1. 接收产品分类 2 以及配置选项信息

```
category2: 'Baremetal' | 'Network' | 'Datacenter' | 'Custom'
configOptions: {
  'CPU': '2 * E5-2660v2',
  'Memory': '2 * 8GB',
  'Storage': 'HDD-1TB, SSD-500GB',
  'NIC': '10Gbps',
  'RAID': 'RAID 10'
  ...
}
```

2. 根据产品分类 2 以及配置信息中的三级分类获取产品列表及产品对应的规则列表、产品 List 等级价格
3. 根据产品列表中的规则列表中的 matchMethod 和 matchPattern 进行匹配
4. 根据匹配成功的产品列表获取产品列表价格，并计算总价
5. 返回匹配结果

```
- CPU: 2 * E5-2660v2, Price: 100
- Memory: 2 * 8GB, Price: 200
- Storage:
  - HDD-1TB: 100
  - SSD-500GB: 150
```

### 数据库表设计

- op_product_price_rules

  - id/createBy/updateBy/createTime/updateTime: 基础字段
  - matchMethod: 匹配方法, 1: regex(default), 2: include, 3: exclude
  - matchPattern: 匹配模式, string | regex string
  - description: 描述
  - isActive: 是否启用, 1: 启用(default), 0: 禁用

- op_product: 新增字段
  - productPriceRuleId: 产品价格规则 ID

### 程序设计

ConfigMatcherService: 配置匹配服务

1. matchConfig: 配置匹配主函数
2. formatResults: 格式化匹配结果为字符串

OtherService: 其他服务

1. formatConfigOptions: 格式化配置选项
2. 调用 ConfigMatcherService 进行配置匹配

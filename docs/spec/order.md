# 订单

## 操作

订单开通页面操作

### 服务交付

1.更新订单信息

```
assignedRoleId: 分配角色
assignedStaffId: 分配员工
updateBy: 更新人
```

2. 更新订单项目状态

```
status: 订单项目状态
statusTags: 订单项目状态标签
```

3. 更新服务

```
serviceDesc: 服务描述
serviceType: 服务类型
primaryIp: 主IP
updateBy: 更新人
firstActiveDate?: 首次激活日期，如果订单类型为New，则需要设置首次激活日期
```

4. 更新服务项目

   - 将当前正在 Active 的子服务改为 Terminated
   - 新增/更新子服务
   - 修改当前交付的子服务

   ```
   status: 服务项目状态
   activeDate: 激活日期
   description: 服务项目描述
   serviceDetails: 服务项目详情
   serviceDeliveryOpts: 服务项目交付选项
   ```

5. 插入订单回复、订单日志

6. 如果服务存在`cdWhServiceId`则同步更新 WH 服务

```
serviceid: 服务ID
regdate: 注册日期
status: 状态
domain: 域名
dedicatedip: 专用IP
assignedips: 分配IP
customfields: 自定义字段
```

### 服务终止

1. 更新订单信息

```
assignedRoleId: 分配角色
assignedStaffId: 分配员工
updateBy: 更新人
```

2. 更新订单状态信息

```
status: 订单状态
statusTags: 订单状态标签
```

3. 更新服务

```
status: Terminated
terminateDate: 终止日期
```

4. 更新当前子服务

```
status: Terminated
terminateDate: 终止日期
```

5. 如果服务存在`cdWhServiceId`则同步更新 WH 服务

```
terminationdate: 终止日期
status: Terminated
```

6. 插入订单回复、订单日志、发送 discord 消息

### 发送邮件

### 新增转正

### 销售回顾

### 计费开启

### 计费关闭

### 订单完成

### 终止并关闭

### 作废

### 重新打开

### 订单回复

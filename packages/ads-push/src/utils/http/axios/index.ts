import axios from 'axios'
import logger from '../../logger'

const requestClient = axios.create({
	baseURL: process.env.ADS_REQUEST_URL,
	timeout: 60000,
	headers: {
		'Content-Type': 'application/json',
		Authorization: process.env.ADS_REQUEST_TOKEN,
	},
})

type SrcDevice = {
	name: string
	lastUpdate?: string
	id?: number
	status?: 'Active' | 'Inactive'
	message?: string
}

export async function updateSrcDevice(srcDevice: SrcDevice) {
	try {
		const response = await requestClient.put('/srcDevice', srcDevice)
		return response
	} catch (error) {
		logger.error(`请求（updateSrcDevice）异常, ${error.message}`)
	}
}

export async function sendIPFlowToReceive(flows) {
	try {
		const response = await requestClient.post('/receive/flow/ip', flows)
		return response
	} catch (error) {
		logger.error(`请求（sendIPFlowToReceive）异常, ${error.message}`)
	}
}

export async function sendSubnetFlowToReceive(flows) {
	try {
		const response = await requestClient.post('/receive/flow/subnet', flows)
		return response
	} catch (error) {
		logger.error(`请求（sendSubnetFlowToReceive）异常, ${error.message}`)
	}
}

export async function addPushLog(log) {
	try {
		const response = await requestClient.post('/push-log', log)
		return response
	} catch (error) {
		logger.error(`请求（addPushLog）异常, ${error.message}`)
	}
}

type PushActiveIPsParams = {
	ipAddressList: string[]
	srcLocation?: String
	srcDevice?: string
}

export async function pushActiveIPs(data: PushActiveIPsParams) {
	try {
		const response = await requestClient.post('/activeIps/push', data)
		return response
	} catch (error) {
		logger.error(`请求（pushActiveIPs）异常, ${error.message}`)
	}
}

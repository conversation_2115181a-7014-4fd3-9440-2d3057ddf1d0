import dayjs from './dayjs'

export function handleStdout(stdout: string, addInfo?: Record<string, any>) {
	if (!stdout || stdout.trim() === '') return
	if (stdout.includes('No matched flows')) {
		return
	}

	try {
		const results = (JSON.parse(stdout) as any[])
			.map((item) => {
				const first = dayjs(item.t_first)
				const last = dayjs(item.t_last)
				const duration = last.diff(first, 's')
				const firstFormat = first.format('YYYY-MM-DD HH:mm:ss')
				const lastFormat = last.format('YYYY-MM-DD HH:mm:ss')

				return {
					...item,
					...addInfo,
					duration,
					t_first: firstFormat,
					t_last: lastFormat,
				}
			})
			.filter((item) => item.duration > 0)

		return results
	} catch (err) {
		console.error(stdout)
		console.log('Parse IP Error:', err.message)
		return
	}
}

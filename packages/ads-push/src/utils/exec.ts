import { exec } from 'child_process'

export async function execPromise(cmd: string): Promise<string> {
	return new Promise((resolve, reject) => {
		exec(cmd, { maxBuffer: 1024 * 1024 * 10 }, (error, stdout, stderr) => {
			if (error) {
				console.error(`exec error: ${error}`)
				reject(error.message)
				return
			}

			if (stderr) {
				console.error(`stderr: ${stderr}`)
				reject(stderr)
				return
			}

			if (
				!stdout ||
				stdout.trim() === undefined ||
				stdout.trim() === '[]' ||
				stdout.includes('No matched flows')
			) {
				reject('No matched flows')
				return
			}

			resolve(stdout)
		})
	})
}

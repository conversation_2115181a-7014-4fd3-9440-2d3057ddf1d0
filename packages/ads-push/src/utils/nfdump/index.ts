import { execPromise } from '../exec'

class Nfdump {
	async isExistFlowData(path: string) {
		const command = 'nfdump -o json -n 1 -O bytes -r "<path>"'.replace(/<path>/g, path)

		try {
			const stdout = await execPromise(command)
			if (!stdout || stdout.trim() === '' || stdout.trim() === '[]' || stdout.includes('No matched flows')) {
				return false
			} else {
				return true
			}
		} catch (error) {
			return false
		}
	}

	async read(path: string, cmd: string): Promise<string> {
		const command = cmd.replace(/<path>/g, path)
		return execPromise(command)
	}
}

export const nfdump = new Nfdump()

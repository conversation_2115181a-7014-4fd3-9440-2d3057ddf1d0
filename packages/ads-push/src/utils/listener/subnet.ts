import { isUndefined } from 'lodash'
import { dayjs } from '../dayjs'
import { conf } from '../../conf'
import { addPushLog, sendSubnetFlowToReceive } from '../http/axios'
import { nfdump } from '../nfdump'
import { handleStdout } from '../stdout'

export async function watchSubnetListener(path: string) {
	const srcDevice = path.split('/').filter((i) => i)[1]
	const fileGenerationTime = path.split('.')?.[1]
	const lastUpdate = dayjs(fileGenerationTime).format('YYYY-MM-DD HH:mm:ss')
	const commands = conf.formatSubnetCommands

	const keys = Object.keys(commands)
	keys.forEach(async (upstream) => {
		const command = commands[upstream]?.replace(/<path>/g, path)
		const extraInfo = {
			upstream,
			src_device: srcDevice,
			nfd_file_path: path,
		}
		const pushLog: any = {
			type: 'Subnet',
			srcDevice,
			command,
			fileGenerationTime: lastUpdate,
			filePath: path,
		}
		try {
			const stdout = await nfdump.read(path, command)
			const results = handleStdout(stdout, extraInfo)
			if (!isUndefined(results) && results.length) {
				const pushResult = await sendSubnetFlowToReceive(results)
				pushLog.pushLength = results.length
				pushLog.receiveLength = pushResult.data?.length
				pushLog.details = pushResult.data
			} else {
				pushLog.pushLength = 0
			}
			pushLog.status = 1
		} catch (error) {
			pushLog.status = 0
			pushLog.errorMessage = error.message
		}
		addPushLog(pushLog)
	})
}

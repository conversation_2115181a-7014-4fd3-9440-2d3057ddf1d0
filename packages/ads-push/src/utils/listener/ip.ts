import { isUndefined, chain } from 'lodash'
import { dayjs } from '../dayjs'
import { conf } from '../../conf'
import { updateSrcDevice, sendIPFlowToReceive, addPushLog, pushActiveIPs } from '../http/axios'
import { nfdump } from '../nfdump'
import { handleStdout } from '../stdout'

export async function watchIPListener(path: string) {
	const srcDevice = path.split('/').filter((i) => i)[1]
	const srcLocation = srcDevice?.split('-')?.[0]

	const fileGenerationTime = path.split('.')?.[1]
	const lastUpdate = dayjs(fileGenerationTime).format('YYYY-MM-DD HH:mm:ss')
	const commands = conf.formatIPCommands

	// 执行基本检测指令，更新设备LastUpdate
	const isExistData = await nfdump.isExistFlowData(path)

	if (!isExistData) {
		updateSrcDevice({ name: srcDevice, message: '' })
		return
	} else {
		updateSrcDevice({ name: srcDevice, lastUpdate })
	}

	if (process.env.NFDUMP_ACTIVE_IPS) {
		nfdump
			.read(path, process.env.NFDUMP_ACTIVE_IPS?.replace(/<path>/g, path))
			.then((stdout) => handleStdout(stdout))
			.then((data) => {
				if (Array.isArray(data)) {
					const ipAddressList = chain(data)
						.filter((item) => {
							const first = dayjs(item.t_first)
							const last = dayjs(item.t_last)
							if (!first.isValid() || !last.isValid()) return
							return last.diff(first, 'seconds') > 0
						})
						.map((item) => item['dst4_addr'])
						.uniq()
						.value()
					pushActiveIPs({ ipAddressList, srcDevice, srcLocation })
				}
			})
			.catch((error) => console.error(error))
	}

	const keys = Object.keys(commands)
	keys.forEach(async (upstream) => {
		const command = commands[upstream]?.replace(/<path>/g, path)
		const extraInfo = {
			upstream,
			src_device: srcDevice,
			nfd_file_path: path,
		}

		const pushLog: any = {
			type: 'IP',
			srcDevice,
			command,
			fileGenerationTime: lastUpdate,
			filePath: path,
		}

		try {
			const stdout = await nfdump.read(path, command)
			const results = handleStdout(stdout, extraInfo)
			if (!isUndefined(results) && results.length) {
				const pushResult = await sendIPFlowToReceive(results)
				pushLog.pushLength = results.length
				pushLog.receiveLength = pushResult.data?.length
				pushLog.details = pushResult.data
			} else {
				pushLog.pushLength = 0
			}

			pushLog.status = 1
		} catch (error) {
			pushLog.status = 0
			pushLog.errorMessage = error.message
			console.log(error)
		}

		try {
			addPushLog(pushLog)
		} catch (error) {
			console.log('Create Push Log Error', error.message)
		}
	})
}

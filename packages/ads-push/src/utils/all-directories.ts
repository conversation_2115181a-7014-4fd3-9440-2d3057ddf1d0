import * as fs from 'fs'
import * as path from 'path'

/**
 * 获取指定文件夹路径下的所有子文件夹路径（深度递归）
 */
export function getAllDirectoriesPaths(dirPath) {
	let directories = []

	// 同步地读取目录内容
	const contents = fs.readdirSync(dirPath)

	contents.forEach((item) => {
		// 获取每个文件或文件夹的完整路径
		const itemPath = path.join(dirPath, item)

		// 检查是否是文件夹
		if (fs.statSync(itemPath).isDirectory()) {
			// 检查文件夹名称是否包含当天日期
			directories.push(itemPath) // 将文件夹路径添加到数组中
			// 递归获取子文件夹路径
			directories = directories.concat(getAllDirectoriesPaths(itemPath))
		}
	})

	return directories
}

// 示例用法：获取指定目录下所有文件夹路径
// const currentDir = '/flow';
// const allDirectoriesPaths = getAllDirectoriesPaths(currentDir);
// console.log('All directories paths:', allDirectoriesPaths);

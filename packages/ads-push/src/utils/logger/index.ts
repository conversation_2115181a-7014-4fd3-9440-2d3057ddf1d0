import { createLogger, format, transports } from 'winston'
import dayjs from 'dayjs'

const { combine, printf, colorize } = format

// 自定义时间戳格式
const customTimestamp = format((info) => {
	info.timestamp = dayjs().format('YYYY-MM-DD HH:mm:ss')
	return info
})

// 自定义日志格式
const customFormat = printf(({ level, message, timestamp, ...meta }) => {
	let metaString = ''
	if (Object.keys(meta).length > 0) metaString = JSON.stringify(meta)
	return `${timestamp} [${level}]: ${message} ${metaString}`
})

// 创建日志记录器
const logger = createLogger({
	format: combine(
		customTimestamp(), // 使用自定义时间戳格式
		colorize(), // 彩色输出
		customFormat // 使用自定义格式
	),
	transports: [
		new transports.Console(), // 输出到控制台
		new transports.File({ filename: 'combined.log' }),
	],
})

export default logger

import { chain } from 'lodash'

const { ADS_REQUEST_URL, NFDUMP_PATH, NFDUMP_FORMAT_IP_FLOW_CMD, NFDUMP_FORMAT_SUBNET_FLOW_CMD } = process.env

// IP指令
const formatIPCommands = chain(process.env)
	.pickBy((_v, k) => k.includes('NFDUMP_COMMAND_IP'))
	.mapKeys((_v, k) => k.split('NFDUMP_COMMAND_IP_')[1])
	.value()

// Subnet指令
const formatSubnetCommands = chain(process.env)
	.pickBy((_v, k) => k.includes('NFDUMP_COMMAND_SUBNET'))
	.mapKeys((_v, k) => k.split('NFDUMP_COMMAND_SUBNET_')[1])
	.value()

export const conf = {
	pushUrl: ADS_REQUEST_URL,
	nfdIpCmd: NFDUMP_FORMAT_IP_FLOW_CMD,
	nfdSubnetCmd: NFDUMP_FORMAT_SUBNET_FLOW_CMD,
	watchPath: NFDUMP_PATH || '/flow',
	formatIPCommands: formatIPCommands,
	formatSubnetCommands: formatSubnetCommands,
}

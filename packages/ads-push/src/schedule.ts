import * as schedule from 'node-schedule'
import dayjs from './utils/dayjs'
import { getAllDirectoriesPaths } from './utils/all-directories'
import { createWatch } from './watcher'
import { conf } from './conf'

const nfdumpPath = conf.watchPath || '/flow'

/**
 * 返回监听路径
 */
function getWatchPaths(): string[] {
	/**
	 * 获取当前日期及下一天日期
	 * 为什么需要获取当天下一天的日期？
	 * getAllDirectoriesPaths 函数会在运行时获取当前时刻的子文件夹路径，
	 * 而刷新监听器任务是在 0 点执行的，这时 nfdump 不一定已经生成了当天的文件夹及数据，
	 * 因此可能会出现监听不到的情况。
	 * 为了确保在 0 点时刻能够监听到当天的数据，我们需要同时监听当前日期和下一天的文件夹。
	 */
	const currentDate = dayjs().format('YYYY/MM/DD')
	const nextDayDate = dayjs(currentDate).add(1, 'day').format('YYYY/MM/DD')

	/**
	 * 递归获取 nfdumpPath 下的所有子文件夹路径，
	 * 只监听包含当前日期的路径。
	 */
	const watchPaths = getAllDirectoriesPaths(nfdumpPath).filter((dirPath: string) => dirPath.includes(currentDate))

	/**
	 * 增加监听下一天的文件夹路径，
	 * 将当前日期替换为下一天日期并添加到监听路径中。
	 */
	watchPaths.forEach((path: string) => {
		watchPaths.push(path.replace(currentDate, nextDayDate))
	})

	/**
	 * 返回监听路径，
	 * 包括当前日期和下一天日期的路径。
	 */
	return watchPaths
}

try {
	// ADS-Push 项目运行时启动一次监听器
	createWatch(getWatchPaths())

	// 使用 node-schedule 创建每天在 23:50 执行的任务
	// 重新创建监听器
	schedule.scheduleJob('0 50 23 * * *', () => {
		createWatch(getWatchPaths())
	})
} catch (error) {
	if (error.message) {
		console.error(error.message)
	} else {
		console.error(error)
	}
}

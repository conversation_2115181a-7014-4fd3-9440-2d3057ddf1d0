import * as chokidar from 'chokidar'
import { watchIPListener } from './utils/listener/ip'
import { watchSubnetListener } from './utils/listener/subnet'
import { getCurrentDate } from './utils/dayjs'
// import { watchOriginalIPListener } from './utils/listener/original-ip'

let watcher: chokidar.FSWatcher = null

/**
 * 创建文件监听器
 * 重置上一个监听器并创建新的监听器
 */
export function createWatch(watchPaths: string[]) {
	console.log(`Restart Watch:\n${getCurrentDate()}\nPaths:\n${watchPaths.join('\n')}`)

	/**
	 * 如果 Watcher 不为空则关闭监听，防止重复监听
	 * 重置 Watcher 为 null
	 */
	if (watcher !== null) {
		watcher.close()
		watcher = null
	}

	/**
	 * 创建新的监听器
	 */
	watcher = chokidar.watch(watchPaths, {
		persistent: true,
		ignoreInitial: true,
	})

	/**
	 * 监听文件新增事件
	 */
	watcher.on('add', async (path) => {
		// console.log('Watch Paths:', path)
		try {
			await Promise.all([watchIPListener(path), watchSubnetListener(path)])
		} catch (error) {
			console.log(error.message)
		}
	})
}

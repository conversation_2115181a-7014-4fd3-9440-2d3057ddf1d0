# (optional)
# Default: /flow
# 监听Flow文件夹路径
NFDUMP_PATH=/flow

# 拖送流量数据url路径（required）
ADS_REQUEST_URL=http://************:8004
ADS_REQUEST_TOKEN=

# 格式IP流量指令（required）
# NFDUMP_COMMAND_IP 是固定的，最后一个下划线之后的字符会填充到upstream
NFDUMP_COMMAND_IP_HKG3-HE=nfdump -A dstip -o json -n 50 -O bytes -r "<path>" 'inet and in if 436226560'
NFDUMP_COMMAND_IP_HKG3-HKIX=nfdump -A dstip -o json -n 50 -O bytes -r "<path>" 'inet and in if 436228096'
NFDUMP_COMMAND_IP_HKG3-NTT=nfdump -A dstip -o json -n 50 -O bytes -r "<path>" 'inet and in if 56'

# 格式Subnet流量指令（required）
# NFDUMP_COMMAND_SUBNET 是固定的，最后一个下划线之后的字符会填充到upstream
NFDUMP_COMMAND_SUBNET_HKG3-HE=nfdump -A dstip4/24 -o json -n 50 -O bytes -r "<path>" 'inet and in if 436226560'
NFDUMP_COMMAND_SUBNET_HKG3-HKIX=nfdump -A dstip4/24 -o json -n 50 -O bytes -r "<path>" 'inet and in if 436228096'
NFDUMP_COMMAND_SUBNET_HKG3-NTT=nfdump -A dstip4/24 -o json -n 50 -O bytes -r "<path>" 'inet and in if 56'

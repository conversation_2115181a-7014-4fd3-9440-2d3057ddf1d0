const path = require('path')
const name = 'Data99-OPS-ADS-Push'
const logsDirPath = 'logs'
const outFile = path.resolve(__dirname, logsDirPath, `${name}-out.log`)
const errorFile = path.resolve(__dirname, logsDirPath, `${name}-error.log`)
/**
 * 项目（ADS-Push）运行PM2配置文件
 */
module.exports = {
	apps: [
		{
			name,
			script: 'dist/index.js',
			watch: false,
			out_file: outFile,
			error_file: errorFile,
			cron_restart: '0 8 * * *', // 每天 8:00 自动重启
		},
	],
}

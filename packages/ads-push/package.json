{"name": "nfapp-push", "version": "1.0.0", "description": "", "main": "./src/index.ts", "scripts": {"dev": "nodemon src/index.ts", "build": "node scripts/build.js", "start": "node dist/index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.7.2", "chokidar": "^3.6.0", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "execa": "^9.1.0", "lodash": "^4.17.21", "node-schedule": "^2.1.1", "winston": "^3.17.0"}, "devDependencies": {"@types/lodash": "^4.17.4", "@types/node": "^20.12.12", "@types/node-schedule": "^2.1.7", "esbuild": "^0.21.5", "nodemon": "^3.1.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}
export default () => ({
  port: parseInt(process.env.PORT, 10) || 3000,
  jwt: {
    secret: process.env.JWT_SECRET || 'your-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1d',
  },
  // 忽略 token 验证的路径
  ignoreUrls: ['/auth/login', '/auth/register', '/health'],
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB, 10) || 1,
  },
  pve: {
    username: process.env.PVE_USERNAME,
    password: process.env.PVE_PASSWORD,
  },
});

import 'dotenv/config.js'

import express from 'express'
import multer from 'multer'
import { telegramRouter } from './modules/index.js'
import { handleTGChatId } from './middleware/tg-chatid-split.js'

async function bootstrap() {
	const app = express()
	const storage = multer.memoryStorage()
	const upload = multer({ storage })
	const port = 8003

	app.use(express.json())
	app.use(upload.any())

	app.use('/tg', handleTGChatId, telegramRouter)

	app.listen(port, () => {
		console.log(`Example app listening on port ${port}`)
	})
}

bootstrap()

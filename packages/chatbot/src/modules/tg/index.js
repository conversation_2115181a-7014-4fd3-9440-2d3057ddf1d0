import fs from 'fs'
import express from 'express'
import botsMap, { getBot } from '../../lib/bot.js'

const router = express.Router()

const mapAccountPromises = (accounts, callback) => {
	const promises = accounts.map((a) => callback(getBot(a), a))
	return Promise.all(promises)
}

router.post('/message/text', async (req, res) => {
	const { chatId, text, accounts } = req.body

	const result = await mapAccountPromises(accounts, async (bot, account) => {
		const result = await bot.sendMessage(chatId, text)
		result.account = account
		return result
	})

	return res.send(result)
})

router.post('/message/photo', async (req, res) => {
	const { chatId, options, accounts, filePath } = req.body

	const result = await mapAccountPromises(accounts, async (bot, account) => {
		const result = await bot.sendPhoto(chatId, filePath, options)
		result.account = account
		return result
	})

	res.send(result)
})

router.post('/message/document', async (req, res) => {
	const { chatId, account, filePath, options } = req.body

	try {
		const result = await getBot(account).sendDocument(chatId, filePath, options)
		res.send({ ...result, account })
	} catch (error) {
		res.send(`Error: ${error.message}`)
	}
})

router.post('/message/delete', async (req, res) => {
	const { chatId, messageId, account } = req.body
	try {
		const result = await getBot(account).deleteMessage(chatId, messageId)
		res.send({ isOk: result })
	} catch (error) {
		res.send(`Error: ${error.message}`)
	}
})

router.post('/verify/group-member', async (req, res) => {
	const { chatId, accounts } = req.body

	try {
		// 检查是否是群组ID
		if (!chatId.toString().startsWith('-')) {
			return res.send([])
		}

		// 验证所有账号
		const results = await mapAccountPromises(accounts, async (bot, account) => {
			try {
				// 获取机器人信息
				const botInfo = await bot.getMe()

				// 检查机器人在群组中的状态
				const chatMember = await bot.getChatMember(chatId, botInfo.id)

				// 只返回正常状态
				if (['creator', 'administrator', 'member'].includes(chatMember.status)) {
					return {
						account,
						status: chatMember.status,
						botInfo: {
							id: botInfo.id,
							username: botInfo.username,
							firstName: botInfo.first_name,
						},
					}
				}
				return null
			} catch (error) {
				return null
			}
		})

		// 过滤掉无效的结果
		const validBots = results.filter((result) => result !== null)
		res.send(validBots)
	} catch (error) {
		res.send([])
	}
})

export const telegramRouter = router

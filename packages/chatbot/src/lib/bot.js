import TelegramBot from 'node-telegram-bot-api'
import defHttp from '../api/base.js'
import { createMessage } from '../api/message.js'

let botsMap = new Map()

async function getFilePath(bot, fileId) {
	const { file_path } = await bot.getFile(fileId)
	return file_path
}

function getDownloadUrl(botToken, filePath) {
	return `https://api.telegram.org/file/bot${botToken}/${filePath}`
}

async function handlePhoto(bot, msg) {
	const lastPhoto = msg.photo[msg.photo.length - 1]
	const photo = await bot.getFile(lastPhoto.file_id)
	const file_name = photo.file_path.split('/').pop()
	const downloadUrl = getDownloadUrl(bot.token, photo.file_path)

	return {
		...photo,
		file_name,
		downloadUrl,
	}
}

defHttp
	.post('/app/chat/account/list', {
		platform: 'TG',
	})
	.then(({ data }) => {
		registerTelegram(data)
	})

function registerTelegram(bots) {
	bots.forEach((botItem) => {
		try {
			const label = botItem.accountLabel
			const values = JSON.parse(botItem.accountValues)
			if (typeof values !== 'object' || !values.botToken) return
			const tgBot = new TelegramBot(values.botToken, { polling: true })

			// 监听消息事件
			tgBot.on('message', async (msg, options) => {
				msg.options = options
				msg.platform = 'TG'

				try {
					await handleMessage(tgBot, msg, label)
				} catch (error) {
					console.error(`Failed to handle message: ${error}`)
				}
			})

			// 监听新成员加入事件
			tgBot.on('new_chat_members', async (msg) => {
				try {
					const botInfo = await tgBot.getMe()
					const isBotAdded = msg.new_chat_members.some((member) => member.is_bot && member.id === botInfo.id)

					if (isBotAdded) {
						console.log(`[${label}] 机器人被添加到群组:`, {
							chat_id: msg.chat.id,
							chat_title: msg.chat.title,
							chat_type: msg.chat.type,
							added_by: msg.from.username || msg.from.first_name,
						})

						// 发送欢迎消息
						// await tgBot.sendMessage(msg.chat.id, `大家好！我是 ${botInfo.first_name}，很高兴加入这个群组！`)

						// 通知 OPS 系统
						await defHttp.post('/app/chat/account/join', {
							platform: 'TG',
							account: label,
							chat_id: msg.chat.id,
							group_id: msg.chat.id,
							group_name: msg.chat.title,
							group_type: msg.chat.type,
							operator: msg.from.username || msg.from.first_name,
							operator_id: msg.from.id,
						})
					}
				} catch (error) {
					console.error(`Failed to handle new chat members: ${error}`)
				}
			})

			// 监听成员离开事件
			tgBot.on('left_chat_member', async (msg) => {
				try {
					const botInfo = await tgBot.getMe()
					if (msg.left_chat_member.is_bot && msg.left_chat_member.id === botInfo.id) {
						console.log(`[${label}] 机器人被从群组移除:`, {
							chat_id: msg.chat.id,
							chat_title: msg.chat.title,
							chat_type: msg.chat.type,
							removed_by: msg.from.username || msg.from.first_name,
						})

						// 通知 OPS 系统
						await defHttp.post('/app/chat/account/leave', {
							platform: 'TG',
							account: label,
							chat_id: msg.chat.id,
							group_id: msg.chat.id,
							group_name: msg.chat.title,
							group_type: msg.chat.type,
							operator: msg.from.username || msg.from.first_name,
							operator_id: msg.from.id,
						})
					}
				} catch (error) {
					console.error(`Failed to handle left chat member: ${error}`)
				}
			})

			botsMap.set(label, tgBot)
		} catch (e) {
			console.error(`Failed to register bot ${botItem.accountLabel}: ${e}`)
		}
	})
}

async function handleMessage(bot, msg, key) {
	switch (msg.options.type) {
		case 'photo':
			msg.photo = await handlePhoto(bot, msg)
			break
		case 'document':
			msg.document.downloadUrl = getDownloadUrl(bot.token, await getFilePath(bot, msg.document.file_id))
			break
		case 'new_chat_members':
		case 'left_chat_member':
			return
	}

	const createMessageFunc = () => {
		return createMessage({ msg, platform: 'TG', account: key })
	}

	switch (msg.chat.type) {
		case 'private':
			await createMessageFunc()
			break
		case 'supergroup':
		case 'group':
			await createMessageFunc()
			break
	}
}

export const getBot = (key) => {
	return botsMap.get(key)
}

export default botsMap

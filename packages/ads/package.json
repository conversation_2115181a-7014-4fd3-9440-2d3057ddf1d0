{"name": "nfapp", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@influxdata/influxdb-client": "^1.33.2", "@influxdata/influxdb-client-apis": "^1.33.2", "@nestjs/axios": "^3.0.2", "@nestjs/bull": "^10.1.1", "@nestjs/cache-manager": "^2.2.2", "@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.2", "@nestjs/typeorm": "^10.0.2", "@types/big.js": "^6.2.2", "axios": "^1.7.2", "big.js": "^6.2.1", "bull": "^4.14.0", "cache-manager": "^5.5.3", "cache-manager-redis-yet": "^5.0.1", "cfork": "^1.11.0", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.11", "dotenv": "^16.4.5", "express": "^4.19.2", "googleapis": "^144.0.0", "handlebars": "^4.7.8", "ioredis": "^5.4.2", "ip": "^2.0.1", "lodash": "^4.17.21", "mysql2": "^3.10.0", "redis": "3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "ssh-config": "^4.4.4", "ssh2": "^1.15.0", "typeorm": "^0.3.20", "winston": "^3.17.0", "xml2js": "^0.6.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/ip": "^1.1.3", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.4", "@types/node": "^20.3.1", "@types/ssh2": "^1.15.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}
/**
 * Data99-OPS-ADS pm2 配置文件
 *
 * 使用方式: pm2 startOrRestart ecosystem.config.js
 */

const path = require('path');
const name = 'Data99-OPS-ADS';
const logsDirPath = 'logs';
const outFile = path.resolve(__dirname, logsDirPath, `${name}-Out.log`);
const errorFile = path.resolve(__dirname, logsDirPath, `${name}-Error.log`);

module.exports = {
  apps: [
    {
      name,
      script: 'node',
      args: 'server.js',
      node_args: '--max-old-space-size=4096',
      watch: false,
      out_file: outFile,
      error_file: errorFile,
      env: {
        NODE_ENV: 'development',
      },
      env_production: {
        NODE_ENV: 'production',
      },
    },
  ],
};

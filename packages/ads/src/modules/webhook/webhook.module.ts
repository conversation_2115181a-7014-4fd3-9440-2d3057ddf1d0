import { Modu<PERSON> } from '@nestjs/common';
import { AttackController } from './controllers/attack.controller';
import { AttackService } from './services/attack.service';
import { PullFlowHistory } from '../pull/entities/pull_flow_history';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
  imports: [TypeOrmModule.forFeature([PullFlowHistory])],
  controllers: [AttackController],
  providers: [AttackService],
})
export class WebhookModule {}

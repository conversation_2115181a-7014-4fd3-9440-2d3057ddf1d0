import { Body, Controller, Post } from '@nestjs/common';
import { AttackService } from '../services/attack.service';
import { AttackPlatform } from '../enums/attack';

@Controller('webhook')
export class AttackController {
  constructor(private readonly attackService: AttackService) {}

  @Post('/attack')
  async receiveAttack(@Body() body: any) {
    const { platform } = body;

    console.log(body);

    switch (platform) {
      case AttackPlatform.GSL:
        return this.attackService.receiveGsl(body);
      default:
        return { success: false, message: 'platform not supported' };
    }
  }
}

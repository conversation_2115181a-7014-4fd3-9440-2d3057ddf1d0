import { PullFlowHistory } from '@/modules/pull/entities/pull_flow_history';
import * as dayjs from 'dayjs';

// {"content":"The report for a single destination attack is ready. Information is as follows:\nDestination: 168.235.251.168\/32\nStart: 11th June 2025 02:22:42\nFinish: 11th June 2025 02:22:42\nReport URL: https:\/\/api-creatia.securelayer.com\/api\/guest\/report\/download\/SYueoVQnSgRniFRTOKo7VXEE5MsfdeA1DjXfjxJUuYIsFx1x3TeFSnaSO9VTXeS96ruuay0F0BH5J6g69ILFmzxnAM2q4sANpTqqrTkm7tufPm11Qe5AGJkYBBBtRfga"}
export function mapGslToAttack(data: any): Partial<PullFlowHistory> {
  const { content } = data;

  // 解析基本信息
  const destinationMatch = content.match(/Destination: ([\d\.\/]+)/);
  const destination = destinationMatch ? destinationMatch[1] : '';

  // 解析开始时间
  const startTimeMatch = content.match(/Started At: ([\d\-]+ [\d:]+ UTC)/);
  const startTime = startTimeMatch ? dayjs(startTimeMatch[1]).format('YYYY-MM-DD HH:mm:ss') : '';

  // 解析结束时间
  const concludedTimeMatch = content.match(/Concluded At: ([\d\-]+ [\d:]+ UTC)/);
  const concludedTime = concludedTimeMatch ? dayjs(concludedTimeMatch[1]).format('YYYY-MM-DD HH:mm:ss') : '';

  // 解析 Gbps
  const gbpsMatch = content.match(/Gbps: ([\d\.]+)/);
  const gbps = gbpsMatch ? gbpsMatch[1] : '';

  // 解析 Reference
  const referenceMatch = content.match(/Reference: ([A-Z0-9\-]+)/);
  const reference = referenceMatch ? referenceMatch[1] : '';

  // 解析报告 URL
  const reportUrlMatch = content.match(/Report URL: (https:\/\/[^\s]+)/);
  const reportUrl = reportUrlMatch ? reportUrlMatch[1] : '';

  // 将 Gbps 转换为 bps
  const bps = gbps ? (parseFloat(gbps) * 1000000000).toString() : '0';

  // 计算 PPS（每秒包数，1460字节包）
  const pps = gbps ? (parseFloat(gbps) * 1000000000 / 11680).toFixed(0) : '0';

  return {
    uuid: reference,
    platformUuid: reference,
    firstTime: startTime,
    lastTime: concludedTime,
    pullTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
		ip: destination.split('/')[0],
    bps: bps,
		pps,
    source: 'GSL',
    detail: {
      gbps,
      reference,
      reportUrl,
    },
  };
}

// // 测试用例
// const testCases = [
// 	{
// 		content:
// 			'An attack has met the configured threshold to trigger an alert.\n' +
// 			'Destination: ***************/32\n' +
// 			'Started At: 2025-06-17 02:44:31 UTC\n' +
// 			'Gbps: 1.47\n' +
// 			'Reference: S-0001AA-000007641711',
// 		platform: 'GSL',
// 	},
// 	{
// 		content:
// 			'An attack has met the configured threshold to trigger an alert.\n' +
// 			'Destination: ***************/32\n' +
// 			'Started At: 2025-06-17 02:44:31 UTC\n' +
// 			'Gbps: 1.47\n' +
// 			'Reference: S-0001AA-000007641711',
// 		platform: 'GSL',
// 	},
// 	{ content: 'Example Webhook Payload', platform: 'GSL' },
// 	{
// 		content:
// 			'An attack has meeting the threshold has concluded. A report will soon be generated if the attack was large enough.\n' +
// 			'Destination: ***************/32\n' +
// 			'Concluded At: 2025-06-17 02:46:31 UTC\n' +
// 			'Gbps: 2.65\n' +
// 			'Reference: F-0001AA-000007641711',
// 		platform: 'GSL',
// 	},
// 	{
// 		content:
// 			'The report for a single destination attack is ready. Information is as follows:\n' +
// 			'Destination: ***************/32\n' +
// 			'Start: 17th June 2025 02:44:31\n' +
// 			'Finish: 17th June 2025 02:44:31\n' +
// 			'Report URL: https://api-creatia.securelayer.com/api/guest/report/download/eNIVfzN6Uekvi7vzEmCvL59wI4kzFZvfwItVUINaV7veFcSUl2OEsM3HQlRybxuF0xXbzuUsVGwHOzBOqpkBAvLwCQFShrkiALgA7iNPqOA45o0trrod1dgPOPk7E74K',
// 		platform: 'GSL',
// 	},
// ]

// // 测试每个用例
// testCases.forEach((testCase, index) => {
//   console.log(mapGslToAttack(testCase));//
// });

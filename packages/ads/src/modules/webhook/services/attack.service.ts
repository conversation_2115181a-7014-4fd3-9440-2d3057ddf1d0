import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { mapGslToAttack } from '../utils/map-attack-data';
import { PullFlowHistory } from '@/modules/pull/entities/pull_flow_history';

@Injectable()
export class AttackService {
  constructor(
    @InjectRepository(PullFlowHistory)
    private readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
  ) {}

  async receiveGsl(body: any) {
    const data = mapGslToAttack(body);

    try {
      const pullFlowHistory = this.pullFlowHistoryRepository.create(data);
      await this.pullFlowHistoryRepository.save(pullFlowHistory);
    } catch (error) {
      console.log(error);
      return { success: false, message: `save pull flow history error: ${error.message}` };
    }

    return { success: true, message: 'save pull flow history success' };
  }
}

import { Injectable } from '@nestjs/common';

import { isArray } from 'lodash';
import { isEmpty, isExist } from '@/utils/types';
import { IPHistoryService } from '@/modules/flow/service/ip-history.service';
import { IpAddressAndSubnetService } from '@/modules/sync-ops/services/get';
import { PreciseSubnetService } from '@/common/service/precise-subnet.service';

import { CreateIPFlowDto } from './dto/create-ip-flow.dto';
import { mapIPFlow } from './utils/map-ip-flow';

@Injectable()
export class ReceiveIPService {
  constructor(
    // 业务通用的服务
    private preciseSubnet: PreciseSubnetService,
    private ipHistoryService: IPHistoryService,
    private ipAddressAndSubnetService: IpAddressAndSubnetService,
  ) {}

  /**
   * 接收 IP 流量数据，并进行数据存储
   *  - 映射 NDF(dto) 字段 为数据库字段
   *  - 获取 OPS IP 信息
   *  - 获取当前最精确IP config
   *  - 保存到 Database
   */
  async handleReceiveIp(dtos: CreateIPFlowDto[]) {
    const results = dtos.map((dto) => mapIPFlow(dto));

    if (isEmpty(results)) return;

    try {
      const originIpAddressList = await this.ipAddressAndSubnetService.getIpAddressAndIpSubnetList(
        results.map((item) => item.ipadd),
      );

      const ipAddressList = originIpAddressList.map((item) => ({
        ...item,
        customer: item.customerNo,
      }));

      if (isArray(ipAddressList) && isExist(ipAddressList)) {
        results.forEach((item) => {
          const ipAddressItem = ipAddressList.find((ipAddressItem) => {
            return ipAddressItem.ipAddress === item.ipadd;
          });

          if (ipAddressItem) Object.assign(item, ipAddressItem);
        });
      }
    } catch (error) {
      console.log(error.message);
    }

    await this.preciseSubnet.bindPreciseSubnet(results, 'ipadd', 'IP');
    const insertResult = await this.ipHistoryService.save(results);
    return insertResult.identifiers;
  }
}

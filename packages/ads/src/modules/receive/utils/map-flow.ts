import { chain, isFunction, isString } from 'lodash';
import * as Big from 'big.js';
import * as dayjs from 'dayjs';
import { isEmpty } from 'src/utils/types';

export function getDuration(t_first, t_last) {
  const duration = dayjs(t_last).diff(dayjs(t_first), 'ms');
  return new Big(duration).div(1000).toFixed(3);
}

export function isEqZero(duration) {
  return new Big(duration).eq(0);
}

export function mapFlow(dto, kvmap: Record<string, any>) {
  return chain(kvmap)
    .pickBy((v) => isFunction(v) || !isEmpty(v))
    .mapValues((v) => (isString(v) ? dto[v] : v(dto)))
    .value();
}

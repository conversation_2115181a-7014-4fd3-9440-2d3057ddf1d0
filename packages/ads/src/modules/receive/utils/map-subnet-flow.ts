import * as Big from 'big.js';
import { dayjs } from 'src/utils/dayjs';
import { mapFlow, getDuration, isEqZero } from './map-flow';

const kvmap = {
  ip: 'dst4_addr',
  af: '',
  totalFlows: '',
  srcDevice: 'src_device',
  fwdStatus: 'fwd_status',
  firstTime: 't_first',
  lastTime: 't_last',
  totalBytes: 'in_bytes',
  totalPackets: 'in_packets',
  upstream: 'upstream',
  duration: 'duration',
  srcLocation: ({ src_device }) => {
    // 获取原设备第一个 '-' 之前的字符
    const index = src_device?.indexOf('-');
    if (index !== -1) {
      return src_device.substring(0, index);
    }
    return ''; // 如果没有找到 '-', 返回空字符串
  },
  avgBps: ({ t_first, t_last, in_bytes }) => {
    const duration = getDuration(t_first, t_last) || 1;
    if (isEqZero(duration)) return 0;

    const bytes = new Big(in_bytes);
    return bytes.times(8).div(duration).toFixed(2);
  },
  avgBpp: ({ in_packets, in_bytes }) => {
    const packets = new Big(in_packets);
    const bytes = new Big(in_bytes);
    return bytes.div(packets).toFixed(2);
  },
  avgPps: ({ t_first, t_last, in_packets }) => {
    const duration = getDuration(t_first, t_last);
    if (isEqZero(duration)) return 0;

    const packets = new Big(in_packets);
    return packets.div(duration).toFixed(2);
  },
  createTime: () => dayjs().toDate(),
};

export function mapSubnetFlow(createSubnetFlowDto: any) {
  return mapFlow(createSubnetFlowDto, kvmap);
}

import * as Big from 'big.js';
import { dayjs } from 'src/utils/dayjs';
import { mapFlow, getDuration, isEqZero } from './map-flow';
import bigUtils from 'src/utils/big';

const kvmap = {
  protocol: 'proto',
  ipadd: 'dst4_addr',
  srcAddr: 'src4_addr',
  srcPort: 'src_port',
  dstAddr: 'dst4_addr',
  dstPort: 'dst_port',
  fwdStatus: 'tcp_flags',
  tcpFlags: 'fwd_status',
  inputIfIndex: 'input_snmp',
  srcAs: 'src_as',
  dstAs: 'dst_as',
  srcDevice: 'src_device',
  nfdFilePath: 'nfd_file_path',
  firstTime: 't_first',
  lastTime: 't_last',
  upstream: 'upstream',
  duration: 'duration',
  srcLocation: ({ src_device }) => {
    // 获取原设备第一个 '-' 之前的字符
    const index = src_device?.indexOf('-');
    if (index !== -1) {
      return src_device.substring(0, index);
    }
    return ''; // 如果没有找到 '-', 返回空字符串
  },
  bytes: ({ in_bytes }) => {
    return bigUtils.convertMetricSuffix(in_bytes);
  },
  packets: ({ in_packets }) => {
    return bigUtils.convertMetricSuffix(in_packets);
  },
  bps: ({ t_first, t_last, in_bytes }) => {
    const duration = getDuration(t_first, t_last) || 1;
    if (isEqZero(duration)) return 0;

    const bytes = new Big(bigUtils.convertMetricSuffix(in_bytes));
    return bytes.times(8).div(duration).toFixed(2);
  },
  bpp: ({ in_packets, in_bytes }) => {
    const packets = new Big(bigUtils.convertMetricSuffix(in_packets));
    const bytes = new Big(bigUtils.convertMetricSuffix(in_bytes));
    return bytes.div(packets).toFixed(2);
  },
  pps: ({ t_first, t_last, in_packets }) => {
    const duration = getDuration(t_first, t_last);
    if (isEqZero(duration)) return 0;

    const packets = new Big(bigUtils.convertMetricSuffix(in_packets));
    return packets.div(duration).toFixed(2);
  },
  createTime: () => dayjs().toDate(),
};

export function mapIPFlow(dto: any) {
  return mapFlow(dto, kvmap);
}

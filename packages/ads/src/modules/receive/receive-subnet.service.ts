import { Injectable } from '@nestjs/common';
import { isArray, pick } from 'lodash';

import { isEmpty, isExist } from '@/utils/types';
import { IpAddressAndSubnetService } from '@/modules/sync-ops/services/get';
import { SubnetHistoryService } from '@/modules/flow/service/subnet-history.service';
import { PreciseSubnetService } from '@/common/service/precise-subnet.service';

import { mapSubnetFlow } from './utils/map-subnet-flow';
import { CreateSubnetFlowDto } from './dto/create-subnet-flow.dto';
import { logger } from '@/utils/logger';

/**
 * ReceiveService：处理 NFD 服务器推送数据
 */
@Injectable()
export class ReceiveSubnetService {
  constructor(
    // 业务通用的服务
    private readonly preciseSubnet: PreciseSubnetService,
    private readonly subnetHistoryService: SubnetHistoryService,
    private ipAddressAndSubnetService: IpAddressAndSubnetService,
  ) {}

  /**
   * 接收 Subnet 流量数据
   *  - 映射数据库字段
   *  - 获取OPS IP数据，遍历
   *  - 获取最精确匹配的IP配置，遍历
   *  - 保存DB
   */
  async handleReceiveSubnet(dtos: CreateSubnetFlowDto[]) {
    const results = dtos.map((dto) => mapSubnetFlow(dto));
    if (isEmpty(results)) return;

    try {
      const ipSubnetList = await this.ipAddressAndSubnetService.getIpSubnetList(results.map((item) => item.ip));

      if (isArray(ipSubnetList) && isExist(ipSubnetList)) {
        results.forEach((item) => {
          const ipSubnetItem = ipSubnetList.find((ipSubnet) => {
            return ipSubnet.subnet === item.ip;
          });
          if (ipSubnetItem) {
            item.customer = ipSubnetItem.customerNo;
            item.originAsn = ipSubnetItem.originAsn;
            item.productName = ipSubnetItem.productName;
            item.location = ipSubnetItem.location;
            item.serviceNo = ipSubnetItem.serviceNo;
            item.serviceNo2 = ipSubnetItem.serviceNo2;
            item.description = ipSubnetItem.description;
          }
        });
      }
    } catch (error) {
      logger.error(`同步NFD Subnet信息失败，错误信息：${error.message}`);
    }

    await this.preciseSubnet.bindPreciseSubnet(results, 'ip', 'Subnet');

    try {
      // 3.保存到 Database
      const insertResult = await this.subnetHistoryService.save(results);
      return insertResult.identifiers;
    } catch (err) {
      console.error(err.message);
    }
  }
}

import { Body, Controller, Post } from '@nestjs/common';
import { CreateIPFlowDto } from './dto/create-ip-flow.dto';
import { CreateSubnetFlowDto } from './dto/create-subnet-flow.dto';
import { ReceiveIPService } from './receive-ip.service';
import { ReceiveSubnetService } from './receive-subnet.service';

@Controller('receive/flow')
export class ReceiveFlowController {
  constructor(
    private readonly receiveIpService: ReceiveIPService,
    private readonly receiveSubnetService: ReceiveSubnetService,
  ) {}

  /**
   * 接收 IP 流量数据
   * Url: /receive/flow/ip
   * Method: POST
   * @param dtos IP Flows
   * @returns
   */
  @Post('ip')
  async receiveIP(@Body(/* receiveIPPipe */) dtos: CreateIPFlowDto[]) {
    if (!dtos.length) return;
    // 调用 ReceiveService 的 receiveIPFlow 方法处理 IP Flow 数据
    return this.receiveIpService.handleReceiveIp(dtos);
  }

  /**
   * 接收子网流量数据
   * Url: /receive/flow/subnet
   * Method: POST
   * @param dtos Subnet Flows
   * @returns
   */
  @Post('subnet')
  async receiveSubnetFlow(@Body(/* receiveSubnetPipe */) dtos: CreateSubnetFlowDto[]) {
    if (!dtos.length) return;
    // 调用 ReceiveService 的 receiveSubnetFlow 方法处理 Subnet Flow 数据
    return this.receiveSubnetService.handleReceiveSubnet(dtos);
  }
}

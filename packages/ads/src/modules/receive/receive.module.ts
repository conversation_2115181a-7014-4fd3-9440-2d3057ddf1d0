import { Module } from '@nestjs/common';
import { AnalyzeModule } from 'src/modules/analyze/analyze.module';
import { ReceiveFlowController } from './receive.controller';
import { ActionModule } from 'src/modules/action/action.module';
import { FlowModule } from 'src/modules/flow/flow.module';
import { RedisModule } from 'src/base';
import { CommonModule } from 'src/common/common.module';
import { ReceiveIPService } from './receive-ip.service';
import { ReceiveSubnetService } from './receive-subnet.service';
import { SyncOPSModule } from '../sync-ops/sync-ops.module';

@Module({
  imports: [RedisModule, CommonModule, AnalyzeModule, ActionModule, FlowModule, SyncOPSModule],
  controllers: [ReceiveFlowController],
  providers: [ReceiveIPService, ReceiveSubnetService],
})
export class ReceiveModule {}

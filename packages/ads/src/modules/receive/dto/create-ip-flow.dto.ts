import { IsDateString, IsIP, IsNumber, IsString } from 'class-validator';

export class CreateIPFlowDto {
  @IsString()
  type: string;

  @IsNumber()
  sampled: number;

  @IsNumber()
  export_sysid: number;

  @IsDateString()
  t_first: Date;

  @IsDateString()
  t_last: Date;

  @IsNumber()
  proto: number;

  @IsIP()
  src4_addr: string;

  @IsIP()
  dst4_addr: string;

  @IsNumber()
  src_port: number;

  @IsNumber()
  dst_port: number;

  @IsNumber()
  fwd_status: number;

  @IsString()
  tcp_flags: string;

  @IsNumber()
  src_tos: number;

  @IsNumber()
  in_packets: number;

  @IsNumber()
  in_bytes: number;

  @IsNumber()
  input_snmp: number;

  @IsNumber()
  output_snmp: number;

  @IsNumber()
  src_as: number;

  @IsNumber()
  dst_as: number;

  @IsNumber()
  src_mask: number;

  @IsNumber()
  dst_mask: number;

  @IsNumber()
  dst_tos: number;

  @IsNumber()
  direction: number;

  @IsNumber()
  src_vlan: number;

  @IsNumber()
  dst_vlan: number;

  @IsString()
  in_src_mac: string;

  @IsString()
  out_dst_mac: string;

  @IsString()
  mpls_1: string;

  @IsString()
  mpls_2: string;

  @IsString()
  mpls_3: string;

  @IsString()
  mpls_4: string;

  @IsString()
  mpls_5: string;

  @IsString()
  mpls_6: string;

  @IsString()
  mpls_7: string;

  @IsString()
  mpls_8: string;

  @IsString()
  mpls_9: string;

  @IsString()
  mpls_10: string;

  @IsDateString()
  t_received: Date;

  @IsIP()
  ip4_next_hop: string;

  @IsIP()
  bgp4_next_hop: string;

  @IsIP()
  ip4_router: string;

  @IsString()
  label: string;
}

// {
// 	"type" : "FLOW",
// 	"sampled" : 1,
// 	"export_sysid" : 1,
// 	"t_first" : "2024-05-29T09:35:28.665",
// 	"t_last" : "2024-05-29T09:35:28.665",
// 	"proto" : 6,
// 	"src4_addr" : "*************",
// 	"dst4_addr" : "**************",
// 	"src_port" : 34714,
// 	"dst_port" : 44321,
// 	"fwd_status" : 0,
// 	"tcp_flags" : "...AP...",
// 	"src_tos" : 0,
// 	"in_packets" : 400000,
// 	"in_bytes" : 41200000,
// 	"input_snmp" : 560,
// 	"output_snmp" : 556,
// 	"src_as" : 0,
// 	"dst_as" : 0,
// 	"src_mask" : 0,
// 	"dst_mask" : 0,
// 	"dst_tos" : 0,
// 	"direction" : 0,
// 	"src_vlan" : 270,
// 	"dst_vlan" : 0,
// 	"in_src_mac" : "80:b5:75:42:03:02",
// 	"out_dst_mac" : "ac:78:d1:3f:00:13",
// 	"mpls_1" : "0-0-0",
// 	"mpls_2" : "0-0-0",
// 	"mpls_3" : "0-0-0",
// 	"mpls_4" : "0-0-0",
// 	"mpls_5" : "0-0-0",
// 	"mpls_6" : "0-0-0",
// 	"mpls_7" : "0-0-0",
// 	"mpls_8" : "0-0-0",
// 	"mpls_9" : "0-0-0",
// 	"mpls_10" : "0-0-0",
// 	"t_received" : "2024-05-29T09:35:28.665",
// 	"ip4_next_hop" : "0.0.0.0",
// 	"bgp4_next_hop" : "0.0.0.0",
// 	"ip4_router" : "*************",
// 	"label" : "<none>"
// }

import { Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';

import { isArray } from 'lodash';
import { DataSource, Not, Repository } from 'typeorm';

import { logger } from '@/utils/logger';
import { getDifferences } from '@/utils/differences';
import { ipaddHttp } from '@/utils/http/axios/modules/ipadd';
import { IPConfig } from '../entities/ip-config.entity';

@Injectable()
export class IpConfigScheduleService {
  constructor(
    @InjectRepository(IPConfig)
    private readonly ipconfigRepository: Repository<IPConfig>,

    private readonly dataSource: DataSource,
  ) {}

  // 每天执行一次同步OPS subnet和ADS IPCOnfig，保证数据一致性
  @Cron('0 0 * * *')
  async cronHandler() {
    try {
      await this.handler();
    } catch (error) {
      logger.error(`执行OPS/ADS IP Config同步失败，错误信息：${error.message}`);
    }
  }

  async handler() {
    const opsSubnet = await ipaddHttp.getOpsSubnet();
    const configs = await this.ipconfigRepository.find({
      select: ['id', 'ip', 'prefixLength'],
      where: {
        ip: Not('0.0.0.0'),
      },
    });

    const result = getDifferences(opsSubnet, configs, (item) => `${item.ip}/${item.prefixLength}`);

    const { aItems, uItems } = result;

    // 新增
    if (isArray(aItems) && aItems.length > 0) {
      const addItems = aItems.map((i) => {
        const { ip, prefixLength, originAsn, customer, location, product, productNo, productName } = i;

        return {
          ip: ip.trim(),
          prefixLength,
          originAsn,
          customer,
          location,
          product,
          productNo,
          productName,
        };
      });

      this.ipconfigRepository.createQueryBuilder().insert().values(addItems).execute();
    }

    // 更新
    if (isArray(uItems) && uItems.length) {
      this.dataSource.transaction(async (runner) => {
        const promises = uItems.map(async (item) => {
          const { ip, prefixLength, originAsn, customer, location, product, productNo, productName } = item;

          return runner
            .createQueryBuilder()
            .update(IPConfig)
            .set({
              originAsn,
              customer,
              location,
              product,
              productNo,
              productName,
            })
            .where('ip = :ip && prefixLength = :prefixLength', {
              ip,
              prefixLength,
            })
            .execute();
        });

        await Promise.all(promises);
      });
    }
  }
}

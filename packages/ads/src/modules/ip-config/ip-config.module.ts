import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IPConfig } from './entities/ip-config.entity';
import { IpConfigService } from './ip-config.service';
import { IpConfigController } from './ip-config.controller';
import { RedisModule } from 'src/base';
import { IpConfigScheduleService } from './schedule/ip-config.shedule';

@Module({
  imports: [TypeOrmModule.forFeature([IPConfig]), RedisModule],
  controllers: [IpConfigController],
  providers: [IpConfigService, IpConfigScheduleService],
  exports: [IpConfigService],
})
export class IpConfigModule {}

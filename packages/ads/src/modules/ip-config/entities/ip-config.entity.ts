import { AnalyzePolicy } from 'src/modules/analyze/entities/analyze-policy.entity';
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn, ManyToOne, Index } from 'typeorm';
@Entity('op_ads_ip_config')
@Index(['ip', 'prefixLength'])
export class IPConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    comment: '状态，Active：当前配置有效，Inactive：当前配置无效',
    default: 'Active',
  })
  status: 'Active' | 'Inactive';

  @ManyToOne(() => AnalyzePolicy, { nullable: true })
  ipAnalyzePolicy: AnalyzePolicy;
  @Column({ comment: '分析策略 ID', nullable: true })
  ipAnalyzePolicyId: number;

  @ManyToOne(() => AnalyzePolicy, { nullable: true })
  subnetAnalyzePolicy: AnalyzePolicy;
  @Column({ comment: '分析策略 ID', nullable: true })
  subnetAnalyzePolicyId: number;

  @Column({ comment: 'IP 地址' })
  ip: string;

  @Column({ comment: '掩码长度', type: 'int' })
  prefixLength: number;

  @Column({ comment: 'IP 列表描述', default: '' })
  description: string;

  @Column({ comment: '客户', default: '', nullable: true })
  customer: string;

  @Column({ comment: 'ASN', default: '', nullable: true })
  originAsn: string;

  @Column({ comment: 'Product', default: '', nullable: true })
  product: string;

  @Column({ comment: 'Product No', default: '', nullable: true })
  productNo: string;

  @Column({ comment: 'Product Name', default: '', nullable: true })
  productName: string;

  @Column({ comment: '机房', default: '', nullable: true })
  location: string;

  @Column({ comment: '创建用户', default: '' })
  createBy: string;

  @Column({ comment: '更新用户', default: '' })
  updateBy: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

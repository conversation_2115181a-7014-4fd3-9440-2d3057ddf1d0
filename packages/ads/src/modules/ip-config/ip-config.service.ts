import type { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { selectBuilder, defineOption } from 'src/utils/orm/builder';
import { IPConfig } from './entities/ip-config.entity';
import { AnalyzePolicy } from '../analyze/entities/analyze-policy.entity';

@Injectable()
export class IpConfigService {
  constructor(
    @InjectRepository(IPConfig)
    private readonly ipConfigRepository: Repository<IPConfig>,
  ) {}

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async page(query: any) {
    const option = defineOption({
      select: [
        'a.*',
        `CONCAT(a.ip, '/', a.prefixLength) selectKey`,
        'b.name ipAnalyzePolicyName',
        'c.name subnetAnalyzePolicyName',
      ],
      fieldLike: [
        { column: 'a.ip', requestParam: 'ip' },
        { column: 'a.product', requestParam: 'product' },
      ],
      fieldEq: [
        { column: 'a.id', requestParam: 'id' },
        { column: 'a.type', requestParam: 'type' },
        { column: 'a.status', requestParam: 'status' },
        { column: 'a.customer', requestParam: 'customer' },
      ],
      keyWordLikeFields: ['a.description', 'a.originAsn', 'a.ip'],
      join: [
        {
          entity: AnalyzePolicy,
          type: 'leftJoin',
          alias: 'b',
          condition: 'a.ipAnalyzePolicyId = b.id',
        },
        {
          entity: AnalyzePolicy,
          type: 'leftJoin',
          alias: 'c',
          condition: 'a.subnetAnalyzePolicyId = c.id',
        },
      ],
      extend: (builder) => {
        const { analyzePolicyId } = query;
        if (analyzePolicyId) {
          builder.andWhere(`(b.id IN (${analyzePolicyId}) OR c.id IN (${analyzePolicyId}))`);
        }
      },
    });
    const builder = this.ipConfigRepository.createQueryBuilder('a');
    return selectBuilder.page(query, option, builder);
  }

  async create(data: IPConfig) {
    data.ip = data.ip?.trim();

    const { raw } = await this.ipConfigRepository.insert(data);

    return {
      id: raw.insertId,
    };
  }

  async update(id: number, data: IPConfig) {
    const findWhere: any = {};
    data.ip = data.ip?.trim();

    if (id) {
      findWhere.id = id;
    } else if (data.ip && data.prefixLength) {
      findWhere.ip = data.ip;
      findWhere.prefixLength = data.prefixLength;
    } else {
      throw new Error('没有传入唯一标识');
    }

    const conf = await this.ipConfigRepository.findOne({ where: findWhere });

    data.id = conf?.id;
    await this.ipConfigRepository.save(data);
  }

  /**
   * 删除
   */
  async delete(id: number | number[]) {
    id = Array.isArray(id) ? id : [id];
    await this.ipConfigRepository.delete(id);
  }

  async deleteByIPAndPrefixLength(ip: string, prefixLength: number) {
    return this.ipConfigRepository
      .createQueryBuilder()
      .delete()
      .where('ip = :ip', { ip })
      .andWhere('prefixLength = :prefixLength', { prefixLength })
      .execute();
  }

  /**
   * 详情
   */
  async info(id: number) {
    return this.ipConfigRepository.findOne({ where: { id } });
  }

  /**
   * 批量更新
   */
  async handleBatchUpdate(ids: Array<number>, data: any) {
    // 更新数据库
    await this.ipConfigRepository.save(ids.map((id) => ({ id, ...data })));
  }
}

import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { IpConfigService } from './ip-config.service';

@Controller('ip-config')
export class IpConfigController {
  constructor(private readonly ipConfigService: IpConfigService) {}

  @Post('page')
  async page(@Body() query) {
    return await this.ipConfigService.page(query);
  }

  @Post('add')
  async add(@Body() data) {
    return await this.ipConfigService.create(data);
  }

  @Post('update')
  async update(@Body() data) {
    return await this.ipConfigService.update(data.id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return await this.ipConfigService.delete(ids);
  }

  @Post('deleteByIPAndPrefixLength')
  async deleteByIPAndPrefixLength(@Body('ip') ip: string, @Body('prefixLength') prefixLength: number) {
    return this.ipConfigService.deleteByIPAndPrefixLength(ip, prefixLength);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return await this.ipConfigService.info(id);
  }

  @Post('batch/update')
  async handleBatchUpdate(@Body() body) {
    const { ids, ...data } = body;
    return await this.ipConfigService.handleBatchUpdate(ids, data);
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminLog } from './entities/admin-log.entity';
import { AdminLogController } from './controller/admin-log.controller';
import { AdminLogService } from './service/admin-log.service';
import { PushLog } from './entities/push-log.entity';
import { PushLogController } from './controller/push-log.controller';
import { PushLogService } from './service/push-log.service';

@Module({
  imports: [TypeOrmModule.forFeature([AdminLog, PushLog])],
  controllers: [AdminLogController, PushLogController],
  providers: [AdminLogService, PushLogService],
  exports: [AdminLogService, PushLogService],
})
export class AdminLogModule {}

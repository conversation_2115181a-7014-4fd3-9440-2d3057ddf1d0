import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('op_ads_push_log')
export class PushLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '流量类型', default: '' })
  type: string;

  @Column({ comment: '设备名', default: '' })
  srcDevice: string;

  @Column({ comment: '指令', type: 'text', nullable: true })
  command: string;

  @Column({ comment: '文件生成时间', default: '' })
  fileGenerationTime: string;

  @Column({ comment: '文件路径', default: '' })
  filePath: string;

  @Column({ comment: '推送数量', type: 'int', default: 0 })
  pushLength: number;

  @Column({ comment: '接收数量', type: 'int', default: 0 })
  receiveLength: number;

  @Column({ comment: '推送状态', type: 'boolean', default: false })
  status: boolean;

  @Column({ comment: '错误信息', default: '' })
  errorMessage: string;

  // @Column({ comment: '设备IP', default: '' })
  // srcDeviceIpadd: string;

  @Column({ comment: 'Details', type: 'text', nullable: true })
  details: string;

  @CreateDateColumn()
  createTime: Date;
}

import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { AdminLogService } from '../service/admin-log.service';
import { defineOption } from 'src/utils/orm/builder';

@Controller('admin/log')
export class AdminLogController {
  private readonly pageQueryOp = defineOption({
    select: ['a.*'],
  });

  constructor(private readonly adminLogService: AdminLogService) {}

  @Post('page')
  async page(@Query() query: any) {
    return this.adminLogService.find(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data: any) {
    return this.adminLogService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.adminLogService.update(data.id, data);
  }

  @Post('delete')
  async remove(@Body('ids') ids: number | number[]) {
    return this.adminLogService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.adminLogService.findOne(id);
  }
}

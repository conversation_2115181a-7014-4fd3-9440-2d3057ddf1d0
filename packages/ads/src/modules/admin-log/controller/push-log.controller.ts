import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { PushLogService } from '../service/push-log.service';

@Controller('push-log')
export class PushLogController {
  constructor(private readonly pushLogService: PushLogService) {}

  @Get()
  async find(@Query() query: any) {
    return this.pushLogService.find(query);
  }

  @Post()
  async create(@Body() data: any) {
    return this.pushLogService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.pushLogService.update(data.id, data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[]) {
    return this.pushLogService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.pushLogService.findOne(id);
  }
}

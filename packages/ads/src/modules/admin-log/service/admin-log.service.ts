import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AdminLog } from '../entities/admin-log.entity';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
@Injectable()
export class AdminLogService {
  constructor(
    @InjectRepository(AdminLog)
    private readonly adminLogRepository: Repository<AdminLog>,
  ) {}

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    return selectBuilder.page(query, option, this.adminLogRepository.createQueryBuilder('a'));
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AdminLog) {
    return this.adminLogRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: AdminLog) {
    await this.adminLogRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.adminLogRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.adminLogRepository.findOne({ where: { id } });
  }
}

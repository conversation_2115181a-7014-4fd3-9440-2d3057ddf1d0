import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Repository } from 'typeorm';

import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';
import { PushLog } from '../entities/push-log.entity';

@Injectable()
export class PushLogService {
  constructor(
    @InjectRepository(PushLog)
    private readonly pushLogRepository: Repository<PushLog>,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async cronRemove() {
    try {
      await this.pushLogRepository
        .createQueryBuilder()
        .delete()
        .where('createTime < :time', {
          time: dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
        })
        .execute();
    } catch (error) {
      logger.error(`任务: 每天执行删除7天前的推送日志失败, 失败信息: ${error.message}`);
    }
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any) {
    return this.pushLogRepository.find({ where: query });
  }

  /**
   * 新增
   * @param data
   */
  async create(data: PushLog) {
    data.details = data.details ? JSON.stringify(data.details) : null;
    return this.pushLogRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: PushLog) {
    await this.pushLogRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.pushLogRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.pushLogRepository.findOne({ where: { id } });
  }
}

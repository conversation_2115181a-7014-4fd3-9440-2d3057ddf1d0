import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, Index } from 'typeorm';

@Entity('op_ads_analyze_group_result_history')
export class AnalyzeGroupResultHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ comment: '父级ID', nullable: true })
  parentId: number;

  @Index()
  @Column({ comment: '程序拼接的唯一标识', default: '' })
  itemName: string;

  @Index()
  @Column({ comment: '分析组ID', nullable: true })
  analyzeGroupId: number;

  @Column({ comment: '分析组匹配的TermID', nullable: true })
  matchTermId: number;

  @Column({ comment: '操作ID', nullable: true })
  actionId: number;

  @Index()
  @Column({ comment: '客户', default: '', nullable: true })
  customer: string;

  @Index()
  @Column({ comment: 'ASN', default: '', nullable: true })
  originAsn: string;

  @Index()
  @Column({ comment: 'Product No', default: '', nullable: true })
  productNo: string;

  @Index()
  @Column({ comment: 'Product Name', default: '', nullable: true })
  productName: string;

  @Index()
  @Column({ comment: 'Upstream', default: '', nullable: true })
  upstream: string;

  @Index()
  @Column({ comment: 'Src Device', default: '', nullable: true })
  srcDevice: string;

  @Index()
  @Column({ comment: '机房', default: '', nullable: true })
  location: string;

  @Column({ comment: 'IP段', type: 'text', nullable: true })
  subnet: string;

  @Column({ comment: 'IP', type: 'text', nullable: true })
  ipadd: string;

  @Column({
    comment: 'Bytes',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bytes: number;

  @Column({
    comment: 'BPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bps: number;

  @Column({
    comment: 'PPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  pps: number;

  @Column({ comment: 'IP个数', type: 'int', default: 0 })
  addressCount: number;

  @Column({ comment: '异常IP个数', type: 'int', default: 0 })
  abnormalCount: number;

  @Column({ comment: '匹配的IP个数', type: 'int', default: 0 })
  matchItemCount: number;

  @Index()
  @Column({ comment: 'Severity', default: '' })
  severity: string;

  @Column({ comment: 'matchTermFields', default: '' })
  matchTermFields: string;

  @CreateDateColumn()
  createTime: Date;
}

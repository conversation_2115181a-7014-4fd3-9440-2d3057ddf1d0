import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('op_ads_analyze_group_result_latest')
export class AnalyzeGroupResultLatest {
  @PrimaryGeneratedColumn()
  id: number;

  @Index({ unique: true })
  @Column({ comment: '程序拼接的唯一ID', unique: true })
  uuid: string;

  @Index({ unique: true })
  @Column({ comment: '程序拼接的唯一标识', unique: true })
  itemName: string;

  @Index()
  @Column({ comment: '分析组ID', nullable: true })
  analyzeGroupId: number;

  @Column({ comment: '分析组匹配的TermID', nullable: true })
  matchTermId: number;

  @Column({ comment: '操作ID', nullable: true })
  actionId: number;

  @Column({ comment: '是否正常更新', type: 'boolean', default: true })
  isUpdateNormal: boolean;

  @Index()
  @Column({ comment: '客户', default: '', nullable: true })
  customer: string;

  @Index()
  @Column({ comment: 'ASN', default: '', nullable: true })
  originAsn: string;

  @Index()
  @Column({ comment: 'Product Name', default: '', nullable: true })
  productName: string;

  // @Index()
  // @Column({ comment: 'Service No', type: 'text', nullable: true })
  // serviceNo: string;

  // @Index()
  // @Column({ comment: 'Service No2', type: 'text', nullable: true })
  // serviceNo2: string;

  @Index()
  @Column({ comment: '机房', default: '', nullable: true })
  location: string;

  @Index()
  @Column({ comment: 'Upstream', default: '', nullable: true })
  upstream: string;

  @Index()
  @Column({ comment: 'Src Device', default: '', nullable: true })
  srcDevice: string;

  @Column({ comment: 'IP段', type: 'text', nullable: true })
  subnet: string;

  @Column({ comment: 'IP', type: 'text', nullable: true })
  ipadd: string;

  @Column({
    comment: 'Bytes',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bytes: number;

  @Column({
    comment: 'BPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bps: number;

  @Column({
    comment: 'PPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  pps: number;

  @Column({ comment: 'IP个数', type: 'int', default: 0 })
  addressCount: number;

  @Column({ comment: '异常IP个数', type: 'int', default: 0 })
  abnormalCount: number;

  @Column({ comment: '匹配的IP个数', type: 'int', default: 0 })
  matchItemCount: number;

  @Index()
  @Column({ comment: 'Severity', default: '' })
  severity: string;

  @Column({ comment: 'matchTermFields', default: '' })
  matchTermFields: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

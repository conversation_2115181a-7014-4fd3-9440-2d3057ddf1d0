import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { Brackets, SelectQueryBuilder } from 'typeorm';
import { isArray } from 'lodash';
import { defineOption } from 'src/utils/orm/builder';
import { AnalyzeGroupResultLatestService } from '../service/analyze-group-result-latest.service';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { AnalyzePolicy } from 'src/modules/analyze/entities/analyze-policy.entity';
import { isEmpty } from 'src/utils/types';

@Controller('analyze-group-result-latest')
export class AnalyzeGroupResultLatestController {
  private pageQueryOp(query) {
    return defineOption({
      select: ['a.*', 'b.name analyzeGroupName'],
      fieldEq: [
        { column: 'a.isUpdateNormal', requestParam: 'isUpdateNormal' },
        { column: 'a.customer', requestParam: 'customer' },
        { column: 'a.analyzeGroupId', requestParam: 'analyzeGroupId' },
        { column: 'a.severity', requestParam: 'severity' },
      ],
      fieldLike: [
        { column: 'a.subnet', requestParam: 'subnet' },
        { column: 'a.ipadd', requestParam: 'ipadd' },
        { column: 'a.productName', requestParam: 'productName' },
        { column: 'a.originAsn', requestParam: 'originAsn' },
        { column: 'a.itemName', requestParam: 'itemNameLike' },
      ],
      findInSet: [{ column: 'b.groupType', requestParam: 'groupType' }],
      join: [
        {
          entity: AnalyzePolicy,
          alias: 'b',
          type: 'leftJoin',
          condition: 'a.analyzeGroupId = b.id',
        },
      ],
      extend: (builder: SelectQueryBuilder<AnalyzeGroupResultLatest>) => {
        const { location, order, sort } = query;

        if (query.isAllAbnormal) {
          builder.orWhere('a.severity != "" AND a.severity is NOT NULL');
        }

        if (location) {
          const locations = isArray(location) ? location : [location];
          builder.andWhere(
            new Brackets((qb) => {
              locations.forEach((item) => {
                if (item === 'ALL') {
                  qb.orWhere(`a.location != ''`);
                  return;
                }
                qb.orWhere(`FIND_IN_SET("${item}", a.location)`);
              });
            }),
          );
        }

        if (isEmpty(order) && isEmpty(sort)) {
          // builder.addOrderBy('a.firstTime', 'DESC');
          builder
            .orderBy("CASE WHEN a.severity IS NULL OR a.severity = '' THEN 1 ELSE 0 END", 'ASC')
            .addOrderBy('a.severity', 'ASC')
            .addOrderBy('a.updateTime', 'DESC');
        }
      },
    });
  }

  constructor(private readonly analyzeGroupResultLatestService: AnalyzeGroupResultLatestService) {}

  @Get()
  async find(@Query() query: any) {
    return this.analyzeGroupResultLatestService.find(query, this.pageQueryOp(query));
  }

  @Post()
  async create(@Body() data: any) {
    return this.analyzeGroupResultLatestService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.analyzeGroupResultLatestService.update(data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[]) {
    return this.analyzeGroupResultLatestService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.analyzeGroupResultLatestService.findOne(id);
  }
}

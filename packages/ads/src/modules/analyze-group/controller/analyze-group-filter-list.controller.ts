import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { AnalyzeGroupFilterListService } from '../service/analyze-group-filter-list.service';

@Controller('analyze-group/filter')
export class AnalyzeGroupFilterListController {
  constructor(private readonly analyzeGroupFilterListService: AnalyzeGroupFilterListService) {}

  @Get()
  async find(@Query() query: any) {
    return this.analyzeGroupFilterListService.find(query);
  }

  @Post('page')
  async page(@Body() data: any) {
    return this.analyzeGroupFilterListService.find(data);
  }

  @Post('listByGroupId')
  async listByGroupId(@Body() data: any) {
    if (!data.analyzeGroupId) return;
    return this.analyzeGroupFilterListService.findFilterListByAnalyzeGroupId(data);
  }

  @Post('removeMapping')
  async removeMapping(@Body() data: any) {
    if (!data.analyzeGroupId || !data.filterListId) return;
    return this.analyzeGroupFilterListService.removeMapping(data);
  }

  @Post()
  async create(@Body() data: any) {
    return this.analyzeGroupFilterListService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.analyzeGroupFilterListService.update(data.id, data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[]) {
    return this.analyzeGroupFilterListService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.analyzeGroupFilterListService.findOne(id);
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { Brackets } from 'typeorm';
import { defineOption } from 'src/utils/orm/builder';
import { AnalyzeGroupService } from '../service/analyze-group.service';
import { AnalyzeNFDumpService } from '../service/analyze-nfdump.service';
import { isArray } from 'lodash';

@Controller('analyze-group')
export class AnalyzeGroupController {
  pageQueryOp(query) {
    return defineOption({
      select: ['a.*'],
      fieldEq: [
        { column: 'a.type', requestParam: 'type' },
        { column: 'a.isGroup', requestParam: 'isGroup' },
        { column: 'a.isActive', requestParam: 'isActive' },
      ],
      findInSet: [{ column: 'a.groupType', requestParam: 'groupType' }],
      fieldLike: [{ column: 'a.name', requestParam: 'nameLike' }],
      keyWordLikeFields: ['a.name'],
      extend: (builder) => {
        const { applicableScope } = query;

        if (!applicableScope) return;

        const scope = isArray(applicableScope) ? applicableScope : [applicableScope];
        builder.andWhere(
          new Brackets((qb) => {
            scope.forEach((item) => {
              qb.orWhere(`JSON_CONTAINS(a.applicableScope, '["${item}"]')`);
            });
          }),
        );
      },
    });
  }

  constructor(
    private readonly analyzeGroupService: AnalyzeGroupService,
    private readonly analyzeNFDumpService: AnalyzeNFDumpService,
  ) {}

  @Post('analyze')
  async analyze() {
    return this.analyzeNFDumpService.executeAnalyzeGroup();
  }

  @Post('manual/execute/action')
  async manualExecuteAction(
    @Body('id') id: number,
    @Body('actionId') actionId: number,
    @Body('target') targetType: any,
  ) {
    if (!id || !actionId || !targetType) {
      return { code: 1001, message: 'Request ID | ActionID' };
    }

    return this.analyzeGroupService.manualExecute(id, actionId, targetType);
  }

  @Get()
  async find(@Query() query: any) {
    return this.analyzeGroupService.find(query, this.pageQueryOp(query));
  }

  @Post()
  async create(@Body() data: any) {
    return this.analyzeGroupService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.analyzeGroupService.update(data.id, data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[]) {
    return this.analyzeGroupService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.analyzeGroupService.findOne(id);
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { defineOption } from 'src/utils/orm/builder';
import { isArray } from 'lodash';
import { Brackets, SelectQueryBuilder } from 'typeorm';
import { AnalyzeGroupResultHistoryService } from '../service/analyze-group-result-history.service';
import { AnalyzeGroupResultHistory } from '../entities/analyze-group-result-history.entity';
import { AnalyzePolicy } from 'src/modules/analyze/entities';

@Controller('analyze-group-result-history')
export class AnalyzeGroupResultHistoryController {
  private pageQueryOp(query) {
    return defineOption({
      select: ['a.*', 'c.name analyzeGroupName'],
      fieldEq: [
        { column: 'a.isUpdateNormal', requestParam: 'isUpdateNormal' },
        { column: 'a.customer', requestParam: 'customer' },
        { column: 'a.analyzeGroupId', requestParam: 'analyzeGroupId' },
        { column: 'a.severity', requestParam: 'severity' },
      ],
      fieldLike: [
        { column: 'a.productName', requestParam: 'productName' },
        { column: 'a.originAsn', requestParam: 'originAsn' },
        { column: 'a.itemName', requestParam: 'itemNameLike' },
      ],
      findInSet: [{ column: 'a.subnet', requestParam: 'subnet' }],
      join: [
        {
          entity: AnalyzePolicy,
          type: 'leftJoin',
          alias: 'c',
          condition: 'c.id = a.analyzeGroupId',
        },
      ],
      extend: (builder: SelectQueryBuilder<AnalyzeGroupResultHistory>) => {
        const { location } = query;
        if (location) {
          const locations = isArray(location) ? location : [location];
          builder.andWhere(
            new Brackets((qb) => {
              locations.forEach((item) => {
                if (item === 'ALL') {
                  qb.orWhere(`a.location != ''`);
                  return;
                }
                qb.orWhere(`FIND_IN_SET("${item}", a.location)`);
              });
            }),
          );
        }

        builder.andWhere('a.parentId IS NULL');
      },
    });
  }

  constructor(private readonly analyzeGroupResultHistoryService: AnalyzeGroupResultHistoryService) {}

  @Get()
  async find(@Query() query: any) {
    return this.analyzeGroupResultHistoryService.find(query, this.pageQueryOp(query));
  }

  @Get(':id/children')
  async findChildren(@Param('id') id, @Query() query: any) {
    return this.analyzeGroupResultHistoryService.findChildren(id, query);
  }

  @Post()
  async create(@Body() data: any) {
    return this.analyzeGroupResultHistoryService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.analyzeGroupResultHistoryService.update(data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[]) {
    return this.analyzeGroupResultHistoryService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.analyzeGroupResultHistoryService.findOne(id);
  }
}

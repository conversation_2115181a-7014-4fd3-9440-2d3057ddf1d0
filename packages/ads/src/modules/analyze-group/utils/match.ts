import * as Big from 'big.js';
import { chain } from 'lodash';
import { bigUtils } from 'src/utils/big';
import { isEmpty, isExist } from 'src/utils/types';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { AbnormalCurrent } from 'src/modules/abnormal/entities';
import { dayjs } from 'src/utils/dayjs';
import { czLodash } from 'src/utils/lodash';
import { AnalyzeTermItem } from 'src/modules/analyze/entities';

export type MatchDataSource = {
  previousResult?: AnalyzeGroupResultLatest;
  currentAbnormal?: AbnormalCurrent[];
};

// 返回匹配的结果（Term），如果不匹配，返回undefined
export function getMatchTerm(terms: any[], source: any[], dataSource?: MatchDataSource) {
  const values: Record<string, any> = calcValues(source);

  const matchTerm = terms
    .filter((term) => isExist(term.termItems)) // 过滤termItems为空的term
    .find((term) => {
      const currentValues: Record<string, any> = { ...values };
      //
      const isAllMatch = term.termItems.every((item) => {
        // 如果需要使用上一次分析结果，但是没有传入或者上一次分析结果为空，直接返回false
        if (isRequirePrevious(item.classifyMethod) && isEmpty(dataSource.previousResult)) {
          return false;
        }

        const {
          classifyMethod: method,
          classifyField: key,
          stringValue: str = '',
          maxValue: max,
          minValue: min,
        } = item;

        // 针对异常IP数量
        if (key === 'abnormalCount') {
          if (isEmpty(dataSource.currentAbnormal)) return;
          const parseResult = parseCurrentAbnormal(method);

          let minutes: number;
          if (isExist(parseResult)) minutes = parseResult[0];

          const minDate = dayjs().subtract(minutes, 'minutes');
          const currentAbnormal = dataSource.currentAbnormal.filter((abnormal) => {
            if (minutes) return dayjs(abnormal.createTime).isAfter(minDate);
            return true;
          });
          currentValues[key] = currentAbnormal.length;
          return checkValueInRange(currentValues[key], min, max);
        }

        // 针对IP项目匹配数
        if (key === 'matchItemCount' && isExist(item.children)) {
          const matchSourceItem = source.filter((sourceItem) => {
            return (item.children as AnalyzeTermItem[]).every((termItem) => {
              return isMatchTermItem(sourceItem, termItem);
            });
          });

          if (isEmpty(matchSourceItem)) return;
          const matchItemCount = matchSourceItem.length;
          currentValues[key] = matchItemCount;
          return checkValueInRange(matchItemCount, min, max);
        }

        if (key === 'addressCount') {
          currentValues[key] = values[key];
        }

        // 不同方法对value的特殊处理
        switch (method) {
          case 'increaseRate':
            currentValues[key] = calcIncreaseRate(currentValues[key], dataSource.previousResult?.addressCount);
            break;
          case 'decreaseRate':
            currentValues[key] = calcDecreaseRate(currentValues[key], dataSource.previousResult?.addressCount);
            break;
          case 'increaseNum':
            currentValues[key] = calcIncreaseNum(currentValues[key], dataSource.previousResult?.addressCount);
            break;
          case 'decreaseNum':
            currentValues[key] = calcDecreaseNum(currentValues[key], dataSource.previousResult?.addressCount);
            break;
          default:
            currentValues[key] = currentValues[key];
        }

        return isMatchTermItem(currentValues, item);
      });

      if (isAllMatch) Object.assign(values, currentValues);
      return isAllMatch;
    });

  if (matchTerm) matchTerm.values = values;
  return {
    values,
    matchTerm,
  };
}

function isMatchTermItem(values: any, termItem: AnalyzeTermItem) {
  const { classifyMethod: method, classifyField: key, stringValue: str = '' } = termItem;

  let { minValue: min, maxValue: max } = termItem;

  const value = values[key];

  // 对min、max特殊处理，如果存在M、K、G，则进行转换为B
  switch (method) {
    case 'gte':
    case 'gt':
    case 'eq':
    case 'lte':
    case 'lt':
      min = bigUtils.convertMetricSuffix(min);
      break;
    case 'between':
      min = bigUtils.convertMetricSuffix(min);
      max = bigUtils.convertMetricSuffix(max);
      break;
  }

  switch (method) {
    case 'between':
      return +value >= +min && +value <= +max;
    case 'gte':
      return +value >= +min;
    case 'gt':
      return +value > +min;
    case 'eq':
      return +value === +min;
    case 'lte':
      return +value <= +min;
    case 'lt':
      return +value < +min;
    case 'include':
    case 'includes':
    case 'filter':
      return str
        .split(',')
        .map((i) => i.trim())
        .includes(value);
    case 'exclude':
    case 'excludes':
      return !str
        .split(',')
        .map((i) => i.trim())
        .includes(value);
    case 'increaseRate':
    case 'decreaseRate':
    case 'increaseNum':
    case 'decreaseNum':
      return checkValueInRange(value, min, max);
    default:
      return false;
  }
}

const isValidNumber = (num: number) => !isNaN(num) && num > 0;
function checkValueInRange(value, min, max) {
  value = parseFloat(value);
  min = parseFloat(min);
  max = parseFloat(max);

  // 1. 如果 value 不存在，返回 false
  if (!isValidNumber(value)) return false;

  // 2. 如果 min 存在而 max 不存在，返回 value >= min
  if (isValidNumber(min) && !isValidNumber(max)) return value >= min;

  // 3. 如果 max 存在而 min 不存在，返回 value <= max
  if (isValidNumber(max) && !isValidNumber(min)) return value <= max;

  // 4. 如果 min 和 max 都存在，返回 value 是否在 min 和 max 之间（包含边界）
  if (isValidNumber(min) && isValidNumber(max)) {
    return value >= min && value <= max;
  }

  return false; // 如果 min 和 max 都不存在，返回 false
}

// 计算值
const reduceKeys = ['bps', 'pps', 'bytes'];
const joinKeys = [
  'customer',
  'serviceNo',
  'serviceNo2',
  'productName',
  'location',
  'originAsn',
  'upstream',
  'srcDevice',
];
export function calcValues(source: any[]) {
  const values: Record<string, any> = {};

  reduceKeys.forEach((key) => {
    values[key] = bigUtils.reduce(source, key);
  });

  joinKeys.forEach((key) => {
    values[key] = czLodash.joinUniqStringByArray(source, key);
  });

  // addressCount
  values.addressCount = getAddressCount(source);

  return values;
}

// 返回IP个数
function getAddressCount(source: any[]) {
  return chain(source).map('ipadd').uniq().value().length;
}

// 判断当前方法是否需要使用上一次分析的值
function isRequirePrevious(method: string) {
  return ['increaseRate', 'decreaseRate', 'increaseNum', 'decreaseNum'].includes(method);
}

// 返回数字1对于数字2增长的百分比
function calcIncreaseRate(newCount: number, oldCount = 0) {
  const newNum = new Big(newCount);
  const oldNum = new Big(oldCount);
  return newNum.minus(oldNum).div(oldNum).times(100).toFixed(2);
}

// 返回数字1对于数字2减少的百分比
function calcDecreaseRate(newCount: number, oldCount = 0) {
  const newNum = new Big(newCount);
  const oldNum = new Big(oldCount);
  return newNum.minus(oldNum).div(oldNum).times(100).toFixed(2);
}

// 返回数字1对于数字2增加的值
function calcIncreaseNum(newCount: number, oldCount: number) {
  const newNum = new Big(newCount);
  const oldNum = new Big(oldCount);
  return newNum.minus(oldNum).toString();
}

// 返回数字1对于数字2减少的值
function calcDecreaseNum(newCount: number, oldCount: number) {
  const newNum = new Big(newCount);
  const oldNum = new Big(oldCount);
  return oldNum.minus(newNum).toString();
}

// 定义正则表达式模式
const isCurrentAbnormalPattern = /^current abnormal (\d+)$/;
function parseCurrentAbnormal(input) {
  const match = input.match(isCurrentAbnormalPattern); // 测试输入字符串是否匹配模式

  if (match) {
    return [parseInt(match[1], 10)]; // 返回匹配的两个数字
  } else {
    return null; // 如果不匹配，则返回 null
  }
}

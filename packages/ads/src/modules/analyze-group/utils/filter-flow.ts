import { isEmpty } from '@/utils/types';
import {
  AnalyzePolicyFilterList,
  FilterListTypeEnum,
  FilterListActionEnum,
} from 'src/modules/analyze/entities/analyze-policy-filter-list.entity';
import { isIpInSubnets } from 'src/utils/ip';

export function filterByList(filterList: AnalyzePolicyFilterList[], flows: any[]) {
  return filterList.reduce((filteredFlows, filterItem) => {
    const { action, type, value } = filterItem;

    if (isEmpty(value)) return filteredFlows;

    return filteredFlows.filter((flow) => {
      const searchElement = flow[type];
      let matches = false;
      switch (type) {
        case FilterListTypeEnum.ACL:
          matches = isIpInSubnets(
            flow.ipadd,
            value?.split('\n').map((i) => i.trim()),
          );
          break;
        default:
          matches = value
            .split('\n')
            .map((i) => i.trim())
            .includes(searchElement);
          break;
      }

      if (action === FilterListActionEnum.PERMIT) {
        return matches;
      } else if (action === FilterListActionEnum.DENY) {
        return !matches;
      }

      return true;
    });
  }, flows);
}

export function isIncludesNoGrouping(groupType: string) {
  return groupType.includes('noGrouping');
}

export function isIncludesIpaddGroup(groupType: string) {
  return groupType.includes('ipadd');
}

export function isIncludesSubnetGroup(groupType: string) {
  return groupType.includes('subnet') || groupType.includes('subnetc');
}

// 测试
// const flows = [
//   { id: 1, type: 'email', status: 'sent' },
//   { id: 2, type: 'sms', status: 'failed' },
//   { id: 3, type: 'email', status: 'failed' },
// ];
// const values = {
//   type: 'email,sms',
//   status: 'sent',
// };
// const filteredFlows = filterByValues(flows, values);
// console.log(filteredFlows); // [{ id: 1, type: 'email', status: 'sent' }]

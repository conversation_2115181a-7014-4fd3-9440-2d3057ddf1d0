import { filterByList, isIncludesNoGrouping, isIncludesIpaddGroup, isIncludesSubnetGroup } from '../utils/filter-flow';
import { isEmpty, isExist } from 'src/utils/types';
import { chain, forEach } from 'lodash';
import { calcValues, getMatchTerm, MatchDataSource } from '../utils/match';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { isIpInSubnets, toSubnetAddress } from 'src/utils/ip';
import { generateHash } from 'src/utils/uuid';
import { AbnormalHistory, AbnormalHistoryStatusEnum } from 'src/modules/abnormal/entities/abnormal-history.entity';
import { formatTerm, formatValues } from 'src/modules/analyze/utils/filter';
import { czLodash } from 'src/utils/lodash';
import { AnalyzeGroupResultHistory } from '../entities/analyze-group-result-history.entity';

const groupItemMap = new Map([
  ['subnetc', 'subnet'],
  ['asn', 'originAsn'],
]);

export function handleGroupByAny({ source, analyze }) {
  try {
    // 过滤数据
    if (isExist(analyze.filterList)) {
      source = filterByList(analyze.filterList, source);
    }

    if (isEmpty(analyze.groupType)) return;
    const groupType = analyze.groupType as string;
    const groupKeys = groupType.split(',').map((item) => groupItemMap.get(item) ?? item);

    const results = chain(source)
      .filter((sourceItem) => {
        if (isIncludesNoGrouping(groupType)) return true; // 如果组类型为 “by_address_list” 不进行过滤
        return !groupKeys.some((groupItem) => isEmpty(sourceItem[groupItem])); // 如果数据中存在分组项目值为空的，过滤
      })
      .groupBy((sourceItem) => {
        if (isIncludesNoGrouping(groupType)) return '';
        return groupKeys.map((groupItem) => sourceItem[groupItem]).join('-'); // 分组
      })
      .mapValues((groupItemSource, groupValueStr) => {
        const groupValues = groupValueStr.split('-');

        const matchDataSource: MatchDataSource = {};
        // 获取当前分组的/24异常IP

        if (analyze.currentAbnormal) {
          matchDataSource.currentAbnormal = chain(groupItemSource)
            .map((sourceItem) => sourceItem.ipadd as string) // 获取IP
            .map((ipadd) => toSubnetAddress(ipadd)) // 获取IP段（/24）
            .uniq()
            .map((subnet) => {
              return chain(analyze.currentAbnormal) // 获取符合IP段的异常IP
                .filter((abnormal) => isIpInSubnets(abnormal.ipadd, [subnet]))
                .uniqBy('ipadd')
                .value();
            })
            .flatten()
            .filter((item) => isExist(item))
            .value();
        }

        // 获取当前分组项目上一次分析结果
        if (isExist(analyze?.previousResult)) {
          matchDataSource.previousResult = analyze.previousResult.find((pre) => {
            return analyze.id === pre.analyzeGroupId;
          });
        }

        // 进行匹配
        const { values: matchValues, matchTerm } = getMatchTerm(analyze.terms, groupItemSource, matchDataSource);

        // 获取值
        const result = {
          id: matchDataSource.previousResult?.id,
          analyzeGroupId: analyze.id,
          isUpdateNormal: true,
          ipadd: isIncludesIpaddGroup(groupType) ? czLodash.joinUniqStringByArray(groupItemSource, 'ipadd') : null,
          subnet: chain(groupItemSource)
            .map(({ ipadd }) => toSubnetAddress(ipadd))
            .uniq()
            .join(',')
            .value(),
          ...matchValues,
        } as AnalyzeGroupResultLatest & {
          children?: AnalyzeGroupResultHistory[];
          term?: Record<string, any> | null;
        };
        result.itemName = [result.analyzeGroupId, ...groupValues].filter((item) => isExist(item)).join('-');
        result.uuid = generateHash(result.itemName);
        if (isExist(matchTerm)) {
          result.term = isExist(matchTerm) ? { ...formatTerm(matchTerm.termItems), message: matchTerm.message } : null;
          result.matchTermId = matchTerm.id;
          result.actionId = matchTerm.actionPolicyId;
          result.severity = matchTerm.severity || '';
          result.matchTermFields = matchTerm.termItems.map((item) => item.classifyField).join(',');
        } else {
          result.severity = '';
          result.matchTermFields = '';
        }

        if (!isIncludesIpaddGroup(groupType)) {
          result.children = chain(groupItemSource)
            .groupBy((item) => (isIncludesSubnetGroup(groupType) ? item.ipadd : toSubnetAddress(item.ipadd)))
            .map((items, key) => {
              const item = new AnalyzeGroupResultHistory();
              const itemValues = calcValues(items);
              forEach(itemValues, (v, k) => {
                item[k] = v;
              });

              if (isIncludesSubnetGroup(groupType)) item.ipadd = key;
              else item.subnet = key;
              return item;
            })
            .value();
        }

        return result;
      })
      .map((result) => result)
      .filter((result) => isExist(result))
      .value();

    const abnormalHistoryList = chain(results)
      .filter((abnormal) => isExist(abnormal.matchTermId))
      .map((abnormal) => {
        let abnormalGroup: Record<string, any> = {
          ipadd: abnormal.itemName,
          termId: abnormal.matchTermId,
          actionPolicyId: abnormal.actionId,
          analyzePolicyId: analyze.id,
          isGroup: true,
          values: formatValues(abnormal),
          triggeredTermDetail: abnormal.term,
        };

        if (isIncludesIpaddGroup(groupType) && abnormal.ipadd) {
          abnormalGroup = Object.assign({}, abnormalGroup, {
            ipadd: abnormal.ipadd,
            status: AbnormalHistoryStatusEnum.PENDING,
            termId: abnormal.matchTermId,
            upstream: abnormal.upstream,
            actionPolicyId: abnormal.actionId,
            isGroup: false,
          });
        } else if (isIncludesNoGrouping(groupType)) {
        } else {
          abnormalGroup.children = chain(source)
            .filter((item) => isIpInSubnets(item.ipadd, abnormal.subnet))
            .groupBy((item) => toSubnetAddress(item.ipadd))
            .map((v, k) => {
              const subnet = k.split('/')[0];
              return {
                ipadd: subnet,
                status: AbnormalHistoryStatusEnum.PENDING,
                termId: abnormal.matchTermId,
                actionPolicyId: abnormal.actionId,
                analyzePolicyId: analyze.id,
                isGroup: false,
                values: formatValues(calcValues(v)),
                triggeredTermDetail: abnormal.term,
              } as AbnormalHistory;
            })
            .value();
        }

        return abnormalGroup;
      })
      .filter((i) => isExist(i))
      .value();

    return { results, abnormalHistoryList };
  } catch (error) {
    console.error(error);
  }
}

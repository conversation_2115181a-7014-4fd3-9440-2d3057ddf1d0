import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';
import { AnalyzeGroupResultHistory } from '../entities/analyze-group-result-history.entity';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { AnalyzeNFDumpService } from '../service/analyze-nfdump.service';

@Injectable()
export class AnalyzeGroupScheduleService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly analyzeNFDumpService: AnalyzeNFDumpService,
  ) {}

  // 执行分析组
  // 12s
  @Cron('*/20 * * * * *')
  async execute() {
    try {
      await this.analyzeNFDumpService.executeAnalyzeGroup();
    } catch (error) {
      logger.error(`执行分析组IP失败，错误信息：${error.message}`);
    }
  }

  // 每分钟删除组分析纪录
  @Cron(CronExpression.EVERY_MINUTE)
  async deleteOldData() {
    try {
      // 删除7天前的组分析历史记录
      await Promise.all([
        this.dataSource
          .createQueryBuilder()
          .delete()
          .from(AnalyzeGroupResultHistory)
          .where('createTime < :date', {
            date: dayjs().subtract(7, 'days').format('YYYY-MM-DD HH:mm:ss'),
          })
          .execute(),

        // 删除7天前更新的latest记录，并且isUpdateNormal为false（可选）
        this.dataSource
          .createQueryBuilder()
          .delete()
          .from(AnalyzeGroupResultLatest)
          .where('updateTime < :date', {
            date: dayjs().subtract(2, 'days').format('YYYY-MM-DD HH:mm:ss'),
          })
          .execute(),
      ]);
    } catch (error) {
      logger.error(`删除分析组IP异常记录失败，错误信息：${error.message}`);
    }
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { defineOption, PageResult, selectBuilder } from 'src/utils/orm/builder';
import { isEmpty } from 'src/utils/types';
import { AnalyzePolicyFilterList } from 'src/modules/analyze/entities';

@Injectable()
export class AnalyzeGroupFilterListService {
  constructor(
    @InjectRepository(AnalyzePolicyFilterList)
    private readonly analyzeGroupFIlterListRepository: Repository<AnalyzePolicyFilterList>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any) {
    const builder = this.analyzeGroupFIlterListRepository.createQueryBuilder('a');
    const option = defineOption({
      select: ['a.*'],
      fieldEq: [
        { column: 'a.id', requestParam: 'id' },
        { column: 'a.action', requestParam: 'action' },
        { column: 'a.type', requestParam: 'type' },
      ],
      keyWordLikeFields: ['a.name', 'a.description'],
    });
    return selectBuilder.page(query, option, builder);
  }

  // 根据分析组ID获取过滤项目
  async findFilterListByAnalyzeGroupId({ page = 1, size = 15, analyzeGroupId }) {
    const mapping = await this.dataSource.query(
      `select * from op_ads_analyze_filter_list_mapping where analyzeId = ?`,
      [analyzeGroupId],
    );

    if (isEmpty(mapping)) return;

    const filterIds = mapping.map((i) => i.filterListId);
    const list = await this.analyzeGroupFIlterListRepository.find({
      where: { id: In(filterIds) },
    });
    return {
      list,
      pagination: {
        page,
        size,
        total: list.length,
      },
    } as PageResult;
  }

  // 移除关联
  async removeMapping({ analyzeGroupId, filterListId }) {
    try {
      await this.dataSource.query(
        `delete from op_ads_analyze_filter_list_mapping where analyzeId = ? and filterListId = ?`,
        [analyzeGroupId, filterListId],
      );

      return {
        code: 1000,
        message: 'Remove Success / 移除成功',
      };
    } catch (error) {
      return {
        code: 1001,
        message: `Remove Error - ${error.message || ''}`,
      };
    }
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AnalyzePolicyFilterList) {
    return this.analyzeGroupFIlterListRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: AnalyzePolicyFilterList) {
    await this.analyzeGroupFIlterListRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.analyzeGroupFIlterListRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.analyzeGroupFIlterListRepository.findOne({ where: { id } });
  }
}

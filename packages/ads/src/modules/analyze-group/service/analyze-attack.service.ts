import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>ron } from '@nestjs/schedule';

import { DataSource, In, Raw, Repository } from 'typeorm';
import { chain, flatten, groupBy, maxBy, omit } from 'lodash';

import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';
import { isEmpty, isExist } from '@/utils/types';
import { getBestTermItems } from '@/utils/filterTerms';
import { isIPv4AndNotEndsWithZero, toSubnetAddress } from '@/utils/ip';
import { AnalyzePolicy } from '@/modules/analyze/entities';
import { AbnormalHistory } from '@/modules/abnormal/entities/abnormal-history.entity';
import { AbnormalCurrent, AbnormalCurrentStatusEnum } from '@/modules/abnormal/entities';
import { AnalyzeService } from '@/modules/analyze/service/analyze.service';
import { ApplicableScopeEnum } from '@/modules/analyze/entities/analyze-policy.entity';
import { PullFlowFormatted } from '@/modules/pull/entities/pull_flow_formatted';
import { RedisService } from '@/base/redis/redis.service';
import { PullFlowHistory } from '@/modules/pull/entities/pull_flow_history';

import { IPHistory } from '../../flow/entities/ip-history.entity';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';

import { handleGroupByAny } from '../utils/group_by_any';

/**
 * 执行分组统计分析服务
 */

type AnalyzeGroupType = AnalyzePolicy & {
  [key: string]: any;
};

@Injectable()
export class AnalyzeAttackService {
  constructor(
    @InjectRepository(AnalyzeGroupResultLatest)
    private analyzeGroupResultLatest: Repository<AnalyzeGroupResultLatest>,
    private redisService: RedisService,
    private dataSource: DataSource,
    private analyzeService: AnalyzeService,
  ) {}

  @Cron('*/20 * * * * *')
  async execute() {
    try {
      await this.executeAnalyzeGroup();
    } catch (error) {
      logger.error(`执行外部分析组异常，错误信息：${error.message}`);
    }
  }

  /**
   * 执行分析策略组
   */
  async executeAnalyzeGroup() {
    // 获取分析组
    const analyzeGroupList: AnalyzeGroupType[] = await this.analyzeService.getAnalyzeDetail({
      isActive: true,
      isGroup: true,
      applicableScope: Raw((alias) => `JSON_CONTAINS(${alias}, :scope)`, {
        scope: `"${ApplicableScopeEnum.ATTACK_OVERVIEW}"`,
      }),
    });

    if (isEmpty(analyzeGroupList)) return 'Not isActive AnalyzeGroup';

    // 过滤符合时间段的 termItems
    this.filterBestTermItems(analyzeGroupList);

    // 检测并绑定策略分析上一次结果
    await this.bindPreviousResult(analyzeGroupList);

    // 绑定 currentAbnormals
    await this.bindCurrentAbnormal(analyzeGroupList);

    // 获取需要分析的攻击
    const flows = await this.getAttacks();

    if (isEmpty(flows)) return 'Not Flows';

    const analyzeResultsPromises = analyzeGroupList
      .filter((analyze) => isExist(analyze.terms))
      .map(async (analyze) => {
        const result = handleGroupByAny({
          source: flows,
          analyze,
        });

        if (isEmpty(result)) return;
        // 1. 更新分析结果
        const { abnormalHistoryList } = result;

        if (isExist(abnormalHistoryList)) {
          this.dataSource.transaction(async (runner) => {
            const promises = abnormalHistoryList.map(async (abnormal) => {
              const children = abnormal.children;
              const { raw } = await runner.insert(AbnormalHistory, omit(abnormal, [children]));

              if (isExist(children)) {
                await runner.insert(
                  AbnormalHistory,
                  children.map((i) => ({ ...i, parentId: raw.insertId })),
                );
              }
            });

            await Promise.all(promises);
          });
        }
      });

    const analyzeResults = await Promise.all(analyzeResultsPromises);
    return flatten(analyzeResults);
  }

  async bindCurrentAbnormal(analyzeGroupList) {
    const currentAbnormalAnalyzeGroup = analyzeGroupList.filter((analyze) => {
      return analyze.terms.some((term) => {
        return term.termItems.some((item) => {
          return item.classifyField === 'abnormalCount';
        });
      });
    });

    if (isExist(currentAbnormalAnalyzeGroup)) {
      const currentAbnormal = await this.dataSource
        .createQueryBuilder()
        .select('a.*')
        .from(AbnormalCurrent, 'a')
        .where('a.status = :status', {
          status: AbnormalCurrentStatusEnum.PENDING,
        })
        .getRawMany<AbnormalCurrent>();

      const lastCurrentAbnormal = currentAbnormal.filter((abnormal) => {
        return isIPv4AndNotEndsWithZero(abnormal.ipadd);
      });

      currentAbnormalAnalyzeGroup.forEach((analyze) => {
        analyze.currentAbnormal = lastCurrentAbnormal;
      });
    }
  }

  /**
   * 给传入的分析组绑定previousResult属性，previousResult这个组上一次分析的所有结果
   * @param analyzeGroupList
   */
  async bindPreviousResult(analyzeGroupList) {
    // 获取分析策略中存在 termItem.classifyMethod
    // increaseRate
    // decreaseRate
    // increaseNum
    // decreaseNum
    const existAddressCountAnalyzeIds = analyzeGroupList
      .filter((item) => {
        return item.terms.find((term) =>
          term.termItems.find((ti) =>
            ['increaseRate', 'decreaseRate', 'increaseNum', 'decreaseNum'].includes(ti.classifyMethod),
          ),
        );
      })
      .map((analyze) => analyze.id);

    // 存在需要使用上一次分析结果的条件
    if (isExist(existAddressCountAnalyzeIds)) {
      const analyzeResultLatests = await this.analyzeGroupResultLatest.find({
        where: { analyzeGroupId: In(existAddressCountAnalyzeIds) },
      });

      if (isExist(analyzeResultLatests)) {
        const analyzeResultGroupByGroupId = groupBy(analyzeResultLatests, 'analyzeGroupId');

        analyzeGroupList.forEach((analyze) => {
          const previousResult = analyzeResultGroupByGroupId[analyze.id + ''];
          if (isExist(previousResult)) {
            analyze.previousResult = previousResult;
          }
        });
      }
    }
  }

  /**
   * 过滤符合时间段的 termItem
   */
  filterBestTermItems(analyzeGroupList: AnalyzePolicy[]) {
    analyzeGroupList.forEach((analyze) => {
      analyze.terms?.forEach((term) => {
        term.termItems = getBestTermItems(term.termItems, 'classifyField');
      });
    });
  }

  mapToNfdumpItem(item: Partial<PullFlowHistory & PullFlowFormatted>) {
    return {
      id: item.id,
      analyzePolicyId: null,
      bestSubnetId: null,
      firstTime: item.firstTime,
      lastTime: item.lastTime,
      duration: '',
      ipadd: item.ip?.includes('/') ? item.ip.split('/')?.[0] : item.ip,
      subnet: toSubnetAddress(item.ip),
      bytes: 0,
      bps: +item.bps,
      bpp: 0,
      pps: +item.pps,
      customer: item.customer,
      serviceNo: item.serviceNo,
      location: item.location,
      source: item.source,
    } as Partial<IPHistory> & Record<string, any>;
  }

  async getAttacks() {
    let prevUpdateTime = await this.redisService.get('ads:analyze:attack_overview:prev_time');

    if (!prevUpdateTime) {
      prevUpdateTime = dayjs().subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss');
    }

    const formattedAttacks = await this.dataSource
      .createQueryBuilder()
      .select()
      .from(PullFlowFormatted, 'a')
      .where('a.updateTime > :time', { time: prevUpdateTime })
      .getRawMany<PullFlowFormatted>();

    if (isEmpty(formattedAttacks)) return;

    const currentMaxUpdateTime = maxBy(formattedAttacks, 'updateTime')?.updateTime;

    await this.redisService.set('ads:analyze:attack_overview:prev_time', currentMaxUpdateTime, 0);

    const maxBpsItemIds = chain(formattedAttacks)
      .filter((item) => isExist(item.maxBpsItemIds))
      .map((item) => {
        return chain(item.maxBpsItemIds || '')
          .split(',')
          .map((id) => +id.trim())
          .filter((id) => !isNaN(id))
          .uniq()
          .value();
      })
      .flatten()
      .value();

    const attacks = await this.dataSource
      .createQueryBuilder()
      .select()
      .from(PullFlowHistory, 'a')
      .where('a.id IN (:...ids)', { ids: maxBpsItemIds })
      .getRawMany<PullFlowHistory>();

    return attacks.map((attack) => {
      const formattedAttack = formattedAttacks.find((item) => {
        return item.ip === attack.ip;
      });

      return this.mapToNfdumpItem({
        ...attack,
        customer: formattedAttack?.customer,
        serviceNo: formattedAttack?.serviceNo,
        location: formattedAttack?.location,
      });
    });
  }
}

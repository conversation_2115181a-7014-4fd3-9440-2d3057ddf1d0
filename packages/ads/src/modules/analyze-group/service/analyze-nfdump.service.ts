import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Raw, Repository } from 'typeorm';
import { chain, flatten, forEach, groupBy, maxBy, omit } from 'lodash';
import { IPHistory } from '../../flow/entities/ip-history.entity';
import { dayjs } from 'src/utils/dayjs';
import { isEmpty, isExist } from 'src/utils/types';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { getBestTermItems } from 'src/utils/filterTerms';
import { AnalyzeGroupResultLatestService } from './analyze-group-result-latest.service';
import { AbnormalCurrent, AbnormalCurrentStatusEnum } from 'src/modules/abnormal/entities';
import { isIPv4AndNotEndsWithZero, toSubnetAddress } from 'src/utils/ip';
import { AnalyzePolicy } from 'src/modules/analyze/entities';
import { AbnormalHistory } from 'src/modules/abnormal/entities/abnormal-history.entity';
import { handleGroupByAny } from '../utils/group_by_any';
import { AnalyzeService } from 'src/modules/analyze/service/analyze.service';
import { ApplicableScopeEnum } from 'src/modules/analyze/entities/analyze-policy.entity';

/**
 * 执行分组统计分析服务
 */

type AnalyzeGroupType = AnalyzePolicy & {
  [key: string]: any;
};

@Injectable()
export class AnalyzeNFDumpService {
  constructor(
    @InjectRepository(AnalyzeGroupResultLatest)
    private readonly analyzeGroupResultLatest: Repository<AnalyzeGroupResultLatest>,

    private readonly dataSource: DataSource,
    private readonly analyzeService: AnalyzeService,
    private readonly analyzeGroupResultLatestService: AnalyzeGroupResultLatestService,
  ) {}

  /**
   * 执行分析策略组
   */
  async executeAnalyzeGroup() {
    // 获取分析组
    const analyzeGroupList: AnalyzeGroupType[] = await this.analyzeService.getAnalyzeDetail({
      isActive: true,
      isGroup: true,
      applicableScope: Raw((alias) => `(JSON_CONTAINS(${alias}, :scope) OR ${alias} IS NULL)`, {
        scope: `"${ApplicableScopeEnum.NFDUMP}"`,
      }),
    });

    if (isEmpty(analyzeGroupList)) return 'Not isActive AnalyzeGroup';

    // 过滤符合时间段的 termItems
    this.filterBestTermItems(analyzeGroupList);

    // 检测并绑定策略分析上一次结果
    await this.bindPreviousResult(analyzeGroupList);

    // 绑定 currentAbnormals
    await this.bindCurrentAbnormal(analyzeGroupList);

    // 获取最大的时间范围
    const { duration: maxDuration } = maxBy(analyzeGroupList, 'duration');

    const flows = await this.getFlowByDuration(maxDuration);
    if (isEmpty(flows)) return 'Not Flows';

    const analyzeResultsPromises = analyzeGroupList
      .filter((analyze) => isExist(analyze.terms))
      .map(async (analyze) => {
        // 过滤符合策略时间范围的流量数据
        const flowsFilterByDuration = flows.filter((flow) => {
          const duration = analyze.duration;
          return dayjs(flow.createTime).isAfter(dayjs().subtract(duration, 'seconds'));
        });

        if (isEmpty(flowsFilterByDuration)) return;

        const result = handleGroupByAny({
          source: flowsFilterByDuration,
          analyze,
        });

        if (isEmpty(result)) return;
        // 1. 更新分析结果
        const { results, abnormalHistoryList } = result;
        const endAnalyzeDateTime = dayjs().toDate();
        this.analyzeGroupResultLatestService
          .updateLatest(results.map((r) => omit(r, ['term'])))
          .then()
          .catch((error) => console.error(error));

        if (isExist(abnormalHistoryList)) {
          this.dataSource.transaction(async (runner) => {
            const promises = abnormalHistoryList.map(async (abnormal) => {
              const children = abnormal.children;
              const { raw } = await runner.insert(AbnormalHistory, omit(abnormal, [children]));

              if (isExist(children)) {
                await runner.insert(
                  AbnormalHistory,
                  children.map((i) => ({ ...i, parentId: raw.insertId })),
                );
              }
            });

            await Promise.all(promises);
          });
        }
      });

    const analyzeResults = await Promise.all(analyzeResultsPromises);

    return flatten(analyzeResults);
  }

  async bindCurrentAbnormal(analyzeGroupList) {
    const currentAbnormalAnalyzeGroup = analyzeGroupList.filter((analyze) => {
      return analyze.terms.some((term) => {
        return term.termItems.some((item) => {
          return item.classifyField === 'abnormalCount';
        });
      });
    });

    if (isExist(currentAbnormalAnalyzeGroup)) {
      const currentAbnormal = await this.dataSource
        .createQueryBuilder()
        .select('a.*')
        .from(AbnormalCurrent, 'a')
        .where('a.status = :status', {
          status: AbnormalCurrentStatusEnum.PENDING,
        })
        .getRawMany<AbnormalCurrent>();

      const lastCurrentAbnormal = currentAbnormal.filter((abnormal) => {
        return isIPv4AndNotEndsWithZero(abnormal.ipadd);
      });

      currentAbnormalAnalyzeGroup.forEach((analyze) => {
        analyze.currentAbnormal = lastCurrentAbnormal;
      });
    }
  }

  /**
   * 绑定最精确的subnet信息
   */
  async bindExactSubnetInfo(flows) {
    forEach(flows, (flow) => {
      flow.subnet = toSubnetAddress(flow.ipadd);
    });
  }

  /**
   * 给传入的分析组绑定previousResult属性，previousResult这个组上一次分析的所有结果
   * @param analyzeGroupList
   */
  async bindPreviousResult(analyzeGroupList) {
    // 获取分析策略中存在 termItem.classifyMethod
    // increaseRate
    // decreaseRate
    // increaseNum
    // decreaseNum
    const existAddressCountAnalyzeIds = analyzeGroupList
      .filter((item) => {
        return item.terms.find((term) =>
          term.termItems.find((ti) =>
            ['increaseRate', 'decreaseRate', 'increaseNum', 'decreaseNum'].includes(ti.classifyMethod),
          ),
        );
      })
      .map((analyze) => analyze.id);

    // 存在需要使用上一次分析结果的条件
    if (isExist(existAddressCountAnalyzeIds)) {
      const analyzeResultLatests = await this.analyzeGroupResultLatest.find({
        where: { analyzeGroupId: In(existAddressCountAnalyzeIds) },
      });

      if (isExist(analyzeResultLatests)) {
        const analyzeResultGroupByGroupId = groupBy(analyzeResultLatests, 'analyzeGroupId');

        analyzeGroupList.forEach((analyze) => {
          const previousResult = analyzeResultGroupByGroupId[analyze.id + ''];
          if (isExist(previousResult)) {
            analyze.previousResult = previousResult;
          }
        });
      }
    }
  }

  /**
   * 过滤符合时间段的 termItem
   */
  filterBestTermItems(analyzeGroupList: AnalyzePolicy[]) {
    analyzeGroupList.forEach((analyze) => {
      analyze.terms?.forEach((term) => {
        term.termItems = getBestTermItems(term.termItems, 'classifyField');
      });
    });
  }

  async getFlowByDuration(duration: number) {
    const now = dayjs().subtract(duration, 'seconds').toDate();

    const flows = await this.dataSource
      .createQueryBuilder()
      .select('id')
      .addSelect('bestSubnetId')
      .addSelect('firstTime')
      .addSelect('ipadd')
      .addSelect('bytes')
      .addSelect('pps')
      .addSelect('bpp')
      .addSelect('bps')
      .addSelect('customer')
      .addSelect('serviceNo')
      .addSelect('serviceNo2')
      .addSelect('productName')
      .addSelect('originAsn')
      .addSelect('location')
      .addSelect('upstream')
      .addSelect('srcDevice')
      .addSelect('createTime')
      .from(IPHistory, 'a')
      .where('createTime > :now', { now })
      .getRawMany();

    if (isEmpty(flows)) return;

    await this.bindExactSubnetInfo(flows);

    return flows;
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { selectBuilder, PageOption } from 'src/utils/orm/builder';
import { AnalyzeGroupResultHistory } from '../entities/analyze-group-result-history.entity';

@Injectable()
export class AnalyzeGroupResultHistoryService {
  constructor(
    @InjectRepository(AnalyzeGroupResultHistory)
    private readonly analyzeGroupResultHistoryRepository: Repository<AnalyzeGroupResultHistory>,
  ) {}

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    const { list, pagination } = await selectBuilder.page(
      query,
      option,
      this.analyzeGroupResultHistoryRepository.createQueryBuilder('a'),
    );

    return {
      list: list.map((item) => ({ ...item, isExistChildren: true })),
      pagination,
    };
  }

  async findChildren(id, { order, sort }) {
    const children = await this.analyzeGroupResultHistoryRepository
      .createQueryBuilder('a')
      .select('a.*')
      .where('a.parentId = :id', { id })
      .orderBy(order || 'bps', sort || 'DESC')
      .getRawMany();

    return children;
  }

  /**
   * 创建
   */
  async create(data) {
    return this.analyzeGroupResultHistoryRepository.save(data);
  }

  /**
   * 修改
   */
  async update(data: AnalyzeGroupResultHistory) {
    await this.analyzeGroupResultHistoryRepository.save(data);
  }

  /**
   * 删除
   */
  async remove(id: number | number[]) {
    return this.analyzeGroupResultHistoryRepository.delete(id);
  }

  /**
   * 详情
   */
  async findOne(id: number) {
    return this.analyzeGroupResultHistoryRepository.findOne({ where: { id } });
  }
}

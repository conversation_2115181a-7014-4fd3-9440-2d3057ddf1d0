import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { selectBuilder, PageOption } from 'src/utils/orm/builder';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { isExist } from 'src/utils/types';
import { AnalyzeGroupResultHistory } from '../entities/analyze-group-result-history.entity';
import { omit } from 'lodash';
import dayjs from 'src/utils/dayjs';

@Injectable()
export class AnalyzeGroupResultLatestService {
  constructor(
    @InjectRepository(AnalyzeGroupResultLatest)
    private readonly analyzeGroupResultLatestRepository: Repository<AnalyzeGroupResultLatest>,

    private readonly dataSource: DataSource,
  ) {}

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    return selectBuilder.page(query, option, this.analyzeGroupResultLatestRepository.createQueryBuilder('a'));
  }

  async updateLatest(latests: AnalyzeGroupResultLatest[]) {
    await this.dataSource.transaction(async (runner) => {
      const promises = latests.map(async (latest: any) => {
        const children = (latest as any).children;
        latest = omit(latest, ['children', 'serviceNo', 'serviceNo2']);

        const curLatest = await runner.findOne(AnalyzeGroupResultLatest, {
          where: { uuid: latest.uuid },
        });

        if (isExist(curLatest)) {
          latest.id = curLatest.id;
          await runner.update(AnalyzeGroupResultLatest, latest.id, latest);
        } else {
          const { raw } = await runner.insert(AnalyzeGroupResultLatest, latest);
          latest.id = raw.insertId;
        }

        if (latest.matchTermId) {
          const { raw } = await runner.insert(AnalyzeGroupResultHistory, {
            ...omit(latest, 'id'),
          });

          if (isExist(children)) {
            await Promise.all(
              (children as AnalyzeGroupResultHistory[]).map(async (item) => {
                item.parentId = raw.insertId;
                return runner.insert(AnalyzeGroupResultHistory, item);
              }),
            );
          }
        }
      });
      return Promise.all(promises);
    });
  }

  /**
   * 创建
   */
  async create(data) {
    return this.analyzeGroupResultLatestRepository.save(data);
  }

  /**
   * 修改
   */
  async update(data: AnalyzeGroupResultLatest) {
    await this.analyzeGroupResultLatestRepository.save(data);
  }

  /**
   * 删除
   */
  async remove(id: number | number[]) {
    return this.analyzeGroupResultLatestRepository.delete(id);
  }

  /**
   * 详情
   */
  async findOne(id: number) {
    return this.analyzeGroupResultLatestRepository.findOne({ where: { id } });
  }
}

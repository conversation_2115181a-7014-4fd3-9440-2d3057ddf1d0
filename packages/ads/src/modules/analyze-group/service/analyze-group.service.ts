import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { selectBuilder, PageOption } from 'src/utils/orm/builder';
import { chain, isArray } from 'lodash';
import { getDifferences } from 'src/utils/differences';
import { isExist, isEmpty } from 'src/utils/types';
import { AnalyzePolicy, AnalyzeTerm, AnalyzeTermItem, AnalyzePolicyFilterList } from 'src/modules/analyze/entities';
import { ActionCommonService } from 'src/common/service/action.service';
import { AnalyzeGroupResultLatest } from '../entities/analyze-group-result-latest.entity';
import { formatValues } from 'src/modules/analyze/utils/filter';
import { AnalyzeGroupResultHistory } from '../entities/analyze-group-result-history.entity';
import { AnalyzeService } from 'src/modules/analyze/service/analyze.service';

@Injectable()
export class AnalyzeGroupService {
  constructor(
    @InjectRepository(AnalyzePolicy)
    private readonly analyzePolicyGroupRepository: Repository<AnalyzePolicy>,

    @InjectRepository(AnalyzeTerm)
    private readonly analyzeTermGroup: Repository<AnalyzeTerm>,

    @InjectRepository(AnalyzeTermItem)
    private readonly analyzeTermItemGroup: Repository<AnalyzeTermItem>,

    @InjectRepository(AnalyzePolicyFilterList)
    private readonly analyzeGroupFilterRepository: Repository<AnalyzePolicyFilterList>,

    private dataSource: DataSource,
    private actionCommonService: ActionCommonService,
    private analyzeService: AnalyzeService,
  ) {}

  // 手动执行组结果操作
  async manualExecute(id: number, actionId: number, targetType: 'current' | 'history') {
    const entityClass = targetType === 'current' ? AnalyzeGroupResultLatest : AnalyzeGroupResultHistory;

    const target = await this.dataSource
      .createQueryBuilder()
      .select()
      .from(entityClass, 'a')
      .where('a.id = :id', { id })
      .getRawOne();

    if (isEmpty(target)) {
      return { code: 1001, message: 'Not Data By ID' };
    }

    const abnormalList = await this.actionCommonService.executeAbnormalList([
      {
        id: target.id,
        actionId,
        ip: target.ipadd ? target.ipadd : target.subnet,
        values: formatValues(target),
      },
    ]);

    return {
      code: abnormalList.every(({ result }) => result.every((i) => i.isOk)) ? 1000 : 1001,
      message: abnormalList.map(({ result }) => result?.map((i) => i.prefix).join('\n')).join('\n'),
    };
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    const builder = this.analyzePolicyGroupRepository.createQueryBuilder('a');

    return selectBuilder.page<any>(query, option, builder);
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AnalyzePolicy & { filterListIds: number[] }) {
    if (isArray(data.filterListIds) && isExist(data.filterListIds)) {
      const filterList = await this.analyzeGroupFilterRepository.find({
        where: { id: In(data.filterListIds) },
      });
      data.filterList = filterList;
    } else {
      data.filterList = [];
    }

    const result = await this.analyzePolicyGroupRepository.save(data);
    return this.handleTerms(data.terms, result.id);
  }

  /**
   * 更新
   */
  async update(id: number, data: AnalyzePolicy & { filterListIds: number[] }) {
    const { terms, ...analyzeGroup } = data;
    if (isArray(data.filterListIds) && isExist(data.filterListIds)) {
      const filterList = await this.analyzeGroupFilterRepository.find({
        where: { id: In(analyzeGroup.filterListIds) },
      });
      analyzeGroup.filterList = filterList;
    } else if (isArray(data.filterListIds) && data.filterListIds.length === 0) {
      analyzeGroup.filterList = [];
    }

    await this.analyzePolicyGroupRepository.save(analyzeGroup);
    if (terms) this.handleTerms(terms, id);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    id = Array.isArray(id) ? id : [id];

    const connection = this.analyzePolicyGroupRepository.manager.connection;
    const runner = connection.createQueryRunner();
    await runner.startTransaction();

    try {
      await runner.manager.delete(AnalyzeTerm, {
        analyzePolicyId: In(id),
      });
      await runner.manager.delete(AnalyzePolicy, id);
      await runner.commitTransaction();
    } catch (error) {
      await runner.rollbackTransaction();
      console.log(error.message);
      throw new Error(error.message);
    } finally {
      await runner.release();
    }
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    const [analyze] = await this.analyzeService.getAnalyzeDetail({ id });
    return analyze;
  }

  /**
   * 处理 terms 和 items
   */
  async handleTerms(terms: Array<any>, analyzePolicyId: number) {
    const termIds = chain(terms).uniqBy('id').value();

    // 删除旧的并且不存在的 Term
    const oldTerms = await this.analyzeTermGroup.find({
      where: { analyzePolicyId },
    });
    const { dKeys } = getDifferences(termIds, oldTerms);
    if (dKeys.length > 0) await this.analyzeTermGroup.delete(dKeys);

    // 保存 term
    const result = await this.analyzeTermGroup.save(
      terms.map((term) => {
        const data = {
          ...term,
          analyzePolicyId,
        };
        delete data.termItems;
        return data;
      }),
    );

    // 当前策略所有 items
    const items = terms.flatMap((term, index) => {
      return term.termItems.map((item) => {
        return {
          ...item,
          termId: result[index].id,
        };
      });
    });

    // 当前策略的所有旧的 item 和 children
    const oldItems = await this.analyzeTermItemGroup.find({
      where: { termId: In(result.map((term) => term.id)) },
    });

    // 删除旧的并且不存在的 item
    const itemIds = chain(items).uniqBy('id').value();
    const { dKeys: dItemIds } = getDifferences(
      itemIds,
      oldItems.filter((item) => !item.parentId),
    );
    if (dItemIds.length > 0) await this.analyzeTermItemGroup.delete(dItemIds);

    // 保存 items
    const itemsResult = await this.analyzeTermItemGroup.save(items);

    // 当前策略中 item 的 children
    const childrenList = items.flatMap((item, index) => {
      return item.children.map((children) => {
        return {
          ...children,
          parentId: itemsResult[index].id,
          termId: itemsResult[index].termId,
        };
      });
    });

    // 删除旧的并且不存在的 children
    const childrenIds = chain(childrenList).uniqBy('id').value();
    const { dKeys: dChildrenIds } = getDifferences(
      childrenIds,
      oldItems.filter((item) => !!item.parentId),
    );
    if (dChildrenIds.length > 0) await this.analyzeTermItemGroup.delete(dChildrenIds);

    // 保存 children
    return await this.analyzeTermItemGroup.save(childrenList);
  }
}

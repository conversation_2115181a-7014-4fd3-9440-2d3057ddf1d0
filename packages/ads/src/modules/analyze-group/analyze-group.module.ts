import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyzeGroupController } from './controller/analyze-group.controller';
import { AnalyzeGroupResultLatestController } from './controller/analyze-group-result-latest.controller';
import { AnalyzeGroupResultHistoryController } from './controller/analyze-group-result-history.controller';
import { AnalyzeGroupFilterListController } from './controller/analyze-group-filter-list.controller';
import { AnalyzeGroupService } from './service/analyze-group.service';
import { AnalyzeGroupResultLatest } from './entities/analyze-group-result-latest.entity';
import { AnalyzeGroupResultHistory } from './entities/analyze-group-result-history.entity';
import { AnalyzeGroupResultLatestService } from './service/analyze-group-result-latest.service';
import { AnalyzeGroupResultHistoryService } from './service/analyze-group-result-history.service';
import { AnalyzeGroupFilterListService } from './service/analyze-group-filter-list.service';
import { AnalyzeGroupScheduleService } from './schedule/analyze-group.schedule';
import { CommonModule } from 'src/common/common.module';
import { AnalyzeNFDumpService } from './service/analyze-nfdump.service';
import {
  AnalyzePolicy,
  AnalyzeTerm,
  AnalyzeTermItem,
  AnalyzePolicyFilterList,
  AnalyzeLog,
} from 'src/modules/analyze/entities';
import { AnalyzeLogService } from '../analyze/service/analyze-log.service';
import { AnalyzeModule } from '../analyze/analyze.module';
import { AnalyzeAttackService } from './service/analyze-attack.service';
import { RedisModule } from 'src/base';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AnalyzePolicy,
      AnalyzeTerm,
      AnalyzeTermItem,
      AnalyzePolicyFilterList,
      AnalyzeGroupResultLatest,
      AnalyzeGroupResultHistory,
      AnalyzeLog,
    ]),
    CommonModule,
    AnalyzeModule,
    RedisModule,
  ],
  controllers: [
    AnalyzeGroupController,
    AnalyzeGroupResultLatestController,
    AnalyzeGroupResultHistoryController,
    AnalyzeGroupFilterListController,
  ],
  providers: [
    AnalyzeGroupService,
    AnalyzeGroupResultLatestService,
    AnalyzeGroupResultHistoryService,
    AnalyzeGroupScheduleService,
    AnalyzeGroupFilterListService,
    AnalyzeNFDumpService,
    AnalyzeAttackService,
    AnalyzeLogService,
  ],
})
export class AnalyzeGroupModule {}

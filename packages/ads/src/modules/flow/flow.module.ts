import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IPHistoryService } from './service/ip-history.service';
import { IPFlowHistoryController } from './controller/ip-history';
import { SubnetHistoryController } from './controller/subnet-history';
import { IPHistory } from './entities/ip-history.entity';
import { SubnetHistory } from './entities/subnet-history.entity';
import { SubnetHistoryService } from './service/subnet-history.service';
import { IPHistoryScheduleService } from './schedule/ip.schedule';
import { SubnetHistoryScheduleService } from './schedule/subnet.schedule';
import { IpConfigModule } from '../ip-config/ip-config.module';
import { RedisModule } from 'src/base';
import { AnalyzeModule } from '../analyze/analyze.module';
import { CommonModule } from 'src/common/common.module';
import { IPConfig } from '../ip-config/entities/ip-config.entity';
import { AnalyzePolicy } from '../analyze/entities/analyze-policy.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([IPHistory, SubnetHistory, IPConfig, AnalyzePolicy]),
    forwardRef(() => AnalyzeModule),
    RedisModule,
    CommonModule,
    IpConfigModule,
  ],
  controllers: [IPFlowHistoryController, SubnetHistoryController],
  providers: [IPHistoryScheduleService, SubnetHistoryScheduleService, IPHistoryService, SubnetHistoryService],
  exports: [IPHistoryService, SubnetHistoryService],
})
export class FlowModule {}

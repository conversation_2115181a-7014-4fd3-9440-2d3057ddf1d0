import { Entity, PrimaryGeneratedColumn, PrimaryColumn, Column, Index } from 'typeorm';

@Entity('op_ads_ip_flow_history')
export class IPHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '当前最精确绑定的分析策略ID', nullable: true })
  analyzePolicyId: number;

  @Column({ comment: '当前最精确绑定的配置ID', nullable: true })
  bestSubnetId: number;

  @Index()
  @Column({ comment: 'First Time', type: 'datetime', nullable: true })
  firstTime: string;

  @Column({ comment: 'Last Time', type: 'datetime', nullable: true })
  lastTime: string;

  @Column({ comment: 'Duration', type: 'int', nullable: true })
  duration: string;

  @Column({ comment: 'NFD File Path', default: '' })
  nfdFilePath: string;

  @Column({
    comment: 'Protocol',
    type: 'decimal',
    default: 0,
    precision: 10,
    scale: 2,
    nullable: true,
  })
  protocol: number;

  @Index()
  @Column({ comment: 'IP Address', type: 'varchar', nullable: true })
  ipadd: string;

  @Index()
  @Column({ comment: 'Severity', default: '' })
  severity: string;

  @Column({ comment: '匹配Term的字段', default: '' })
  matchTermFields: string;

  @Column({ comment: 'Flows', type: 'int', nullable: true })
  flows: number;

  @Column({
    comment: 'Packets',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  packets: number;

  @Column({
    comment: 'Bytes',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bytes: number;

  @Column({
    comment: 'PPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  pps: number;

  @Column({
    comment: 'BPP',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bpp: number;

  @Column({
    comment: 'BPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  bps: number;

  @Column({ comment: 'Source Address', type: 'varchar', nullable: true })
  srcAddr: string;

  @Column({ comment: 'Source Port', type: 'int', nullable: true })
  srcPort: number;

  @Column({ comment: 'Destination Address', type: 'varchar', nullable: true })
  dstAddr: string;

  @Column({ comment: 'Destination Port', type: 'int', nullable: true })
  dstPort: number;

  @Column({ comment: 'TCP Flags', type: 'varchar', nullable: true })
  tcpFlags: string;

  @Column({ comment: 'Input Interface Index', type: 'varchar', nullable: true })
  inputIfIndex: string;

  @Column({
    comment: 'Output Interface Index',
    type: 'varchar',
    nullable: true,
  })
  outputIfIndex: string;

  @Column({ comment: 'Source AS', type: 'varchar', nullable: true })
  srcAs: string;

  @Column({ comment: 'Destination AS', type: 'varchar', nullable: true })
  dstAs: string;

  @Column({ comment: 'Forward Status', type: 'varchar', nullable: true })
  fwdStatus: string;

  // srcDevice
  @Index()
  @Column({
    comment: 'Source Device',
    type: 'varchar',
    default: '',
    nullable: true,
  })
  srcDevice: string;

  // upstream
  @Index()
  @Column({ comment: '上游设备', default: '' })
  upstream: string;

  @Index()
  @Column({
    comment: 'Source Location',
    type: 'varchar',
    default: '',
    nullable: true,
  })
  srcLocation: string;

  @Index()
  @Column({ comment: '客户', default: '', nullable: true })
  customer: string;

  @Index()
  @Column({ comment: 'ASN', default: '', nullable: true })
  originAsn: string;

  @Index()
  @Column({ comment: '产品名称', default: '', nullable: true })
  productName: string;

  @Index()
  @Column({ comment: '机房', default: '', nullable: true })
  location: string;

  @Index()
  @Column({ comment: '服务编号1', default: '', nullable: true })
  serviceNo: string;

  @Index()
  @Column({ comment: '服务编号2', default: '', nullable: true })
  serviceNo2: string;

  @Column({ comment: '描述', default: '' })
  description: string;

  @Index()
  @PrimaryColumn({ comment: '创建时间', type: 'datetime' })
  createTime: Date;
}

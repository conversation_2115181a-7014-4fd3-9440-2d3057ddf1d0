import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, PrimaryColumn, Index } from 'typeorm';

@Entity('op_ads_subnet_flow_history')
export class SubnetHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ comment: 'First Time', type: 'datetime', nullable: true })
  firstTime: string;

  @Column({ comment: 'Last Time', type: 'datetime', nullable: true })
  lastTime: string;

  @Column({ comment: 'Duration', type: 'int', nullable: true })
  duration: string;

  @Index()
  @Column({ comment: 'Severity', default: '' })
  severity: string;

  @Column({ comment: '匹配Term的字段', default: '' })
  matchTermFields: string;

  @Column({ comment: '当前最精确绑定的分析策略ID', nullable: true })
  analyzePolicyId: number;

  @Column({ comment: '当前最精确绑定的配置ID', nullable: true })
  bestSubnetId: number;

  @Column({ comment: '推送服务器IP', default: '' })
  nfdPushIp: string;

  @Column({ comment: 'NFD File Path', default: '' })
  nfdFilePath: string;

  @Column({ comment: 'Af', default: '' })
  af: string;

  // ip
  @Index()
  @Column({ comment: 'IP Address', type: 'varchar', nullable: true })
  ip: string;

  // totalFlows
  @Column({ comment: 'Total Flows', type: 'int', nullable: true })
  totalFlows: number;

  // totalPackets
  @Column({
    comment: 'Total Packets',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  totalPackets: number;

  // totalBytes
  @Column({
    comment: 'Total Bytes',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  totalBytes: number;

  @Column({
    comment: 'Average BPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  avgBps: number;

  @Column({
    comment: 'Average BPP',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  avgBpp: number;

  @Column({
    comment: 'Average PPS',
    type: 'decimal',
    default: 0,
    precision: 20,
    scale: 2,
    nullable: true,
  })
  avgPps: number;

  // srcDevice
  @Index()
  @Column({
    comment: 'Source Device',
    type: 'varchar',
    default: '',
    nullable: true,
  })
  srcDevice: string;

  @Index()
  @Column({ comment: '上游设备', default: '' })
  upstream: string;

  @Index()
  @Column({
    comment: 'Source Location',
    type: 'varchar',
    default: '',
    nullable: true,
  })
  srcLocation: string;

  // fwdStatus
  @Column({ comment: 'Forward Status', type: 'varchar', nullable: true })
  fwdStatus: string;

  @Index()
  @Column({ comment: '客户', default: '', nullable: true })
  customer: string;

  @Index()
  @Column({ comment: 'ASN', default: '', nullable: true })
  originAsn: string;

  @Index()
  @Column({ comment: '产品名称', default: '', nullable: true })
  productName: string;

  @Index()
  @Column({ comment: '机房', default: '', nullable: true })
  location: string;

  @Index()
  @Column({ comment: '服务编号1', default: '', nullable: true })
  serviceNo: string;

  @Index()
  @Column({ comment: '服务编号2', default: '', nullable: true })
  serviceNo2: string;

  @Column({ comment: '描述', default: '' })
  description: string;

  @Index()
  @PrimaryColumn({ comment: '创建时间', type: 'datetime' })
  createTime: Date;
}

import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { Brackets, SelectQueryBuilder } from 'typeorm';

import { isArray } from 'lodash';
import { IPHistoryService } from '../service/ip-history.service';
import { defineOption, PageResult } from 'src/utils/orm/builder';
import { IPHistory } from '../entities/ip-history.entity';
import { isEmpty } from 'src/utils/types';
import { dayjs } from 'src/utils/dayjs';
@Controller('flow/ip/history')
export class IPFlowHistoryController {
  private pageQueryOp(query) {
    return defineOption({
      select: [
        'a.id id',
        'a.firstTime firstTime',
        'a.lastTime lastTime',
        'a.duration duration',
        'a.ipadd ipadd',
        'a.bytes bytes',
        'a.packets packets',
        'a.pps pps',
        'a.bpp bpp',
        'a.bps bps',
        'a.customer customer',
        'a.serviceNo serviceNo',
        'a.serviceNo2 serviceNo2',
        'a.location location',
        'a.productName productName',
        'a.originAsn originAsn',
        'a.srcDevice srcDevice',
        'a.srcLocation srcLocation',
        'a.upstream upstream',
        'a.srcAddr srcAddr',
        'a.dstAddr dstAddr',
        'a.severity severity',
        'a.matchTermFields matchTermFields',
        'a.createTime createTime',
        'a.analyzePolicyId analyzePolicyId',
        'a.bestSubnetId bestSubnetId',
      ],
      fieldEq: [
        { column: 'a.srcDevice', requestParam: 'srcDevice' },
        { column: 'a.srcLocation', requestParam: 'srcLocation' },
        { column: 'a.upstream', requestParam: 'upstream' },
        { column: 'a.ipadd', requestParam: 'ipadd' },
        { column: 'a.serviceNo', requestParam: 'serviceNo' },
        { column: 'a.serviceNo2', requestParam: 'serviceNo2' },
        { column: 'a.customer', requestParam: 'customerNo' },
        { column: 'a.productName', requestParam: 'productName' },
        { column: 'a.location', requestParam: 'location' },
      ],
      // fieldLike: [{ column: 'a.ipadd', requestParam: 'ipaddLike' }],
      extend: (builder: SelectQueryBuilder<IPHistory>) => {
        const { createTime, firstTime, order, sort, ipaddLike, originAsnLike, customerLike } = query;

        if (isArray(createTime) && createTime.length === 2) {
          builder.andWhere('a.createTime > :start AND a.createTime <= :end', {
            start: createTime[0],
            end: createTime[1],
          });
        }

        if (isArray(firstTime) && firstTime.length === 2) {
          builder.andWhere('a.firstTime > :start AND a.firstTime <= :end', {
            start: firstTime[0],
            end: firstTime[1],
          });
        }

        if (ipaddLike) {
          builder.andWhere('a.ipadd LIKE :ipaddLike', {
            ipaddLike: `${ipaddLike}%`,
          });
        }

        if (originAsnLike) {
          builder.andWhere('a.originAsn LIKE :originAsnLike', {
            originAsnLike: `${originAsnLike}%`,
          });
        }

        if (!order && !sort) {
          builder.orderBy('a.createTime', 'DESC');
        }

        if (customerLike) {
          builder.andWhere('a.customer LIKE :customerLike', {
            customerLike: `${customerLike}%`,
          });
        }
      },
    });
  }

  constructor(private readonly ipHistoryService: IPHistoryService) {}

  @Get('srcDevices')
  async getSrcDevices() {
    return this.ipHistoryService.getSrcDevices();
  }

  @Get('upstream')
  async getUpstream() {
    return this.ipHistoryService.getUpstream();
  }

  @Get('srcLocation')
  async getSrcLocation() {
    return this.ipHistoryService.getSrcLocation();
  }

  @Post('manual/execute/action')
  async manualExecuteAction(@Body('id') id: number, @Body('actionId') actionId: number) {
    if (!id || !actionId) {
      return { code: 1001, message: 'Request ID | ActionID' };
    }

    return this.ipHistoryService.manualExecute(id, actionId);
  }

  @Post('top')
  async getTop(@Body() body) {
    const { page, size } = body;
    let firstTime = body.firstTime;

    if (isEmpty(firstTime) || !isArray(firstTime)) {
      firstTime = [dayjs().subtract(3, 'minutes').toDate(), dayjs().toDate()];
    }

    body.start = firstTime[0];
    body.end = firstTime[1];

    const list = await this.ipHistoryService.getTop(body);

    return {
      list,
      pagination: {
        page,
        size,
        total: (page - 1) * size + 2 * size,
      },
    } as PageResult;
  }

  @Post('page')
  async find(@Body() query: any) {
    return this.ipHistoryService.find(query, this.pageQueryOp(query));
  }

  @Post('add')
  async create(@Body() data: any) {
    return this.ipHistoryService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.ipHistoryService.update(data.id, data);
  }

  @Post('delete')
  async remove(@Body('ids') id: number | number[]) {
    return this.ipHistoryService.remove(id);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.ipHistoryService.findOne(id);
  }
}

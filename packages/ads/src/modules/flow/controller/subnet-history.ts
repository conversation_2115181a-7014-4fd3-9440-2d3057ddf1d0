import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { Brackets, SelectQueryBuilder } from 'typeorm';

import { isArray } from 'lodash';
import { SubnetHistoryService } from '../service/subnet-history.service';
import { defineOption, PageResult } from 'src/utils/orm/builder';
import { SubnetHistory } from '../entities/subnet-history.entity';
import { isEmpty } from 'src/utils/types';
import { dayjs } from 'src/utils/dayjs';

@Controller('flow/subnet/history')
export class SubnetHistoryController {
  private pageQueryOp(query) {
    return defineOption({
      select: ['a.*'],
      fieldEq: [
        { column: 'a.ip', requestParam: 'ip' },
        { column: 'a.srcLocation', requestParam: 'srcLocation' },
        { column: 'a.srcDevice', requestParam: 'srcDevice' },
        { column: 'a.upstream', requestParam: 'upstream' },
        { column: 'a.ip', requestParam: 'ip' },
        { column: 'a.serviceNo', requestParam: 'serviceNo' },
        { column: 'a.serviceNo2', requestParam: 'serviceNo2' },
        { column: 'a.customer', requestParam: 'customerNo' },
        { column: 'a.productName', requestParam: 'productName' },
        { column: 'a.location', requestParam: 'location' },
      ],
      // fieldLike: [{ column: 'a.ip', requestParam: 'ipLike' }],
      extend: (builder: SelectQueryBuilder<SubnetHistory>) => {
        const { firstTime, ipLike, order, sort, originAsnLike, customerLike } = query;
        if (isArray(firstTime) && firstTime.length === 2) {
          builder.andWhere('a.firstTime > :start AND a.firstTime <= :end', {
            start: firstTime[0],
            end: firstTime[1],
          });
        }

        if (ipLike) {
          builder.andWhere('a.ip LIKE :ipLike', { ipLike: `${ipLike}%` });
        }

        if (originAsnLike) {
          builder.andWhere('a.originAsn LIKE :originAsnLike', {
            originAsnLike: `${originAsnLike}%`,
          });
        }

        if (!order && !sort) {
          builder.orderBy('a.createTime', 'DESC');
        }

        if (customerLike) {
          builder.andWhere('a.customer LIKE :customerLike', {
            customerLike: `${customerLike}%`,
          });
        }

        // if (isEmpty(order) && isEmpty(sort)) {
        //   builder.addOrderBy('a.firstTime', 'DESC');
        //   // .orderBy(
        //   //   "CASE WHEN a.severity IS NULL OR a.severity = '' THEN 1 ELSE 0 END",
        //   //   'ASC',
        //   // )
        //   // .addOrderBy('a.severity', 'ASC')
        //   // .addOrderBy('a.firstTime', 'DESC');
        // }
      },
    });
  }

  constructor(private readonly subnetHistoryService: SubnetHistoryService) {}

  @Post('manual/execute/action')
  async manualExecuteAction(@Body('id') id: number, @Body('actionId') actionId: number) {
    if (!id || !actionId) {
      return { code: 1001, message: 'Request ID | ActionID' };
    }

    return this.subnetHistoryService.manualExecute(id, actionId);
  }

  @Get('srcDevices')
  async getSrcDevices() {
    return this.subnetHistoryService.getSrcDevices();
  }

  @Get('upstream')
  async getUpstream() {
    return this.subnetHistoryService.getUpstream();
  }

  @Get('srcLocation')
  async getSrcLocation() {
    return this.subnetHistoryService.getSrcLocation();
  }

  @Post('top')
  async getTop(@Body() body) {
    const { page, size } = body;
    let firstTime = body.firstTime;

    if (isEmpty(firstTime) || !isArray(firstTime)) {
      firstTime = [dayjs().subtract(3, 'minutes').toDate(), dayjs().toDate()];
    }

    body.start = firstTime[0];
    body.end = firstTime[1];

    const list = await this.subnetHistoryService.getTop(body);

    return {
      list,
      pagination: {
        page,
        size,
        total: (page - 1) * size + 2 * size,
      },
    } as PageResult;
  }

  @Post('page')
  async find(@Body() query: any) {
    return this.subnetHistoryService.find(query, this.pageQueryOp(query));
  }

  @Post('add')
  async create(@Body() data: any) {
    return this.subnetHistoryService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.subnetHistoryService.update(data.id, data);
  }

  @Post('delete')
  async remove(@Body('ids') id: number | number[]) {
    return this.subnetHistoryService.remove(id);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.subnetHistoryService.findOne(id);
  }
}

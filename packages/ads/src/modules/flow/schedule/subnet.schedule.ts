import { Injectable, OnModuleInit } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { DataSource } from 'typeorm';

import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';

@Injectable()
export class SubnetHistoryScheduleService implements OnModuleInit {
  TABLE_NAME = 'op_ads_subnet_flow_history';
  TABLE_SCHEMA = process.env.ADS_DB_DATABASE;
  PARTITION_FIELD = 'createTime';

  constructor(private readonly dataSource: DataSource) {}

  // 项目初始化函数：检测表是否存在分区，如果不存在则创建一个当天分区
  async onModuleInit() {
    try {
      const result = await this.dataSource.query(
        `SELECT TABLE_SCHEMA, TABLE_NAME, PARTITION_NAME FROM information_schema.PARTITIONS WHERE TABLE_SCHEMA = '${this.TABLE_SCHEMA}' AND TABLE_NAME = '${this.TABLE_NAME}';`,
      );

      // 是否存在分区
      const isExistPartition = result.some((item) => {
        return item.PARTITION_NAME !== null;
      });

      // 当天分区
      const currentDayPartition = `p${dayjs().format('YYYYMMDD')}`;
      const isExistCurrentDayPartition = result.some((item) => {
        return item.PARTITION_NAME === currentDayPartition;
      });

      // 明天分区
      const nextDayPartition = `p${dayjs().add(1, 'days').format('YYYYMMDD')}`;
      const isExistNextDayPartition = result.some((item) => {
        return item.PARTITION_NAME === nextDayPartition;
      });

      // 新增初始化分区
      if (!isExistPartition) {
        logger.info(`表 ${this.TABLE_NAME} 不存在分区，创建初始分区`);
        await this.dataSource.query(`
          ALTER TABLE ${this.TABLE_NAME}
          PARTITION BY RANGE (TO_DAYS(${this.PARTITION_FIELD})) (
            ${this.generatePartitionSQL()}
          );
        `);
        logger.info(`成功创建初始分区: ${currentDayPartition}`);
      } else if (!isExistCurrentDayPartition) {
        logger.info(`缺少当天分区，添加分区: ${currentDayPartition}`);
        await this.dataSource.query(`ALTER TABLE ${this.TABLE_NAME} ADD PARTITION (${this.generatePartitionSQL()});`);
        logger.info(`成功添加当天分区: ${currentDayPartition}`);
      }

      // 检查并添加明天分区（如果不存在）
      if (!isExistNextDayPartition) {
        logger.info(`缺少明天分区，添加分区: ${nextDayPartition}`);
        await this.dataSource.query(`ALTER TABLE ${this.TABLE_NAME} ADD PARTITION (${this.generatePartitionSQL(1)});`);
        logger.info(`成功添加明天分区: ${nextDayPartition}`);
      }

      logger.info(`Subnet历史记录分区初始化完成`);
    } catch (error) {
      logger.error(`Subnet历史记录分区初始化失败: ${error.message}`);
      throw error; // 重新抛出错误，确保初始化失败时应用不会启动
    }
  }

  // 每天23:00执行一次新增分区
  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async addNextDayPartition() {
    try {
      // 检查明天分区是否已存在
      const nextDayPartition = `p${dayjs().add(1, 'days').format('YYYYMMDD')}`;
      const result = await this.dataSource.query(
        `SELECT PARTITION_NAME FROM information_schema.PARTITIONS
         WHERE TABLE_SCHEMA = '${this.TABLE_SCHEMA}'
         AND TABLE_NAME = '${this.TABLE_NAME}'
         AND PARTITION_NAME = '${nextDayPartition}';`,
      );

      if (result.length > 0) {
        logger.info(`明天分区 ${nextDayPartition} 已存在，跳过添加操作`);
        return;
      }

      // 添加明天分区
      await this.dataSource.query(`ALTER TABLE ${this.TABLE_NAME} ADD PARTITION (${this.generatePartitionSQL(1)});`);
      logger.info(`成功添加明天分区: ${nextDayPartition}`);
    } catch (error) {
      logger.error(`新增NFD Subnet历史记录分区失败，错误信息：${error.message}`);
    }
  }

  // 每天00:00执行一次删除分歧
  // 7天前的分支
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async removePartition() {
    const sevenDaysAgo = dayjs().subtract(7, 'days');
    const sevenDaysAgoStr = sevenDaysAgo.format('YYYYMMDD');

    try {
      // 查找所有7天前的分区
      const result = await this.dataSource.query(
        `SELECT PARTITION_NAME FROM information_schema.PARTITIONS
         WHERE TABLE_SCHEMA = '${this.TABLE_SCHEMA}'
         AND TABLE_NAME = '${this.TABLE_NAME}'
         AND PARTITION_NAME IS NOT NULL
         AND PARTITION_NAME < 'p${sevenDaysAgoStr}';`,
      );

      if (result.length === 0) {
        logger.info(`没有找到7天前的分区需要删除`);
        return;
      }

      // 删除所有7天前的分区
      const partitionsToDelete = result.map((item) => item.PARTITION_NAME);
      logger.info(`准备删除以下分区: ${partitionsToDelete.join(', ')}`);

      for (const partitionName of partitionsToDelete) {
        try {
          const REMOVE_PARTITION_SQL = `ALTER TABLE ${this.TABLE_NAME} DROP PARTITION ${partitionName};`;
          await this.dataSource.query(REMOVE_PARTITION_SQL);
          logger.info(`成功删除分区: ${partitionName}`);
        } catch (partitionError) {
          logger.error(`删除分区 ${partitionName} 失败: ${partitionError.message}`);
          // 继续删除其他分区，不因为单个分区失败而停止
        }
      }

      logger.info(`分区清理完成，共删除 ${partitionsToDelete.length} 个分区`);
    } catch (error) {
      logger.error(`查询或删除分区时发生错误: ${error.message}`);
    }
  }

  generatePartitionSQL(value = 0): string {
    const now = dayjs().add(value, 'days');
    const partitionName = `p${now.format('YYYYMMDD')}`;
    const nextDay = now.add(1, 'days').format('YYYY-MM-DD');

    const sql = `
        PARTITION ${partitionName} VALUES LESS THAN (TO_DAYS('${nextDay}'))
    `;

    return sql.trim();
  }
}

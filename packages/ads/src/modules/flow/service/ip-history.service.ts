import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { isArray, uniq } from 'lodash';
import { IPHistory } from '../entities/ip-history.entity';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import { isEmpty, isExist } from 'src/utils/types';
import { AnalyzePolicy } from 'src/modules/analyze/entities/analyze-policy.entity';
import { IPConfig } from 'src/modules/ip-config/entities/ip-config.entity';
import { ActionCommonService } from 'src/common/service/action.service';
import { formatValues } from 'src/modules/analyze/utils/filter';

@Injectable()
export class IPHistoryService {
  constructor(
    @InjectRepository(IPHistory)
    private readonly ipHistoryRepository: Repository<IPHistory>,

    @InjectRepository(AnalyzePolicy)
    private readonly analyzePolicyRepository: Repository<AnalyzePolicy>,

    private dataSource: DataSource,
    private actionCommonService: ActionCommonService,
  ) {}

  async getMaxId() {
    const { maxId } = await this.ipHistoryRepository.createQueryBuilder('a').select('MAX(a.id) as maxId').getRawOne();

    return maxId;
  }

  /**
   * 获取 srcDevices
   */
  async getSrcDevices() {
    const result = await this.ipHistoryRepository
      .createQueryBuilder('a')
      .select('GROUP_CONCAT(DISTINCT a.srcDevice)', 'srcDevices')
      .getRawOne();

    // 将结果转换为数组
    const srcDevicesArray = result.srcDevices ? result.srcDevices.split(',').filter((i) => !isEmpty(i)) : [];

    return srcDevicesArray;
  }

  /**
   * 获取 upstream
   */
  async getUpstream() {
    const result = await this.ipHistoryRepository
      .createQueryBuilder('a')
      .select('GROUP_CONCAT(DISTINCT a.upstream)', 'upstream')
      .getRawOne();

    // 将结果转换为数组
    const upstreamArray = result.upstream ? result.upstream.split(',').filter((i) => !isEmpty(i)) : [];

    return upstreamArray;
  }

  /**
   * 获取 srcLocation
   */
  async getSrcLocation() {
    const result = await this.ipHistoryRepository
      .createQueryBuilder('a')
      .select('GROUP_CONCAT(DISTINCT a.srcLocation)', 'srcLocations')
      .getRawOne();

    // 将结果转换为数组
    const srcLocationsArray = result.srcLocations ? result.srcLocations.split(',').filter((i) => !isEmpty(i)) : [];

    return srcLocationsArray;
  }

  // 获取IP流量数据区间，Ipadd去重获取最大的ID
  async getBetween(minId: number, maxId: number) {
    const subQuery = this.ipHistoryRepository
      .createQueryBuilder('a')
      .select('MAX(a.id)', 'maxId')
      .addSelect('a.ipadd')
      .where('a.id > :minId', { minId })
      .andWhere('a.id <= :maxId', { maxId })
      .andWhere('a.ipadd IS NOT NULL')
      .andWhere('a.analyzePolicyId IS NOT NULL')
      .groupBy('a.ipadd')
      .addGroupBy('a.upstream');

    return this.ipHistoryRepository
      .createQueryBuilder('b')
      .select(['b.*', 'b.analyzePolicyId as analyzeId'])
      .innerJoin(`(${subQuery.getQuery()})`, 'sub', 'b.id = sub.maxId')
      .setParameters(subQuery.getParameters())
      .orderBy('b.id', 'DESC')
      .getRawMany();
  }

  async save(flows) {
    return this.ipHistoryRepository.createQueryBuilder().insert().values(flows).execute();
  }

  // 手动执行IP操作
  async manualExecute(id: number, actionId: number) {
    const target = await this.dataSource
      .createQueryBuilder()
      .select('a.*')
      .addSelect('b.customer customer')
      .addSelect('b.location location')
      .addSelect('b.productNo productNo')
      .addSelect('b.productName productName')
      .addSelect('b.originAsn originAsn')
      .addSelect('b.ip ip')
      .from(IPHistory, 'a')
      .leftJoin(IPConfig, 'b', 'a.bestSubnetId = b.id')
      .where('a.id = :id', { id })
      .getRawOne<IPHistory>();

    if (isEmpty(target)) {
      return { code: 1001, message: 'Not Data By ID' };
    }

    const abnormalList = await this.actionCommonService.executeAbnormalList([
      {
        id: target.id,
        actionId,
        ip: target.ipadd,
        values: formatValues(target),
      },
    ]);

    return {
      code: abnormalList.every(({ result }) => result.every((i) => i.isOk)) ? 1000 : 1001,
      message: abnormalList.map(({ result }) => result?.map((i) => i.prefix).join('\n')).join('\n'),
    };
  }

  async getTop(query: any) {
    const start = query.start;
    const end = query.end;
    const order = query.order || 'bps';
    const sort = query.sort || 'DESC';
    const size = query.size || 50;
    const page = query.page || 1;
    const skip = (page - 1) * size;
    const srcLocation = isArray(query.srcLocation)
      ? query.srcLocation
      : isExist(query.srcLocation)
        ? [query.srcLocation]
        : undefined;
    const srcDevice = isArray(query.srcDevice)
      ? query.srcDevice
      : isExist(query.srcDevice)
        ? [query.srcDevice]
        : undefined;
    const upstream = isArray(query.upstream) ? query.upstream : isExist(query.upstream) ? [query.upstream] : undefined;

    return this.dataSource
      .createQueryBuilder()
      .select('a.*')
      .from(IPHistory, 'a')
      .where('a.firstTime >= :start AND a.firstTime <= :end', { start, end })
      .andWhere(srcLocation ? 'a.srcLocation IN (:...srcLocation)' : '1=1', {
        srcLocation,
      })
      .andWhere(srcDevice ? 'a.srcDevice IN (:...srcDevice)' : '1=1', {
        srcDevice,
      })
      .andWhere(upstream ? 'a.upstream IN (:...upstream)' : '1=1', {
        upstream,
      })
      .addOrderBy(`a.${order}`, sort)
      .limit(size)
      .offset(skip)
      .getRawMany();
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    const builder = this.ipHistoryRepository.createQueryBuilder('a');
    const { list, pagination } = await selectBuilder.page<any>(query, option, builder, false);

    const analyzePolicyIds = uniq(list.map((i) => i.analyzePolicyId));

    const analyzePolicyPromise = this.analyzePolicyRepository.find({
      select: ['id', 'name'],
      where: { id: In(analyzePolicyIds) },
    });

    const analyzePolicys = await analyzePolicyPromise;

    // if (!query.order && !query.sort) {
    //   list = chain(list).sortBy('firstTime').reverse().value();
    // }

    return {
      pagination,
      list: list.map((item: any) => {
        const analyzePolicy = analyzePolicys.find((p) => {
          return p.id === item.analyzePolicyId;
        });

        if (!isEmpty(analyzePolicy)) {
          item.analyzePolicyId = analyzePolicy.id;
          item.analyzePolicyName = analyzePolicy.name;
        }

        return item;
      }),
    };
  }

  /**
   * 新增
   * @param data
   */
  async create(data: IPHistory) {
    return this.ipHistoryRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: IPHistory) {
    await this.ipHistoryRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.ipHistoryRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.ipHistoryRepository.findOne({ where: { id } });
  }
}

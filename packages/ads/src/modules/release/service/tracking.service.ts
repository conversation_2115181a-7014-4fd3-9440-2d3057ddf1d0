import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { dayjs } from 'src/utils/dayjs';
import { chain } from 'lodash';
import { DataSource, In, Repository } from 'typeorm';
import { AbnormalCurrent, AbnormalCurrentStatusEnum } from 'src/modules/abnormal/entities';
import { CommonService } from 'src/common/common.service';
import { isEmpty, isExist } from 'src/utils/types';
import { Device } from 'src/modules/device/entities/device.entity';
import { ReleaseLog } from '../entities/release-log.entity';

/**
 * AbnormalCurrentService：追踪服务
 */
@Injectable()
export class AbnormalCurrentService {
  constructor(
    @InjectRepository(AbnormalCurrent)
    private readonly abnormalCurrentRepository: Repository<AbnormalCurrent>,

    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,

    private readonly dataSource: DataSource,

    private readonly commonService: CommonService,
  ) {}

  /**
   * 自动执行AbnormalCurrent Release
   * AbnormalCurrent Release执行成功将删除数据库表记录项目
   */
  async executeRelease(abnormals?: AbnormalCurrent[]) {
    if (isEmpty(abnormals)) {
      abnormals = await this.abnormalCurrentRepository
        .createQueryBuilder('a')
        .select(['a.*'])
        .where('a.status = :status', {
          status: AbnormalCurrentStatusEnum.PENDING,
        })
        .andWhere('a.releaseDateTime < :now', {
          now: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        })
        .getRawMany();
    }

    if (isEmpty(abnormals)) return;

    // 获取不需要进行ssh操作释放的id
    const notExecuteIds = abnormals.filter((a) => a.isGroup || !a.deviceId || !a.releaseScript).map((a) => a.id);

    // 获取设备ID
    const deviceIds = abnormals.filter((a) => a.deviceId).map((a) => a.deviceId);

    let executeResults;

    if (isExist(deviceIds)) {
      // 获取 Devices
      const devices = await this.deviceRepository
        .createQueryBuilder('a')
        .select('a.*')
        .where('a.id IN (:...ids)', { ids: deviceIds })
        .getRawMany();

      // 遍历 AbnormalCurrents 拼接操作指令
      const commandGroupByDeviceId = chain(abnormals)
        .filter((a) => !notExecuteIds.includes(a.id))
        .groupBy('deviceId')
        .mapValues((abnormals) => {
          const commands = abnormals
            .map((t) => {
              return (t.releaseScript as string)
                ?.split('\n')
                .map((script) => script.trim())
                .concat(`!!!tid:${t.id}!!!`)
                .join(';');
            })
            .join(';');

          return commands;
        })
        .value();

      // 执行结果
      const resultPromises = chain(commandGroupByDeviceId)
        .map(async (command, deviceId) => {
          const device = devices.find((d) => d.id === +deviceId);
          if (isEmpty(device)) return;
          return this.commonService.executeScript(device, command);
        })
        .value();

      const result = await Promise.all(resultPromises);
      executeResults = chain(result)
        .flatten()
        .map((r) => {
          r.abnormal = abnormals.find((t) => t.id == r.tid);
          return r;
        })
        .value();
    }

    await Promise.all([
      this.handleExecuteError(executeResults),
      this.handleExecuteSuccess(executeResults),
      isExist(notExecuteIds)
        ? this.dataSource
            .createQueryBuilder()
            .delete()
            .from(AbnormalCurrent)
            .where('id IN (:...ids)', { ids: notExecuteIds })
            .execute()
        : null,
    ]);

    return {
      code: 1000,
      message: isExist(executeResults) ? executeResults.map(({ prefix }) => prefix) : 'Release Success!',
    };
  }

  /**
   * 手动执行/立即执行 Release
   * Abnormal Current Release执行成功将删除数据库表记录项目
   * @param id Abnormal Current ID
   * @returns
   */
  async releaseImmediately(id: number) {
    const abnormals = await this.dataSource
      .createQueryBuilder()
      .select()
      .from(AbnormalCurrent, 'a')
      .where('a.id = :id', { id })
      .orWhere('a.parentId = :id', { id })
      .getRawMany();

    return this.executeRelease(abnormals);
  }

  /**
   *
   * 传入IPS，在AbnormalCurrent表中查询是否存在，
   * 返回 ips中 不存在的IP str
   */
  async checkIpsExist(ips: string[]): Promise<string[]> {
    const abnormalCurrentIps = await this.abnormalCurrentRepository.find({
      where: { ipadd: In(ips) },
    });
    const abnormalCurrentIpSet = new Set(abnormalCurrentIps.map((item) => item.ipadd));
    return ips.filter((ip) => !abnormalCurrentIpSet.has(ip));
  }

  // 处理执行失败
  async handleExecuteError(executeResults) {
    if (isEmpty(executeResults)) return;

    const errorResults = executeResults.filter((result) => {
      return !result.isOk;
    });

    if (isExist(errorResults)) {
      const abnormalCurrentIds = errorResults.map(({ abnormal }) => {
        return abnormal.id;
      });

      // 修改abnormalCurrent状态
      await this.abnormalCurrentRepository
        .createQueryBuilder()
        .update()
        .set({ status: AbnormalCurrentStatusEnum.FAILED })
        .where('id IN (:...ids)', { ids: abnormalCurrentIds })
        .execute();

      // 增加日志
      const releaseLogs = errorResults.map(({ abnormal, prefix }) => {
        const { ipadd, deviceId } = abnormal;
        return {
          ipadd,
          deviceId,
          status: false,
          actionResult: prefix,
        };
      });

      await this.dataSource.createQueryBuilder().insert().into(ReleaseLog).values(releaseLogs).execute();
    }
  }

  // 处理执行成功
  async handleExecuteSuccess(executeResults) {
    if (isEmpty(executeResults)) return;

    const successResults = executeResults.filter((result) => {
      return result.isOk;
    });

    if (isExist(successResults)) {
      // 删除abnormalCurrent
      const abnormalCurrentIds = successResults.map(({ abnormal }) => {
        return abnormal.id;
      });

      await this.abnormalCurrentRepository
        .createQueryBuilder()
        .delete()
        .where('id IN (:...ids)', { ids: abnormalCurrentIds })
        .execute();

      const releaseLogs = successResults.map(({ abnormal, prefix }) => {
        const { ipadd, deviceId } = abnormal;
        return {
          ipadd,
          deviceId,
          status: true,
          actionResult: prefix,
        };
      });

      // 新增日志
      await this.dataSource.createQueryBuilder().insert().into(ReleaseLog).values(releaseLogs).execute();
    }
  }
}

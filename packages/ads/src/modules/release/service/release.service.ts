import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { AbnormalCurrent } from 'src/modules/abnormal/entities';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import { chain, pick } from 'lodash';
import { isExist } from 'src/utils/types';

/**
 * ReleaseService：释放服务
 * - 基本CRUD
 */
@Injectable()
export class ReleaseService {
  constructor(
    @InjectRepository(AbnormalCurrent)
    private readonly abnormalCurrentRepository: Repository<AbnormalCurrent>,
  ) {}

  /**
   * 分页查询
   */
  async page(query: any, option: PageOption) {
    const { list, pagination } = await selectBuilder.page(
      query,
      option,
      this.abnormalCurrentRepository.createQueryBuilder('a'),
    );

    const parentIds = list.map((i) => i.id);
    const children = isExist(parentIds)
      ? await this.abnormalCurrentRepository.find({
          where: { parentId: In(parentIds) },
        })
      : [];

    return {
      list: list.map((a) => {
        return {
          ...a,
          children: children.filter((c) => c.parentId === a.id),
        };
      }),
      pagination,
    };
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AbnormalCurrent) {
    return this.abnormalCurrentRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: AbnormalCurrent) {
    await this.abnormalCurrentRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.abnormalCurrentRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.abnormalCurrentRepository.findOne({ where: { id } });
  }
}

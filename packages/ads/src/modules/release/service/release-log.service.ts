import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ReleaseLog } from '../entities/release-log.entity';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';

@Injectable()
export class ReleaseLogService {
  constructor(
    @InjectRepository(ReleaseLog)
    private readonly releaseLogRepository: Repository<ReleaseLog>,
  ) {}

  /**
   * 分页
   */
  async find(query: Record<string, any>, option: PageOption) {
    return selectBuilder.page(query, option, this.releaseLogRepository.createQueryBuilder('a'));
  }

  /**
   * 详情
   */
  async findOne(id: number) {
    return this.releaseLogRepository.findOne({ where: { id } });
  }

  /**
   * 增加
   */
  async create(data: ReleaseLog | ReleaseLog[]) {
    return this.releaseLogRepository.insert(data);
  }

  /**
   * 修改
   */
  async update(id: number, data: ReleaseLog) {
    return this.releaseLogRepository.update(id, data);
  }

  /**
   * 删除
   */
  async remove(id: number | number[]) {
    return this.releaseLogRepository.delete(id);
  }
}

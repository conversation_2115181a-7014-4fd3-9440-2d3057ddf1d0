import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { Device } from 'src/modules/device/entities/device.entity';

@Entity('op_ads_release_log')
export class ReleaseLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '操作IP', default: '', nullable: true })
  ipadd: string;

  @Column({ comment: '操作结果是否成功', type: 'boolean', nullable: true })
  status: boolean;

  @ManyToOne(() => Device, { nullable: true })
  device: Device;
  @Column({ comment: '操作设备', nullable: true })
  deviceId: number;

  @Column({ comment: 'username', default: '' })
  username?: string;

  @Column({ comment: '释放结果', type: 'text', nullable: true })
  actionResult: string;

  @CreateDateColumn()
  createTime: Date;
}

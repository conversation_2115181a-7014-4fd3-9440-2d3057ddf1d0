import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AbnormalCurrent } from 'src/modules/abnormal/entities';
import { ReleaseScheduleService } from './schedule/release.schedule';
import { ReleaseService } from './service/release.service';
import { AbnormalCurrentService } from './service/tracking.service';
import { ReleaseLogService } from './service/release-log.service';
import { ReleaseController } from './release.controller';
import { ReleaseLogController } from './controller/release-log.controller';
import { CommonModule } from 'src/common/common.module';
import { Device } from '../device/entities/device.entity';
import { ReleaseLog } from './entities/release-log.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ReleaseLog, AbnormalCurrent, Device]), CommonModule],
  providers: [ReleaseScheduleService, ReleaseService, AbnormalCurrentService, ReleaseLogService],
  exports: [ReleaseService, AbnormalCurrentService],
  controllers: [ReleaseController, ReleaseLogController],
})
export class ReleaseModule {}

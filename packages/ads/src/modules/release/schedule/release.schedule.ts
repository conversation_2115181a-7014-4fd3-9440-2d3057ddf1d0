import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AbnormalCurrentService } from 'src/modules/release/service/tracking.service';

@Injectable()
export class ReleaseScheduleService {
  constructor(private readonly abnormalCurrentService: AbnormalCurrentService) {}

  // 每1分钟执行一次
  @Cron(CronExpression.EVERY_MINUTE)
  async release() {
    try {
      await this.abnormalCurrentService.executeRelease();
    } catch (err) {
      console.error(err.message);
    }
  }
}

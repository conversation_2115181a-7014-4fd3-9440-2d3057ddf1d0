import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ReleaseLogService } from '../service/release-log.service';
import { defineOption } from 'src/utils/orm/builder';
import { Device } from '../../device/entities/device.entity';

@Controller('release/log')
export class ReleaseLogController {
  private readonly pageQueryOp = defineOption({
    select: ['a.*', 'b.name deviceName'],
    fieldEq: [{ column: 'a.status', requestParam: 'status' }],
    fieldLike: [{ column: 'a.ipadd', requestParam: 'ipadd' }],
    join: [
      {
        entity: Device,
        alias: 'b',
        condition: 'a.deviceId = b.id',
        type: 'leftJoin',
      },
    ],
  });

  constructor(private readonly releaseLogService: ReleaseLogService) {}

  @Post('page')
  async page(@Body() query: any) {
    return this.releaseLogService.find(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data: any) {
    return this.releaseLogService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.releaseLogService.update(data.id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids: any) {
    return this.releaseLogService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.releaseLogService.findOne(id);
  }
}

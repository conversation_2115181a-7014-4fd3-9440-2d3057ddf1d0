import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ReleaseService } from './service/release.service';
import { defineOption } from 'src/utils/orm/builder';
import { Device } from '../device/entities/device.entity';
import { AbnormalCurrentService } from './service/tracking.service';
import { ActionPolicy } from '../action/entities/action-policy.entity';
import { AbnormalCurrentStatusEnum } from 'src/modules/abnormal/entities';
import { isEmpty } from 'src/utils/types';

@Controller('release')
export class ReleaseController {
  constructor(
    private readonly releaseService: ReleaseService,
    private readonly abnormalCurrentService: AbnormalCurrentService,
  ) {}

  pageQueryOp(query) {
    return defineOption({
      select: ['a.*', 'b.name deviceName', 'c.name actionPolicyName'],
      fieldLike: [{ column: 'a.ipadd', requestParam: 'ipadd' }],
      fieldEq: [{ column: 'a.deviceId', requestParam: 'deviceId' }],
      join: [
        {
          entity: Device,
          type: 'leftJoin',
          alias: 'b',
          condition: 'a.deviceId = b.id',
        },
        {
          entity: ActionPolicy,
          type: 'leftJoin',
          alias: 'c',
          condition: 'c.id = a.actionPolicyId',
        },
      ],
      extend: (builder) => {
        const { order, sort } = query || {};

        builder.andWhere('a.parentId IS NULL');

        if (isEmpty(order) && isEmpty(sort)) {
          builder
            .orderBy(`CASE WHEN a.status = "${AbnormalCurrentStatusEnum.FAILED}" THEN 0 ELSE 1 END`, 'ASC')
            .addOrderBy('a.status', 'ASC')
            .addOrderBy('a.createTime', 'DESC');
        }
      },
    });
  }

  @Post('execTrackingRelease')
  async execTrackingRelease(@Body('id') id) {
    return this.abnormalCurrentService.releaseImmediately(id);
  }

  @Post('page')
  async page(@Body() query) {
    return this.releaseService.page(query, this.pageQueryOp(query));
  }

  @Post('add')
  async add(@Body() params) {
    return this.releaseService.create(params);
  }

  @Post('update')
  async update(@Body() params) {
    return this.releaseService.update(params.id, params);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return this.releaseService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id) {
    return this.releaseService.findOne(id);
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conf } from './entities/conf.entity';

@Injectable()
export class ConfService {
  constructor(
    @InjectRepository(Conf)
    private readonly confRepository: Repository<Conf>,
  ) {}

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any) {
    return this.confRepository.find({ where: query });
  }

  /**
   * 新增
   * @param data
   */
  async create(data: any) {
    return this.confRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: Conf) {
    await this.confRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.confRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.confRepository.findOne({ where: { id } });
  }

  /**
   * 获取value
   */
  async getValue(key: string) {
    const conf = await this.confRepository.findOne({ where: { key } });
    return conf?.value;
  }

  /**
   * 设置value
   */
  async setValue(key: string, value: string) {
    const conf = await this.confRepository.findOne({ where: { key } });

    if (!conf) {
      return this.confRepository.insert({ key, value });
    }

    return this.confRepository.update({ key }, { value });
  }
}

import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('op_ads_conf')
export class Conf {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: 'Key' })
  key: string;

  @Column({ comment: 'Value', length: 2550 })
  value: string;

  @Column({ comment: '描述', default: '' })
  desc: string;

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;
}

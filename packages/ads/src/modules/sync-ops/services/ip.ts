import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';

import { intersectionBy, differenceBy } from 'lodash';
import { requestOPSClient } from '@/utils/http/axios';
import { selectBuilder } from 'src/utils/orm/builder';

import dayjs from 'src/utils/dayjs';
import { logger } from '@/utils/logger';
import { isEmpty } from 'src/utils/types';
import { IPAddress } from '../entities/ip';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class SyncOPSIpAddressService {
  constructor(
    @InjectRepository(IPAddress)
    private readonly ipAddressRepository: Repository<IPAddress>,
    private readonly dataSource: DataSource,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handler(timestamp?: number) {
    try {
      if (isEmpty(timestamp)) {
        timestamp = await this.getLastUpdateTime();
        if (isEmpty(timestamp)) {
          logger.error('获取IP地址上次更新时间戳失败');
          return;
        }
      }

      const ipAddressList = await this.fetchIpAddressList(timestamp);
      if (isEmpty(ipAddressList)) return;

      const { existingIpAddress, nonExistingIpAddress } = await this.checkIpAddressNumInDatabase(ipAddressList);

      await Promise.all([this.updateIpAddressList(existingIpAddress), this.insertIpAddressList(nonExistingIpAddress)]);

      if (existingIpAddress.length || nonExistingIpAddress.length) {
        logger.info(`IP地址同步完成：更新${existingIpAddress.length}条，新增${nonExistingIpAddress.length}条`);
      }
    } catch (error) {
      logger.error(`同步OPS IP信息失败，错误信息：${error.message}`, {
        stack: error.stack,
        timestamp,
      });
    }
  }

  async getLastUpdateTime() {
    const [result] = await this.dataSource.query('select MAX(updateTime) as lastUpdateTime from op_ads_ip_address');

    const lastUpdateTime = result.lastUpdateTime;
    if (isEmpty(lastUpdateTime)) {
      return dayjs('2001-01-01 00:00:00').valueOf();
    }

    const timestamp = dayjs(result.lastUpdateTime).valueOf();
    return timestamp;
  }

  async fetchIpAddressList(timestamp: number): Promise<any[]> {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        const data = await requestOPSClient.get('/app/tec/ipadd/updated/after/timestamp', {
          params: { timestamp },
          timeout: 10000,
        });

        if (!data || !data.data) {
          throw new Error('Invalid response data');
        }

        return data.data;
      } catch (error) {
        retryCount++;
        if (retryCount === maxRetries) {
          throw new Error(`获取IP地址列表失败，已重试 ${maxRetries} 次：${error.message}`);
        }
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }

    return [];
  }

  async checkIpAddressNumInDatabase(ipAddressList: IPAddress[]) {
    const ipAddressNumList = ipAddressList.filter((item) => item.ipAddressNum).map((item) => item.ipAddressNum);

    const existingIpAddressList = await this.ipAddressRepository.find({
      where: {
        ipAddressNum: In(ipAddressNumList),
      },
    });

    // 使用 lodash 区分存在和不存在的 ipAddress
    const existingIpAddress = intersectionBy(ipAddressList, existingIpAddressList, 'ipAddressNum');

    const nonExistingIpAddress = differenceBy(ipAddressList, existingIpAddressList, 'ipAddressNum');

    return {
      existingIpAddress,
      nonExistingIpAddress,
    };
  }

  async insertIpAddressList(ipAddressList: IPAddress[]) {
    if (isEmpty(ipAddressList)) return;
    try {
      await this.ipAddressRepository.insert(ipAddressList);
    } catch (error) {
      logger.error(`批量插入IP地址记录失败：${error.message}`);
      throw error;
    }
  }

  async updateIpAddressList(ipAddressList: IPAddress[]) {
    if (isEmpty(ipAddressList)) return;
    try {
      await this.ipAddressRepository.manager.transaction(async (runner) => {
        return Promise.all(
          ipAddressList.map((item) => {
            return runner.update(IPAddress, { ipAddressNum: item.ipAddressNum }, item).catch((error) => {
              logger.error(`更新IP地址记录失败，ipAddressNum: ${item.ipAddressNum}，错误：${error.message}`);
              throw error;
            });
          }),
        );
      });
    } catch (error) {
      logger.error(`批量更新IP地址记录失败：${error.message}`);
      throw error;
    }
  }

  async resynchronize() {
    const timestamp = dayjs('2001-01-01 00:00:00').valueOf();
    return await this.handler(timestamp);
  }

  async page(query: any, option: any) {
    const builder = this.ipAddressRepository.createQueryBuilder('a');
    return selectBuilder.page(query, option, builder);
  }

  async create(data: IPAddress) {
    const savedDevice = await this.ipAddressRepository.save(data);
    return {
      id: savedDevice.id,
    };
  }

  async update(ipAddress: IPAddress) {
    await this.ipAddressRepository.save(ipAddress);
  }

  async batchUpdate(ids, data: IPAddress) {
    await this.ipAddressRepository.update(ids, data);
  }

  async remove(id: number | Array<number>) {
    return this.ipAddressRepository.delete(id);
  }

  async findOne(id: number) {
    return this.ipAddressRepository.findOne({ where: { id } });
  }
}

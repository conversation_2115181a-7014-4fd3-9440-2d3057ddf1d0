import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';

import { IPAddress } from '../entities/ip';
import { IPSubnet } from '../entities/subnet';

@Injectable()
export class IpAddressAndSubnetService {
  constructor(
    @InjectRepository(IPAddress)
    private readonly ipAddressRepository: Repository<IPAddress>,

    @InjectRepository(IPSubnet)
    private readonly ipSubnetRepository: Repository<IPSubnet>,
  ) {}

  async getIpAddressList(ipAddressList: string[]) {
    return this.ipAddressRepository.find({
      where: { ipAddress: In(ipAddressList) },
    });
  }

  async getIpSubnetList(ipSubnetList: string[]) {
    return this.ipSubnetRepository.find({
      where: { subnet: In(ipSubnetList) },
    });
  }

  async getIpAddressAndIpSubnetList(ipAddressStringList: string[]) {
    // 获取IP地址列表
    const ipAddressList = await this.getIpAddressList(ipAddressStringList);

    // 获取IP地址列表中opsIpSubnetId不为空的IP地址
    const opsIpSubnetIdList = ipAddressList
      .filter((item) => item.opsIpSubnetId !== null)
      .map((item) => item.opsIpSubnetId);

    // 获取IP地址列表中opsIpSubnetId不为空的IP地址对应的IP子网列表
    let ipSubnetList = [];
    if (opsIpSubnetIdList.length > 0) {
      ipSubnetList = await this.ipSubnetRepository.find({
        where: { opsIpSubnetId: In(opsIpSubnetIdList) },
      });
    }

    // 返回IP地址列表和IP子网列表
    return ipAddressStringList.map((ipAddressString) => {
      const ipAddressItem = ipAddressList.find((item) => {
        return item.ipAddress === ipAddressString;
      });

      const ipSubnetItem = ipSubnetList.find((item) => {
        return ipAddressItem.opsIpSubnetId === item.opsIpSubnetId;
      });

      return {
        ipAddress: ipAddressString,
        customerNo: ipAddressItem?.customerNo || ipSubnetItem?.customerNo,
        serviceNo: ipAddressItem?.serviceNo || ipSubnetItem?.serviceNo,
        serviceNo2: ipAddressItem?.serviceNo2 || ipSubnetItem?.serviceNo2,
        originAsn: ipSubnetItem?.originAsn,
        location: ipSubnetItem?.location,
        productName: ipSubnetItem?.productName,
        description: ipAddressItem?.description,
      };
    });
  }
}

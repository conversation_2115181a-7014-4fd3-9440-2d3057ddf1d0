import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource, In, Repository } from 'typeorm';
import { requestOPSClient } from '@/utils/http/axios';
import * as ip from 'ip';
import { intersectionBy, differenceBy, chain } from 'lodash';
import dayjs from 'src/utils/dayjs';
import { selectBuilder } from 'src/utils/orm/builder';
import { isEmpty } from 'src/utils/types';
import { IPSubnet } from '../entities/subnet';
import { logger } from '@/utils/logger';

@Injectable()
export class SyncOPSIpSubnetService {
  constructor(
    @InjectRepository(IPSubnet)
    private readonly ipSubnetRepository: Repository<IPSubnet>,
    private readonly dataSource: DataSource,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handler(timestamp?: number) {
    try {
      if (isEmpty(timestamp)) {
        timestamp = await this.getLastUpdateTime();
        if (isEmpty(timestamp)) {
          logger.error('获取上次更新时间戳失败');
          return;
        }
      }

      const originalSubnetList = await this.fetchIpSubnetList(timestamp);
      if (isEmpty(originalSubnetList)) return;

      const ipSubnetList = chain(originalSubnetList).uniqBy('subnetNum').value();

      const { existingIpSubnet, nonExistingIpSubnet } = await this.checkIpSubnetNumInDatabase(ipSubnetList);

      await Promise.all([this.updateIpAddressList(existingIpSubnet), this.insertIpAddressList(nonExistingIpSubnet)]);

      if (existingIpSubnet.length || nonExistingIpSubnet.length) {
        logger.info(`Subnet同步完成：更新${existingIpSubnet.length}条，新增${nonExistingIpSubnet.length}条`);
      }
    } catch (error) {
      logger.error(`同步OPS Subnet信息失败，错误信息：${error.message}`, {
        stack: error.stack,
        timestamp,
      });
    }
  }

  async getLastUpdateTime() {
    const [result] = await this.dataSource.query('select MAX(updateTime) as lastUpdateTime from op_ads_ip_subnet');

    const lastUpdateTime = result.lastUpdateTime;
    if (isEmpty(lastUpdateTime)) {
      return dayjs('2001-01-01 00:00:00').valueOf();
    }

    const timestamp = dayjs(result.lastUpdateTime).valueOf();
    return timestamp;
  }

  async fetchIpSubnetList(timestamp: number): Promise<any[]> {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        const data = await requestOPSClient.get('/app/tec/ipsubnet/updated/after/timestamp', {
          params: { timestamp },
          timeout: 10000,
        });

        if (!data || !data.data) {
          throw new Error('Invalid response data');
        }

        return data.data;
      } catch (error) {
        retryCount++;
        if (retryCount === maxRetries) {
          throw new Error(`获取 Subnet 列表失败，已重试 ${maxRetries} 次：${error.message}`);
        }
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }

    return [];
  }

  async checkIpSubnetNumInDatabase(ipSubnetList: IPSubnet[]) {
    const ipSubnetListNum = ipSubnetList.filter((item) => item.subnetNum).map((item) => item.subnetNum);

    const existingIpSubnetList = await this.ipSubnetRepository.find({
      where: {
        subnetNum: In(ipSubnetListNum),
      },
    });

    // 使用 lodash 区分存在和不存在的 ipAddress
    const existingIpSubnet = intersectionBy(ipSubnetList, existingIpSubnetList, 'subnetNum');

    const nonExistingIpSubnet = differenceBy(ipSubnetList, existingIpSubnetList, 'subnetNum');

    return {
      existingIpSubnet,
      nonExistingIpSubnet,
    };
  }

  async updateIpAddressList(ipSubnetList: IPSubnet[]) {
    if (isEmpty(ipSubnetList)) return;
    try {
      await this.ipSubnetRepository.manager.transaction(async (runner) => {
        return Promise.all(
          ipSubnetList.map((item) => {
            return runner.update(IPSubnet, { subnetNum: item.subnetNum }, item).catch((error) => {
              logger.error(`更新Subnet记录失败，subnetNum: ${item.subnetNum}，错误：${error.message}`);
              throw error;
            });
          }),
        );
      });
    } catch (error) {
      logger.error(`批量更新Subnet记录失败：${error.message}`);
      throw error;
    }
  }

  async insertIpAddressList(ipSubnetList: IPSubnet[]) {
    if (isEmpty(ipSubnetList)) return;
    try {
      await this.ipSubnetRepository.insert(ipSubnetList);
    } catch (error) {
      logger.error(`批量插入Subnet记录失败：${error.message}`);
      throw error;
    }
  }

  async resynchronize() {
    const timestamp = dayjs('2001-01-01 00:00:00').valueOf();
    await this.handler(timestamp);
  }

  async page(query: any, option: any) {
    const builder = this.ipSubnetRepository.createQueryBuilder('a');
    return selectBuilder.page(query, option, builder);
  }

  async create(data: IPSubnet) {
    const savedDevice = await this.ipSubnetRepository.save(data);
    return {
      id: savedDevice.id,
    };
  }

  async update(data: IPSubnet) {
    await this.ipSubnetRepository.save(data);
  }

  async batchUpdate(ids, data: IPSubnet) {
    await this.ipSubnetRepository.update(ids, data);
  }

  async remove(id: number | Array<number>) {
    return this.ipSubnetRepository.delete(id);
  }

  async findOne(id: number) {
    return this.ipSubnetRepository.findOne({ where: { id } });
  }
}

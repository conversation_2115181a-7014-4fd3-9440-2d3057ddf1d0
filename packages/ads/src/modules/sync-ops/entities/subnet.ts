import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Column, Index } from 'typeorm';

@Entity('op_ads_ip_subnet')
export class IPSubnet {
  @PrimaryGeneratedColumn()
  id: number;

  @Index({ unique: true })
  @Column({ comment: 'Subnet Num', type: 'bigint' })
  subnetNum: string;

  @Column({ comment: 'IP段掩码长度', default: '' })
  prefixLength: string;

  @Index()
  @Column({ comment: 'Ops IP Subnet Id', type: 'int', nullable: true })
  opsIpSubnetId: number;

  @Index({ unique: true })
  @Column({ comment: 'Subnet' })
  subnet: string;

  @Index()
  @Column({ comment: '客户编号', default: '' })
  customerNo: string;

  @Index()
  @Column({ comment: '服务编号', default: '' })
  serviceNo: string;

  @Index()
  @Column({ comment: '服务编号2', default: '' })
  serviceNo2: string;

  @Index()
  @Column({ comment: '区域', default: '' })
  location: string;

  @Index()
  @Column({ comment: 'ASN', default: '' })
  originAsn: string;

  @Index()
  @Column({ comment: '产品名称', default: '' })
  productName: string;

  @Index()
  @Column({ comment: 'Owner', default: '' })
  owner: string;

  @Column({ comment: 'IP描述', default: '' })
  description: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

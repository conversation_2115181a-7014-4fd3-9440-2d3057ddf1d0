import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Column, Index } from 'typeorm';

// import { PageOption } from 'src/utils/orm/builder';
// import { IPSubnet } from '../entities/subnet';
// import { SelectQueryBuilder } from 'typeorm';

@Entity('op_ads_ip_address')
export class IPAddress {
  @PrimaryGeneratedColumn()
  id: number;

  @Index({ unique: true })
  @Column({ comment: 'Subnet Num', type: 'bigint', unsigned: true })
  ipAddressNum: string;

  @Index()
  @Column({ comment: 'Ops IP Subnet Id', type: 'int', nullable: true })
  opsIpSubnetId: number;

  @Index({ unique: true })
  @Column({ comment: 'IP', default: '' })
  ipAddress: string;

  @Column({ comment: 'Subnet Prefix Length', default: '' })
  subnetPrefixLength: string;

  @Column({ comment: 'Subnet', default: '' })
  subnet: string;

  @Index()
  @Column({ comment: '客户编号', default: '' })
  customerNo: string;

  @Index()
  @Column({ comment: '服务编号', default: '' })
  serviceNo: string;

  @Index()
  @Column({ comment: '服务编号2', default: '' })
  serviceNo2: string;

  @Index()
  @Column({ comment: '描述', default: '' })
  description: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date; //
}

// export const getExtraIpAddressInfoDefaultOption = (
//   ipAddressColumn: string,
//   ipSubnetColumn: string,
// ) => {
//   return {
//     select: [
//       'ipSubnet.location location',
//       'ipSubnet.originAsn originAsn',
//       'ipSubnet.productName productName',
//       'ipSubnet.subnet subnet',
//       'ipAddress.serviceNo serviceNo',
//       'COALESCE(ipAddress.customerNo, b.customerNo, c.customerNo) AS serviceNo',
//       // 'COALESCE(ipAddress.customerNo, b.customerNo, c.customerNo) AS customerNo',
//     ],
//     join: [
//       {
//         type: 'leftJoin',
//         entity: IPAddress,
//         alias: 'ipAddress',
//         condition: `${ipAddressColumn} = ipAddress.ipAddress`,
//       },
//       {
//         type: 'leftJoin',
//         entity: IPSubnet,
//         alias: 'ipSubnet',
//         condition: `ipAddress.subnet = ipSubnet.subnet`,
//       },
//     ],
//     extend: <T>(builder: SelectQueryBuilder<T>, ctx) => {
//       const { customerNo, customerNoLike } = ctx.request.body || {};
//     },
//   } as PageOption;
// };

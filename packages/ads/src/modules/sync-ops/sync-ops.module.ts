import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IPAddress } from './entities/ip';
import { IPSubnet } from './entities/subnet';
import { SyncOPSIpAddressService } from './services/ip';
import { SyncOPSIpAddressController } from './controllers/ip';
import { SyncOPSIpSubnetService } from './services/subnet';
import { SyncOPSIpSubnetController } from './controllers/subnet';
import { IpAddressAndSubnetService } from './services/get';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([IPAddress, IPSubnet])],
  controllers: [Sync<PERSON><PERSON>p<PERSON>ddressController, SyncOPSIpSubnetController],
  providers: [SyncOPSIpAddressService, SyncOPSIpSubnetService, IpAddressAndSubnetService],
  exports: [IpAddressAndSubnetService],
})
export class SyncOPSModule {}

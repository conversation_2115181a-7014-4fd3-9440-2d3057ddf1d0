import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { SyncOPSIpAddressService } from '../services/ip';
import { defineOption } from 'src/utils/orm/builder';

@Controller('sync/ops/ipAddress')
export class SyncOPSIpAddressController {
  pageQueryOp = defineOption({
    select: ['a.*'],
    // fieldLike: ['a.username', 'a.host'],
    keyWordLikeFields: ['a.ipAddress', 'a.description', 'a.subnet'],
    fieldEq: [
      { column: 'a.customerNo', requestParam: 'customerNo' },
      { column: 'a.serviceNo', requestParam: 'serviceNo' },
    ],
  });

  constructor(private readonly syncOPSIpAddressService: SyncOPSIpAddressService) {}

  @Post('resynchronize')
  async resynchronize() {
    await this.syncOPSIpAddressService.resynchronize();
  }

  @Post('page')
  async page(@Body() query) {
    return await this.syncOPSIpAddressService.page(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data) {
    return await this.syncOPSIpAddressService.create(data);
  }

  @Post('update')
  async update(@Body() data) {
    return await this.syncOPSIpAddressService.update(data);
  }

  @Post('batch/update')
  async batchUpdate(@Body() data) {
    const { ids, ...params } = data;
    return await this.syncOPSIpAddressService.batchUpdate(ids, params);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return await this.syncOPSIpAddressService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id) {
    return await this.syncOPSIpAddressService.findOne(id);
  }
}

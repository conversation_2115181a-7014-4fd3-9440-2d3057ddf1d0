import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { SyncOPSIpSubnetService } from '../services/subnet';
import { defineOption } from 'src/utils/orm/builder';

@Controller('sync/ops/ipSubnet')
export class SyncOPSIpSubnetController {
  pageQueryOp = defineOption({
    select: ['a.*'],
    // fieldLike: ['a.username', 'a.host'],
    keyWordLikeFields: ['a.description', 'a.subnet'],
    fieldEq: [
      { column: 'a.customerNo', requestParam: 'customerNo' },
      { column: 'a.serviceNo', requestParam: 'serviceNo' },
    ],
  });

  constructor(private readonly syncOPSIpSubnetService: SyncOPSIpSubnetService) {}

  @Post('resynchronize')
  async resynchronize() {
    await this.syncOPSIpSubnetService.resynchronize();
  }

  @Post('page')
  async page(@Body() query) {
    return await this.syncOPSIpSubnetService.page(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data) {
    return await this.syncOPSIpSubnetService.create(data);
  }

  @Post('update')
  async update(@Body() data) {
    return await this.syncOPSIpSubnetService.update(data);
  }

  @Post('batch/update')
  async batchUpdate(@Body() data) {
    const { ids, ...params } = data;
    return await this.syncOPSIpSubnetService.batchUpdate(ids, params);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return await this.syncOPSIpSubnetService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id) {
    return await this.syncOPSIpSubnetService.findOne(id);
  }
}

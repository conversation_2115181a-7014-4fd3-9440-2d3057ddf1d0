import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { IsNull, LessThan, Repository } from 'typeorm';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import { SrcDevice } from '../entities/src-device.entity';
import { RedisService } from 'src/base/redis/redis.service';
import { isUndefined } from 'lodash';
import { isEmpty } from 'src/utils/types';
import { dayjs } from 'src/utils/dayjs';
import { Conf } from 'src/modules/conf/entities/conf.entity';
@Injectable()
export class SrcDeviceService {
  constructor(
    @InjectRepository(SrcDevice)
    private readonly srcDeviceRepository: Repository<SrcDevice>,

    @InjectRepository(Conf)
    private readonly confRepository: Repository<Conf>,

    private readonly redisService: RedisService,
  ) {}

  // 获取lastUpdate离当前时间大于两分钟的srcDevice
  async getAbnormalSrcDevice(minutesAgo: number) {
    const timeAgo = dayjs().subtract(minutesAgo, 'minutes').toDate();
    const srcDevices1 = await this.srcDeviceRepository.find({
      where: [{ lastUpdate: LessThan(timeAgo) }, { lastUpdate: IsNull() }],
    });

    const srcDevices2 = await Promise.all(
      srcDevices1.map(async (device) => {
        const srcDeviceId = device.id;
        const isDelay = await this.redisService.get(`ads:srcDevice:delayAlert:${srcDeviceId}`);

        if (isUndefined(isDelay)) return device;
      }),
    );

    return srcDevices2.filter((i) => !isEmpty(i));
  }

  async delayAlert(srcDeviceId: number, seconds: number) {
    await this.srcDeviceRepository.update(srcDeviceId, {
      delayAlertSeconds: seconds,
    });

    if (seconds === 0) {
      this.redisService.set(`ads:srcDevice:delayAlert:${srcDeviceId}`, seconds);
    } else if (seconds === 1) {
      this.redisService.del(`ads:srcDevice:delayAlert:${srcDeviceId}`);
    } else {
      this.redisService.set(`ads:srcDevice:delayAlert:${srcDeviceId}`, seconds, seconds, 'seconds');
    }
  }

  async setAbnormalTemplate(template: string) {
    await this.confRepository.update({ key: 'srcDevicePushAbnormalTemplate' }, { value: template });
  }

  async getAbnormalTemplate() {
    const template = await this.confRepository.findOne({ where: { key: 'srcDevicePushAbnormalTemplate' } });
    return { template: template?.value };
  }

  /**
   * 分页查询
   * @param query
   * @param option
   * @returns
   */
  async find(query: Record<string, any>, option: PageOption) {
    const { list, pagination } = await selectBuilder.page<any>(
      query,
      option,
      this.srcDeviceRepository.createQueryBuilder('a'),
    );

    const results = await Promise.all(
      list.map(async (srcDevice) => {
        const srcDeviceId = srcDevice.id;
        srcDevice.delaySeconds = await this.redisService.get(`ads:srcDevice:delayAlert:${srcDeviceId}`);
        return srcDevice;
      }),
    );

    return {
      list: results,
      pagination,
    };
  }

  /**
   * 新增
   * @param data
   */
  async create(data: SrcDevice) {
    return this.srcDeviceRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: Partial<SrcDevice>) {
    if (id) {
      await this.srcDeviceRepository.update(id, data);
      return;
    }

    const srcDevice = await this.srcDeviceRepository.findOne({
      where: { name: data.name },
    });

    if (srcDevice) data.id = srcDevice.id;
    await this.srcDeviceRepository.save(data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.srcDeviceRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.srcDeviceRepository.findOne({ where: { id } });
  }

  /**
   * 查询所有需要推送提醒的设备
   * @returns 需要提醒的设备列表
   */
  async findAllAlertDevices() {
    const fiveMinutesAgo = dayjs().subtract(5, 'minute').toDate();

    // 先获取所有延迟超过5分钟的设备
    const delayedDevices = await this.srcDeviceRepository
      .createQueryBuilder('device')
      .where('device.delayAlertSeconds != 0')
      .andWhere('(device.lastUpdate IS NULL OR device.lastUpdate < :fiveMinutesAgo)', { fiveMinutesAgo })
      .getMany();

    // 使用 dayjs 计算时间差，筛选出需要提醒的设备
    const currentTime = dayjs();
    return delayedDevices.filter((device) => {
      if (!device.lastAlertTime) return true;
      const nextAlertTime = dayjs(device.lastAlertTime).add(device.delayAlertSeconds, 'second');
      return nextAlertTime.isBefore(currentTime);
    });
  }
}

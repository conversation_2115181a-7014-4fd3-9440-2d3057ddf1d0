import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import type { Repository } from 'typeorm';
import { Client } from 'ssh2';
import { selectBuilder } from 'src/utils/orm/builder';
import { Device } from '../entities/device.entity';
import { getConnect } from '../utils/connect';

@Injectable()
export class DeviceService {
  constructor(
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
  ) {}

  /**
   * 设备连接测试
   * @param deviceId
   * @returns
   */
  async connectTest(deviceId: number) {
    try {
      const device = await this.deviceRepository.findOne({
        where: { id: deviceId },
      });
      if (!device) throw new Error('设备不存在');

      const connect = await this.connect(deviceId);
      connect.end();
      return {
        code: 1000,
        message: `测试设备${device.name}连接成功`,
      };
    } catch (err) {
      return {
        code: 1001,
        message: `测试连接设备失败 - ${err}`,
      };
    }
  }

  /**
   * 返回设备连接
   * @param deviceId
   * @returns
   */
  async connect(deviceId: number) {
    deviceId = +deviceId;

    const device = await this.deviceRepository.findOne({
      where: { id: deviceId },
    });

    if (!device) {
      throw new Error('设备不存在');
    }

    return getConnect(device);
  }

  /**
   * 传入client实例和command，返回Promise
   */
  async execCommand(client: Client, commands: string | string[]): Promise<string> {
    commands = Array.isArray(commands) ? commands : [commands];

    return new Promise((resolve, reject) => {
      let result = '';

      client.shell((err, stream) => {
        if (err) {
          reject(err);
          return;
        }

        stream
          .on('close', () => {
            resolve(result);
          })
          .on('data', (data) => {
            result += data.toString();
          })
          .stderr.on('data', (data) => {
            result += data;
          });

        // 发送多条指令并添加换行符
        commands.forEach((cmd) => {
          stream.write(cmd + '\n', 'utf-8');
        });

        // 结束会话
        stream.end('exit\n');
      });
    });
  }

  async page(query: any, option: any) {
    const builder = this.deviceRepository.createQueryBuilder('a');
    return selectBuilder.page(query, option, builder);
  }

  async create(data: Device) {
    const savedDevice = await this.deviceRepository.save(data);
    return {
      id: savedDevice.id,
    };
  }

  async update(id: number, data: Device) {
    await this.deviceRepository.save(data);
  }

  async remove(id: number | number[]) {
    return this.deviceRepository.delete(id);
  }

  async info(id: number) {
    return this.deviceRepository.findOne({ where: { id } });
  }

  async findOne(id: number) {
    return this.deviceRepository.findOne({ where: { id } });
  }
}

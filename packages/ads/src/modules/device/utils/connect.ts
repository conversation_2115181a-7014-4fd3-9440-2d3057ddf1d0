import { BadRequestException } from '@nestjs/common';
import { parse } from 'ssh-config';
import { Client, ConnectConfig } from 'ssh2';
import { Device } from '../entities/device.entity';

type ConnectDevice = Partial<Device>;

function convertAlgorithmsToSsh2Config(hostConfig) {
  const algorithms: ConnectConfig['algorithms'] = {};

  if (hostConfig.KexAlgorithms) {
    algorithms.kex = hostConfig.KexAlgorithms.split(',').map((algo) => algo.trim());
  }

  if (hostConfig.HostKeyAlgorithms) {
    algorithms.serverHostKey = hostConfig.HostKeyAlgorithms.split(',').map((algo) => algo.trim());
  }

  if (hostConfig.Ciphers) {
    algorithms.cipher = hostConfig.Ciphers.split(',').map((algo) => algo.trim());
  }

  return algorithms;
}

function getLoginConfig(device: ConnectDevice): ConnectConfig {
  let algorithms;

  if (device.sshConfig) {
    const config = parse(device.sshConfig).compute(device.host);
    algorithms = convertAlgorithmsToSsh2Config(config);
  }

  switch (device.loginType) {
    case 'password':
      return {
        host: device.host,
        port: device.port,
        username: device.username,
        password: device.password,
        algorithms,
      };
    case 'privateKey':
      return {
        host: device.host,
        port: device.port,
        username: device.username,
        privateKey: device.key,
        algorithms,
      };
    default:
      throw new BadRequestException('登录类型不支持');
  }
}

export async function getConnect(device: ConnectDevice): Promise<Client> {
  const conn = new Client();
  const config = getLoginConfig(device);

  return new Promise((resolve, reject) => {
    conn
      .on('ready', () => {
        resolve(conn);
      })
      .on('error', (err) => {
        reject(`Connection :: error :: ${err.message}`);
      })
      .on('end', () => {})
      .connect(config);
  });
}

export async function execCommand(client: Client, command: string): Promise<string> {
  return new Promise((resolve, reject) => {
    let results = '';
    client.shell((err, stream) => {
      if (err) {
        reject(err);
        return;
      }

      stream
        .on('data', (data) => {
          const output = data.toString('utf-8');
          results += output;
        })
        .on('end', () => {
          resolve(results);
        })
        .stderr.on('data', (data) => {
          const output = data.toString('utf-8');
          results += output;
        });

      // 发送多条指令并添加换行符
      // 结束会话
      stream.write(`${command}\n`);
      stream.end('exit\n');
    });
  });
}

// Test Call
// (async () => {
//   const client = await getConnect({
//     loginType: 'password',
//     host: '************',
//     username: 'Nico',
//     password: 'NicozenG13166',
//   });

//   const commands1 = [
//     'config t',
//     'ip route ************* *************** null 0 10 tag 999',
//     'end',
//   ];
//   const commands2 = [
//     'config t',
//     'ip route ************* *************** null 0 10 tag 999',
//     'end',
//   ];

//   try {
//     const result = await Promise.all(
//       [commands1, commands2].map(async (item) => {
//         return execCommand(client, item);
//       }),
//     );

//     console.log(result.forEach((value) => console.log(value)));
//   } catch (error) {
//     console.error('Error executing commands:', error);
//   } finally {
//     client.end();
//   }
// })();

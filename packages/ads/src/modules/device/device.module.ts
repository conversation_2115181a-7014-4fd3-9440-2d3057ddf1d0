import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Device } from './entities/device.entity';
import { DeviceController } from './controller/device.controller';
import { DeviceService } from './service/device.service';
import { RedisModule } from 'src/base';
import { SrcDevice } from './entities/src-device.entity';
import { SrcDeviceController } from './controller/src-device.controller';
import { SrcDeviceService } from './service/src-device.service';
import { DeviceMonitorSchedule } from './schedule/device-monitor.schedule';
import { ConfModule } from '../conf/conf.module';
import { Conf } from '../conf/entities/conf.entity';
@Module({
  imports: [TypeOrmModule.forFeature([Device, SrcDevice, Conf]), RedisModule, ConfModule],
  controllers: [<PERSON>ceController, SrcDeviceController],
  providers: [DeviceService, SrcDeviceService, DeviceMonitorSchedule],
  exports: [DeviceService, SrcDeviceService],
})
export class DeviceModule {}

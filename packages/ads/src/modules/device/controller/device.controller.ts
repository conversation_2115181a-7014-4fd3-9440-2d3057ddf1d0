import { Body, Controller, Get, Post, Query, BadRequestException } from '@nestjs/common';
import { DeviceService } from '../service/device.service';
import { defineOption } from 'src/utils/orm/builder';

@Controller('device')
export class DeviceController {
  pageQueryOp = defineOption({
    select: ['a.*'],
    fieldLike: ['a.username', 'a.host'],
    keyWordLikeFields: ['a.name', 'a.description'],
    fieldEq: ['a.id', 'a.loginType'],
  });

  constructor(private readonly deviceService: DeviceService) {}

  @Post('page')
  async page(@Body() query) {
    return await this.deviceService.page(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data) {
    return await this.deviceService.create(data);
  }

  @Post('update')
  async update(@Body() data) {
    const { id } = data;
    if (!id) throw new BadRequestException('设备ID必传');

    return await this.deviceService.update(id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return await this.deviceService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id) {
    return await this.deviceService.info(id);
  }

  /**
   * 设备连接测试
   */
  @Post('connect')
  async connectTest(@Body('id') id) {
    if (!id) throw new BadRequestException('设备ID不能为空');

    return await this.deviceService.connectTest(id);
  }
}

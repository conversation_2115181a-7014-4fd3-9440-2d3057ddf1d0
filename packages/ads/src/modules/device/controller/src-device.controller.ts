import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { defineOption } from 'src/utils/orm/builder';
import { SrcDeviceService } from '../service/src-device.service';

@Controller('srcDevice')
export class SrcDeviceController {
  pageQueryOp = defineOption({
    select: ['a.*'],
    keyWordLikeFields: ['a.name'],
  });

  constructor(private readonly srcDeviceService: SrcDeviceService) {}

  @Post('update/delayAlert')
  async delayAlert(@Body('srcDeviceId') srcDeviceId: number, @Body('seconds') seconds: number) {
    return this.srcDeviceService.delayAlert(srcDeviceId, seconds);
  }

  @Get('abnormalTemplate')
  async getAbnormalTemplate() {
    return this.srcDeviceService.getAbnormalTemplate();
  }

  @Post('abnormalTemplate')
  async setAbnormalTemplate(@Body('template') template: string) {
    return this.srcDeviceService.setAbnormalTemplate(template);
  }

  @Put()
  async putSrcDevice(@Body() data) {
    return this.srcDeviceService.update(data.id, data);
  }

  @Post('page')
  async find(@Body() query) {
    return this.srcDeviceService.find(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data: any) {
    return this.srcDeviceService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.srcDeviceService.update(data.id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids: any) {
    return this.srcDeviceService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.srcDeviceService.findOne(id);
  }
}

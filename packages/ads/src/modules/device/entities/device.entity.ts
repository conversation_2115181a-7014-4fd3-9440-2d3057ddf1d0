import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('op_ads_device')
export class Device {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '设备类型', default: '' })
  type: string;

  @Column({ comment: '设备名称' })
  name: string;

  @Column({ comment: '主机地址', default: '' })
  host: string;

  @Column({ comment: '端口', nullable: true })
  port: number;

  @Column({ comment: '登录类型', default: '' })
  loginType: string;

  @Column({ comment: '用户名', default: '' })
  username: string;

  @Column({ comment: '密码', default: '' })
  password: string;

  @Column({ comment: '密钥', type: 'text', nullable: true })
  key: string;

  @Column({ comment: '配置', type: 'text', nullable: true })
  sshConfig: string;

  @Column({ comment: '描述', default: '' })
  description: string;

  @Column({ comment: '创建用户', default: '' })
  createBy: string;

  @Column({ comment: '更新用户', default: '' })
  updateBy: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

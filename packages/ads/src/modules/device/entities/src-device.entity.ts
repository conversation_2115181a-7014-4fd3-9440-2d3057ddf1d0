import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity('op_ads_src_device')
export class SrcDevice {
  @PrimaryGeneratedColumn()
  id: number;

  @Index({ unique: true })
  @Column({ comment: '设备名' })
  name: string;

  @Column({ comment: '最后一次更新', type: 'datetime', nullable: true })
  lastUpdate: Date;

  @Column({ comment: '延迟提醒间隔，单位秒', default: 3600 })
  delayAlertSeconds: number;

  @Column({ comment: '最后一次提醒时间', type: 'datetime', nullable: true })
  lastAlertTime: Date;

  @Column({ comment: '信息', default: '' })
  message: string;
}

import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import Handlebars from 'handlebars';
import * as dayjs from 'dayjs';
import { logger } from '@/utils/logger';
import { adsHttp } from '@/utils/http/axios';
import { ConfService } from '@/modules/conf/conf.service';
import { SrcDeviceService } from '../service/src-device.service';
@Injectable()
export class DeviceMonitorSchedule {
  constructor(
    private readonly confService: ConfService,
    private readonly srcDeviceService: SrcDeviceService,
  ) {}

  /**
   * 格式化时间差
   * @param lastUpdate 最后更新时间
   * @returns 格式化的时间差字符串
   */
  private formatTimeDiff(lastUpdate: Date | null): string {
    if (!lastUpdate) return '从未更新';

    const now = dayjs();
    const diff = now.diff(dayjs(lastUpdate), 'minute');

    if (diff < 1) return '1m';
    if (diff < 60) return `${diff}m`;

    const hours = Math.floor(diff / 60);
    const minutes = diff % 60;
    return `${hours}h ${minutes}m`;
  }

  // @Cron('*/10 * * * * *') // 测试，每10秒执行一次
  @Cron('0 * * * * *') // 每分钟的第0秒执行
  async exec() {
    try {
      // 获取所有需要提醒的设备
      const alertDevices = await this.srcDeviceService.findAllAlertDevices();

      // 如果没有需要通知的设备，直接返回
      if (alertDevices.length === 0) return;

      // 获取通知模板
      const templateStr = await this.confService.getValue('srcDevicePushAbnormalTemplate');
      const defaultTemplateStr = `⚠️ 设备监控警告\n设备名称: {{device}}\n最后更新时间: {{lastUpdateTime}}\n当前延迟: {{abnormalTime}}`;
      const defaultTemplate = `{"content": "${defaultTemplateStr}"}`;

      // 编译模板
      const template = Handlebars.compile(templateStr || defaultTemplate);

      // 并行处理需要通知的设备
      await Promise.all(
        alertDevices.map(async (device) => {
          const currentTime = new Date();
          const timeDiff = device.lastUpdate ? (currentTime.getTime() - device.lastUpdate.getTime()) / 1000 : 0;
          const [location] = device.name?.split('-') || [];

          // 使用模板生成消息
          const message = template({
            location,
            device: device.name,
            lastUpdate: device.lastUpdate ? dayjs(device.lastUpdate).format('YYYY-MM-DD HH:mm') : '从未更新',
            abnormalTime: this.formatTimeDiff(device.lastUpdate),
          });

          await adsHttp.sendDiscord(message);
          logger.warn(`设备 ${device.name} 更新延迟超过阈值，延迟时间：${timeDiff}秒，信息：${message}`);

          // 更新设备的最后提醒时间
          await this.srcDeviceService.update(device.id, {
            lastAlertTime: currentTime,
            delayAlertSeconds: 1,
          });
        }),
      );
    } catch (error) {
      logger.error('设备监控任务执行失败:', error);
    }
  }
}

import { Module } from '@nestjs/common';
import { ActionService } from './service/action.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActionLog } from './entities/action-log.entity';
import { ActionPolicy } from './entities/action-policy.entity';
import { ActionPolicyItem } from './entities/action-policy-item.entity';
import { ActionController } from './action.controller';
import { RedisModule } from 'src/base';
import { ActionExecService } from './service/action-exec.service';
import { ActionItemController } from './action-item.controller';
import { ActionItemService } from './service/action-item.service';
import { ActionLogController } from './action-log.controller';
import { ActionLogService } from './service/action-log.service';
import { AbnormalModule } from '../abnormal/abnormal.module';
import { ActionScheduleService } from './schedule/action.schedule';
import { DeviceModule } from '../device/device.module';
import { ReleaseModule } from '../release/release.module';
import { ViewActionPolicy } from './entities/action-policy.view-entity';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([ActionPolicy, ActionPolicyItem, ActionLog, ViewActionPolicy]),
    RedisModule,
    CommonModule,
    AbnormalModule,
    DeviceModule,
    ReleaseModule,
  ],
  controllers: [ActionController, ActionItemController, ActionLogController],
  providers: [ActionScheduleService, ActionService, ActionExecService, ActionItemService, ActionLogService],
  exports: [ActionService, ActionExecService],
})
export class ActionModule {}

import { <PERSON>umn, CreateDateColumn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { ActionPolicyItem } from './action-policy-item.entity';

@Entity('op_ads_action_policy')
export class ActionPolicy {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToMany(() => ActionPolicyItem, (item) => item.actionPolicy)
  actionItems: ActionPolicyItem[];

  @Column({ comment: '策略名称' })
  name: string;

  @Column({ comment: '操作描述', default: '' })
  description: string;

  @Column({ comment: '创建用户', default: '' })
  createBy: string;

  @Column({ comment: '更新用户', default: '' })
  updateBy: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

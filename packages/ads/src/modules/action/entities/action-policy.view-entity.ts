import { DataSource, ViewEntity } from 'typeorm';
import { ActionPolicyItem } from './action-policy-item.entity';
import { ActionPolicy } from './action-policy.entity';

@ViewEntity({
  name: 'view_ads_action_policy',
  expression: (dataSource: DataSource) =>
    dataSource
      .createQueryBuilder()
      .select('ai.id', 'itemId')
      .addSelect('ai.deviceId', 'itemDeviceId')
      .addSelect('ai.actionType', 'itemType')
      .addSelect('ai.description', 'itemDesc')
      .addSelect('ai.releaseTime', 'itemReleaseTime')
      .addSelect('ai.seq', 'itemSeq')
      .addSelect('ai.actionScript', 'itemScript')
      .addSelect('ai.releaseScript', 'itemReleaseScript')
      .addSelect('a.id', 'actionId')
      .addSelect('a.name', 'actionName')
      .addSelect('a.createBy', 'createBy')
      .addSelect('a.createTime', 'createTime')
      .addSelect('a.updateBy', 'updateBy')
      .addSelect('a.updateTime', 'updateTime')
      .from(ActionPolicyItem, 'ai')
      .leftJoin(ActionPolicy, 'a', 'a.id = ai.actionPolicyId'),
})
export class ViewActionPolicy {}

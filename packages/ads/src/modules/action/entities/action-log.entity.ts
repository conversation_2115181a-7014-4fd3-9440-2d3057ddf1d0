import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { Device } from 'src/modules/device/entities/device.entity';
import { ActionPolicyItem } from './action-policy-item.entity';
import { dayjs } from 'src/utils/dayjs';

@Entity('op_ads_action_log')
export class ActionLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '操作IP', default: '', nullable: true })
  ipadd: string;

  @Column({ comment: '操作结果是否成功', type: 'boolean', nullable: true })
  status: boolean;

  @ManyToOne(() => Device, { nullable: true })
  device: Device;
  @Column({ comment: '操作设备', nullable: true })
  deviceId: number;

  @Column({ comment: 'username', default: '' })
  username?: string;

  @ManyToOne(() => ActionPolicyItem, { nullable: true })
  actionItem: ActionPolicyItem;
  @Column({ comment: '操作项目ID', type: 'int', nullable: true })
  actionItemId?: number;

  @Column({ comment: '操作结果', type: 'text', nullable: true })
  actionResult: string;

  @Column({ comment: '操作详情', type: 'text', nullable: true })
  actionDetail: string;

  @CreateDateColumn()
  createTime: Date;
}

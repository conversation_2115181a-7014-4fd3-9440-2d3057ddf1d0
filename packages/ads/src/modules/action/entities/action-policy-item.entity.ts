import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import { ActionPolicy } from './action-policy.entity';
import { Device } from 'src/modules/device/entities/device.entity';

@Entity('op_ads_action_policy_item')
export class ActionPolicyItem {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => ActionPolicy, { onDelete: 'CASCADE' })
  actionPolicy: ActionPolicy;
  @Column({ comment: '策略ID' })
  actionPolicyId: number;

  @ManyToOne(() => Device, { nullable: true })
  device: Device;
  @Column({ comment: '操作设备', type: 'int', nullable: true })
  deviceId: number;

  @Column({ comment: '操作类型', default: '' })
  actionType: 'send_discord_msg' | 'send_email' | 'ssh_cmd';

  @Column({ comment: '操作脚本', type: 'text', nullable: true })
  actionScript: string;

  @Column({
    comment: '释放时间，执行操作脚本之后多长时间进行释放脚本定义，单位：分',
    default: '',
  })
  releaseTime: string;

  @Column({
    comment: 'Release Script / 释放脚本',
    type: 'text',
    nullable: true,
  })
  releaseScript: string;

  @Column({
    comment: 'SSH操作失败发送Discord消息主体',
    type: 'text',
    nullable: true,
  })
  actionErrorDiscordContent: string;

  @Column({ comment: '执行顺序', default: '' })
  seq: string;

  @Column({ comment: '操作描述', default: '' })
  description: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

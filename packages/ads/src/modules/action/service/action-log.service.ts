import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ActionLog } from '../entities/action-log.entity';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';

@Injectable()
export class ActionLogService {
  constructor(
    @InjectRepository(ActionLog)
    private readonly actionLogRepository: Repository<ActionLog>,
  ) {}

  /**
   * 分页
   */
  async find(query: Record<string, any>, option: PageOption) {
    return selectBuilder.page(query, option, this.actionLogRepository.createQueryBuilder('a'));
  }

  /**
   * 详情
   */
  async findOne(id: number) {
    return this.actionLogRepository.findOne({ where: { id } });
  }

  /**
   * 增加
   */
  async create(data: ActionLog | ActionLog[]) {
    return this.actionLogRepository.insert(data);
  }

  /**
   * 修改
   */
  async update(id: number, data: ActionLog) {
    return this.actionLogRepository.update(id, data);
  }

  /**
   * 删除
   */
  async remove(id: number | number[]) {
    return this.actionLogRepository.delete(id);
  }
}

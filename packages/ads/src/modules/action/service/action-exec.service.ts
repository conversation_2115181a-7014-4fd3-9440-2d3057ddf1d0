import { Injectable } from '@nestjs/common';
import { isEmpty, isExist } from 'src/utils/types';
import { AbnormalHistoryService } from 'src/modules/abnormal/service';
import { ActionCommonService } from 'src/common/service/action.service';
import { DataSource } from 'typeorm';
import { AbnormalHistory, AbnormalHistoryStatusEnum } from 'src/modules/abnormal/entities';
import { formatValues } from 'src/modules/analyze/utils/filter';
import { chain } from 'lodash';

/**
 * ActionExecService：执行 Action 服务，由 Analyze 服务调用或 task 调用
 */
@Injectable()
export class ActionExecService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly abnormalService: AbnormalHistoryService,
    private readonly actionCommonService: ActionCommonService,
  ) {}

  /**
   * 执行异常IP操作
   * - 获取异常IP
   * - 为Abnormal对象绑定操作
   * - 获取 allSSHActions、allDeviceIds、allDiscordActions、allEmailActions
   * -
   * @returns
   */
  async startAction() {
    // 获取未处理异常历史记录
    const abnormals = await this.abnormalService.getAbnormal();
    if (isEmpty(abnormals)) return; // 异常IP个数为零，返回

    // 调用异常处理函数
    const abnormalExecuteResult = await this.actionCommonService.executeAbnormalList(
      abnormals.map((abnormal) => ({
        id: abnormal.id,
        ip: abnormal.ipadd,
        actionId: abnormal.actionPolicyId,
        analyzeId: abnormal.analyzePolicyId,
        termId: abnormal.termId,
        term: abnormal.triggeredTermDetail || {},
        values: abnormal.values,
        isGroup: !!abnormal.isGroup,
        children: isExist(abnormal.children)
          ? abnormal.children.map((a) => {
              return {
                id: a.id,
                ip: a.ipadd,
                actionId: a.actionPolicyId,
                analyzeId: a.analyzePolicyId,
                termId: a.termId,
                values: formatValues(abnormal.values),
              };
            })
          : null,
      })),
    );

    // 处理成功的异常
    const executeSuccess = abnormalExecuteResult.filter((result: any) => result.isOk || result.isOk === undefined);

    // 处理失败的异常
    const executeError = abnormalExecuteResult.filter((result: any) => result.isOk === false);

    // 更新异常状态
    if (isExist(executeSuccess)) {
      const ids = chain(executeSuccess)
        .map((a) => [a.id, a.children?.map((c) => c.id)])
        .flattenDeep()
        .value();
      if (isExist(ids)) {
        this.dataSource
          .createQueryBuilder()
          .update(AbnormalHistory)
          .set({ status: AbnormalHistoryStatusEnum.SUCCESSFULLY })
          .where('id IN (:...ids)', { ids })
          .execute();
      }
    }
    if (isExist(executeError)) {
      const ids = chain(executeError)
        .map((a) => [a.id, a.children?.map((c) => c.id)])
        .flattenDeep()
        .value();
      if (isExist(ids)) {
        this.dataSource
          .createQueryBuilder()
          .update(AbnormalHistory)
          .set({ status: AbnormalHistoryStatusEnum.FAILED })
          .where('id IN (:...ids)', { ids })
          .execute();
      }
    }
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { uniq } from 'lodash';
import { isEmpty } from 'src/utils/types';
import { ActionPolicyItem } from '../entities/action-policy-item.entity';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import { getDifferences } from 'src/utils/differences';

@Injectable()
export class ActionItemService {
  constructor(
    @InjectRepository(ActionPolicyItem)
    private readonly actionItemRepository: Repository<ActionPolicyItem>,
  ) {}

  async find(query: Record<string, any>, option: PageOption) {
    return selectBuilder.page(query, option, this.actionItemRepository.createQueryBuilder('a'));
  }

  // 根据actionId获取actionItems
  async getActionItemsByActionId(actionId: number | number[]) {
    const actionIds = Array.isArray(actionId) ? uniq(actionId) : [actionId];
    return this.actionItemRepository.find({
      where: { actionPolicyId: In(actionIds) },
      relations: ['device'],
    });
  }

  async saveByActionId(items: any[], actionId: number) {
    items = items.map((i) => ({
      ...i,
      actionPolicyId: actionId,
      deviceId: isEmpty(i.deviceId) ? null : i.deviceId,
    }));

    const oldItems = await this.actionItemRepository.find({
      where: { actionPolicyId: actionId },
    });

    const result: Record<string, any> = {};
    const { aItems, uItems, dKeys } = getDifferences(items, oldItems);

    if (aItems.length > 0) {
      result.add = await this.actionItemRepository.save(aItems);
    }

    if (uItems.length > 0) {
      result.update = await this.actionItemRepository.save(uItems);
    }

    if (dKeys.length > 0) {
      result.delete = await this.actionItemRepository.delete(dKeys);
    }

    return result;
  }

  async findOne(id: number) {
    return this.actionItemRepository.findOne({ where: { id } });
  }

  async create(data: any) {
    return this.actionItemRepository.insert(data);
  }

  async update(id: number, data: any) {
    return this.actionItemRepository.update(id, data);
  }

  async remove(id: number | number[]) {
    return this.actionItemRepository.delete(id);
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PageOption, defineOption, selectBuilder } from 'src/utils/orm/builder';
import { ActionPolicy } from '../entities/action-policy.entity';
import { ActionPolicyItem } from '../entities/action-policy-item.entity';
import { ActionItemService } from './action-item.service';
import { replacePlaceholders } from '../utils/replace-text';

@Injectable()
export class ActionService {
  constructor(
    @InjectRepository(ActionPolicy)
    private readonly actionRepository: Repository<ActionPolicy>,

    @InjectRepository(ActionPolicyItem)
    private readonly actionItemRepository: Repository<ActionPolicyItem>,
    private readonly actionItemService: ActionItemService,
  ) {}

  // 返回策略ssh操作内容
  async getPreview(ip: string, actionId: number) {
    const actionItems = await this.actionItemRepository.find({
      select: ['actionScript', 'device'],
      relations: ['device'],
      where: { actionPolicyId: actionId, actionType: 'ssh_cmd' },
    });

    const message = actionItems
      .map((item) => {
        const cmd = ip
          .split(',')
          .map((ip) => {
            const [address] = ip.split('/');
            return replacePlaceholders(item.actionScript, {
              targetIp: address,
            });
          })
          .join('\n');

        return `Router Device: ${item.device?.name}\n${cmd}`;
      })
      .join('\n\n');

    return { code: 1000, message };
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async page(query: any, option: PageOption) {
    return selectBuilder.page(query, option, this.actionRepository.createQueryBuilder('a'));
  }

  async create(data) {
    const savedAction = await this.save(data);
    return {
      id: savedAction.id,
    };
  }

  async update(id: number, data: any) {
    await this.save(data);
  }

  /**
   * 如果存在，更新数据，不存在则增加数据
   */
  async save(data: any) {
    const { actionItems: items = [], ...action } = data;

    const savedAction = await this.actionRepository.save(action);

    if (items.length > 0) {
      await this.actionItemService.saveByActionId(items, savedAction.id);
    }

    savedAction.actionItems = await this.actionItemRepository.find({
      where: { actionPolicyId: savedAction.id },
    });

    return savedAction;
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async delete(id: number) {
    await this.actionItemRepository.delete({ actionPolicyId: id });
    return this.actionRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async info(id: number) {
    return this.actionRepository.findOne({
      where: { id },
      relations: ['actionItems'],
    });
  }
}

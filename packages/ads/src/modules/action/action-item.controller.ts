import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ActionItemService } from './service/action-item.service';
import { defineOption } from 'src/utils/orm/builder';
import { Device } from '../device/entities/device.entity';

@Controller('action/item')
export class ActionItemController {
  private readonly pageQueryOp = defineOption({
    select: ['a.*', 'b.name deviceName'],
    fieldEq: [
      { column: 'a.actionPolicyId', requestParam: 'actionPolicyId' },
      { column: 'a.actionType', requestParam: 'actionType' },
    ],
    keyWordLikeFields: ['a.actionScript', 'a.description'],
    join: [
      {
        entity: Device,
        alias: 'b',
        condition: 'a.deviceId = b.id',
        type: 'leftJoin',
      },
    ],
  });

  constructor(private readonly actionItemService: ActionItemService) {}

  @Post('page')
  async page(@Body() query: any) {
    return this.actionItemService.find(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data: any) {
    return this.actionItemService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.actionItemService.update(data.id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids: any) {
    return this.actionItemService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.actionItemService.findOne(id);
  }
}

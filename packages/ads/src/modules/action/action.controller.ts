import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ActionService } from './service/action.service';
import { defineOption } from 'src/utils/orm/builder';
import { ActionExecService } from './service/action-exec.service';

@Controller('action')
export class ActionController {
  constructor(
    private readonly actionService: ActionService,
    private readonly actionExecService: ActionExecService,
  ) {}

  pageQueryOp = defineOption({
    select: ['a.*'],
    fieldLike: [{ column: 'a.name', requestParam: 'name' }],
    fieldEq: [{ column: 'a.id', requestParam: 'id' }],
    keyWordLikeFields: ['a.name', 'a.description'],
  });

  // 操作
  @Post('test')
  async test() {
    return this.actionExecService.startAction();
  }

  @Get('preview/cmd')
  async preview(@Query('ip') ip: string, @Query('actionId') actionId: number) {
    return this.actionService.getPreview(ip, actionId);
  }

  @Post('page')
  async page(@Body() query) {
    return this.actionService.page(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() params) {
    return this.actionService.create(params);
  }

  @Post('update')
  async update(@Body() params) {
    return this.actionService.update(params.id, params);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return this.actionService.delete(ids);
  }

  @Get('info')
  async info(@Query('id') id) {
    return this.actionService.info(id);
  }
}

import Handlebars from 'src/utils/handlebars';
import { isString, merge } from 'lodash';
import { dayjs } from 'src/utils/dayjs';

type DataSource = {
  targetIp?: string;
  targetSubnet?: string;
  targetSubnetMask?: string;
  currentDateTime?: string;
  currentDate?: string;
  values?: Record<string, string | number>;
  term?: Record<string, any>;
  action?: {
    from?: string;
    to?: string;
    result?: string;
    detail?: string;
  };
  device?: {
    id?: number;
    name: string;
  };
};

export function defineDataSource(dataSource: DataSource) {
  return dataSource;
}

/**
 * 对文本中的特殊字符进行替换
 * - 替换为当前日期时：\<currentDateTime\>
 * - 替换为当前日期：\<currentDate\>
 * - ...
 * @param text 文本
 * @returns {String} 替换后的文本
 */
export function replacePlaceholders(text: string, dataSource?: DataSource): string {
  if (!isString(text)) {
    return text;
  }

  dataSource = merge(
    {},
    {
      currentDateTime: dayjs().format('MM-DD_HH:mm'),
      currentDate: dayjs().format('MM-DD'),
      action: {
        from: dayjs().format('MM/DD HH:mm'),
      },
    } as DataSource,
    dataSource,
  );

  const template = Handlebars.compile(text);
  const output = template(dataSource);

  return output;
}

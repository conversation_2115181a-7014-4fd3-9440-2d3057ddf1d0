import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import dayjs from '@/utils/dayjs';
import { logger } from '@/utils/logger';
import { ActionExecService } from '../service/action-exec.service';
import { DataSource } from 'typeorm';
import { ActionLog } from '../entities/action-log.entity';

@Injectable()
export class ActionScheduleService {
  constructor(
    private readonly actionExecService: ActionExecService,
    private readonly dataSource: DataSource,
  ) {}

  // 每5秒执行异常IP操作
  @Cron('*/5 * * * * *')
  async action() {
    try {
      await this.actionExecService.startAction();
    } catch (error) {
      logger.error(`执行异常IP操作失败，错误信息：${error.message}`);
    }
  }

  // 删除旧数据
  // 7 Days
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async deleteOldData() {
    const dateAgo = dayjs().subtract(14, 'days').toDate();

    try {
      await this.dataSource
        .createQueryBuilder()
        .delete()
        .from(ActionLog)
        .where('createTime < :dateAgo', { dateAgo })
        .execute();
    } catch (error) {
      logger.error(`操作日志删除失败，错误信息：${error.message}`);
    }
  }
}

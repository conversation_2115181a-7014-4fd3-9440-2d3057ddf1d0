import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ActionLogService } from './service/action-log.service';
import { defineOption } from 'src/utils/orm/builder';
import { Device } from '../device/entities/device.entity';

@Controller('action/log')
export class ActionLogController {
  private readonly pageQueryOp = defineOption({
    select: ['a.*', 'b.name deviceName'],
    fieldEq: [{ column: 'a.status', requestParam: 'status' }],
    fieldLike: [{ column: 'a.ipadd', requestParam: 'ipadd' }],
    join: [
      {
        entity: Device,
        alias: 'b',
        condition: 'a.deviceId = b.id',
        type: 'leftJoin',
      },
    ],
  });

  constructor(private readonly actionLogService: ActionLogService) {}

  @Post('page')
  async page(@Body() query: any) {
    return this.actionLogService.find(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data: any) {
    return this.actionLogService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.actionLogService.update(data.id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids: any) {
    return this.actionLogService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.actionLogService.findOne(id);
  }
}

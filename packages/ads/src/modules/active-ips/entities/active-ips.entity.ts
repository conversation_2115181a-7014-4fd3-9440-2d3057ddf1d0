import { Entity, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn, Column, Index } from 'typeorm';

@Entity('op_ads_active_ips')
export class ActiveIPs {
  @PrimaryGeneratedColumn()
  id: number;

  @Index({ unique: true })
  @Column({ comment: 'IP字符串', type: 'varchar', length: 15 })
  ipAddress: string;

  @Index({ unique: true })
  @Column({ comment: '转换为十进制后的值', type: 'bigint' })
  ipAddressNum: string;

  @Index()
  @Column({ comment: 'IP段字符串', type: 'varchar', length: 15 })
  ipSubnet: string;

  @Index()
  @Column({
    comment: '状态: Active / Inactive',
    type: 'enum',
    enum: ['Active', 'Inactive'],
    default: 'Active',
  })
  status: 'Active' | 'Inactive';

  @Column({ comment: '首次活动时间', type: 'timestamp', nullable: true })
  firstActiveTime: Date;

  @Index()
  @Column({ comment: '最近一次活动时间', type: 'timestamp', nullable: true })
  lastActiveTime: Date;

  @Index()
  @Column({ comment: '最近一次活动来源设备', default: '' })
  lastActiveSourceDevice: string;

  @Index()
  @Column({ comment: '最近一次活动来源机房', default: '' })
  lastActiveSourceLocation: string;

  @Index()
  @Column({ comment: '每日活跃计数', type: 'int', default: 0 })
  dailyActiveCount: number;

  @Column({ comment: 'Tags', default: '' })
  tags: string;

  @CreateDateColumn({ comment: '创建时间' })
  createTime: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime: Date;
}

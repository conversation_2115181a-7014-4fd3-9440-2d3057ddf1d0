import { BadRequestException, Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

import * as ip from 'ip';
import { chain } from 'lodash';
import { SelectQueryBuilder, DataSource, In, Repository } from 'typeorm';

import { dayjs } from '@/utils/dayjs';

import { logger } from '@/utils/logger';
import { toSubnetAddress } from '@/utils/ip';
import { isArray, isEmpty } from '@/utils/types';
import { PageOption, selectBuilder } from '@/utils/orm/builder';
import { ConfService } from '@/modules/conf/conf.service';
import { IPAddress } from '@/modules/sync-ops/entities/ip';
import { IPSubnet } from '@/modules/sync-ops/entities/subnet';

import { ActiveIPs } from '../entities/active-ips.entity';

@Injectable()
export class ActiveIPsService implements OnModuleInit {
  minDailyActiveCountKey = 'activeIps.minDailyActiveCount';

  constructor(
    @InjectRepository(ActiveIPs)
    private readonly activeIPsRepository: Repository<ActiveIPs>,

    private readonly confService: ConfService,

    private readonly dataSource: DataSource,
  ) {}

  async onModuleInit() {
    const confRow = await this.confService.find({ key: this.minDailyActiveCountKey });
    if (isEmpty(confRow)) await this.confService.create({ key: this.minDailyActiveCountKey, value: '0' });
  }

  // async onModuleInit() {
  //   const start = czDayjs.current('YYYY-MM-DD HH:mm:ss.SSS');

  //   const list = await this.dataSource.createQueryBuilder().select('*').from(ActiveIPs, 'a').getRawMany();
  //   const chunkSize = 1000;
  //   const chunks = chunk(list, chunkSize);

  //   try {
  //     await Promise.all(
  //       chunks.map(async (chunkItem) => {
  //         const updateQueries = chunkItem
  //           .map(() => `UPDATE op_ads_active_ips SET description = ? WHERE id = ?;`)
  //           .join(' ');

  //         const params = chain(chunkItem)
  //           .map((item) => ['192.168.00', item.id])
  //           .flatten()
  //           .value();
  //         try {
  //           await this.dataSource.query(updateQueries, params);
  //         } catch (error) {
  //           console.error(error);
  //         }
  //       }),
  //     );
  //   } catch (error) {
  //     console.error(error);
  //   }

  //   const end = czDayjs.current('YYYY-MM-DD HH:mm:ss.SSS');
  //   const diff = dayjs(end).diff(dayjs(start), 's');
  //   console.log(`${diff}s`);
  // }

  /**
   * 处理推送的IP
   * @param ipAddressList
   */
  async handlePushActiveIPs({ ipAddressList, srcLocation, srcDevice }) {
    if (isEmpty(ipAddressList) || !isArray(ipAddressList)) return;

    // 查找数据库中已存在的 IP 地址记录
    const activeIPs = await this.activeIPsRepository.find({
      where: { ipAddress: In(ipAddressList) },
    });

    // 将数据库中的 activeIPs 转换为一个 Map，减少后续查找的时间复杂度
    const activeIPsMap = new Map(activeIPs.map((item) => [item.ipAddress, item]));

    // 格式化 IP 地址列表，分为已存在和不存在的 IP 地址
    const existedIpAddressList: ActiveIPs[] = [];
    const notExistedIpAddressList: ActiveIPs[] = [];

    ipAddressList.forEach((ipAddress) => {
      const existedIpAddress = activeIPsMap.get(ipAddress) || ({} as ActiveIPs);

      const formattedIp = {
        id: existedIpAddress.id || null,
        ipAddress,
        ipSubnet: toSubnetAddress(ipAddress, false),
        ipAddressNum: existedIpAddress.ipAddressNum || ip.toLong(ipAddress).toString(),
        firstActiveTime: existedIpAddress.firstActiveTime || dayjs().toDate(),
        lastActiveTime: dayjs().toDate(),
        lastActiveSourceDevice: srcDevice || '',
        lastActiveSourceLocation: srcLocation || '',
        dailyActiveCount: 1,
        status: 'Active',
      } as ActiveIPs;

      if (existedIpAddress.id) {
        existedIpAddressList.push(formattedIp);
      } else {
        notExistedIpAddressList.push(formattedIp);
      }
    });

    // 使用事务批量更新已存在的 IP 地址
    if (existedIpAddressList.length > 0) {
      await this.activeIPsRepository.manager.transaction(async (runner) => {
        // 批量更新已存在的 IP 地址记录
        const updatePromises = existedIpAddressList.map((ipAddress) =>
          runner.update(ActiveIPs, ipAddress.id, { ...ipAddress, dailyActiveCount: () => 'dailyActiveCount + 1' }),
        );
        await Promise.all(updatePromises);
      });
    }

    // 批量插入新的 IP 地址记录
    if (notExistedIpAddressList.length > 0) {
      await this.activeIPsRepository.createQueryBuilder().insert().orIgnore().values(notExistedIpAddressList).execute();
    }
  }

  async getSummaryData(query: Record<string, any>) {
    const builder = this.activeIPsRepository.createQueryBuilder('a');
    const minDailyActiveCount = await this.getMinDailyActiveCount();

    const { subnetLike, ownerLike } = query;
    let sql = '';
    let parameters = null;

    const options: PageOption = {
      select: [
        'a.ipSubnet ipSubnet',
        's.owner owner',
        'COUNT(CASE WHEN status = "Active" THEN 1 END) AS count',
        `COUNT(CASE WHEN (i.customerNo IS NULL OR i.customerNo = '') AND (s.customerNo IS NULL OR s.customerNo = '') AND a.status = "Active" THEN 1 END) AS unknown`,
      ],
      join: [
        {
          type: 'leftJoin',
          entity: IPAddress,
          alias: 'i',
          condition: 'i.ipAddress = a.ipAddress',
        },
        {
          type: 'leftJoin',
          entity: IPSubnet,
          alias: 's',
          condition: 's.subnet = a.ipSubnet',
        },
      ],
      extend: (builder: SelectQueryBuilder<IPAddress>) => {
        if ((subnetLike as string)?.toString().trim().length > 0) {
          builder.andWhere('a.ipSubnet LIKE :subnetLike', {
            subnetLike: `${subnetLike}%`,
          });
        }
        if ((ownerLike as string)?.toString().trim().length > 0) {
          builder.andWhere('s.owner LIKE :ownerLike', {
            ownerLike: `%${ownerLike}%`,
          });
        }

        builder.andWhere('a.dailyActiveCount >= :minDailyActiveCount', {
          minDailyActiveCount: minDailyActiveCount || 0,
        });

        builder.groupBy('a.ipSubnet');
        sql = builder.getQuery();
        parameters = builder.getParameters();
      },
    };

    const result = await selectBuilder.page(query, options, builder, false);

    if (sql.trim().length > 0 && parameters) {
      try {
        const [totalResult] = await this.dataSource
          .createQueryBuilder()
          .select('count(*) total')
          .from(`(${sql})`, 'a')
          .setParameters(parameters)
          .getRawMany();

        result.pagination.total = +totalResult.total;
      } catch {}
    }

    return result;
  }

  async setMinDailyActiveCount(minDailyActiveCount: number) {
    return this.confService.setValue(this.minDailyActiveCountKey, minDailyActiveCount.toString());
  }

  async getMinDailyActiveCount() {
    const minDailyActiveCount = await this.confService.getValue(this.minDailyActiveCountKey);
    return minDailyActiveCount ? +minDailyActiveCount : 0;
  }

  /**
   * 任务：每小时执行一次修改status为Inactive
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cronInactive() {
    const time = dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss');

    try {
      await this.activeIPsRepository.manager.transaction(async (runner) => {
        return runner
          .createQueryBuilder()
          .update(ActiveIPs)
          .set({ status: 'Inactive' })
          .where('lastActiveTime < :lastActiveTime', { lastActiveTime: time })
          .execute();
      });
    } catch (error) {
      logger.error(`修改Active IP状态异常，错误信息：${error.message}`);
    }
  }

  /**
   * 任务：每天执行一次删除
   */
  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async cronRemove() {
    const time = dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');

    try {
      await this.activeIPsRepository.manager.transaction(async (runner) => {
        return runner
          .createQueryBuilder()
          .delete()
          .from(ActiveIPs)
          .where('lastActiveTime < :lastActiveTime', { lastActiveTime: time })
          .execute();
      });
    } catch (error) {
      logger.error(`删除Active IP项目异常，错误信息：${error.message}`);
    }
  }

  /**
   * 任务：每天0点执行一次重置活跃计数
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cronResetDailyActiveCount() {
    await this.dataSource.query(`UPDATE op_ads_active_ips set dailyActiveCount = 0;`);
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    const minDailyActiveCount = await this.getMinDailyActiveCount();

    if (query.order === 'description') query.order = 'b.description';

    const builder = this.activeIPsRepository
      .createQueryBuilder('a')
      .andWhere('a.dailyActiveCount >= :minDailyActiveCount', {
        minDailyActiveCount: minDailyActiveCount ? +minDailyActiveCount : 0,
      });

    return selectBuilder.page(query, option, builder);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.activeIPsRepository.findOne({ where: { id } });
  }

  async getColumnList(column: string) {
    const result = await this.activeIPsRepository.createQueryBuilder().select(column).groupBy(column).getRawMany();

    return chain(result)
      .map((i) => i[column])
      .filter(Boolean)
      .uniq()
      .value();
  }

  async handlePatchTags(id: number, tags: string) {
    const item = await this.activeIPsRepository.findOne({
      where: { id },
    });

    if (!item) throw new BadRequestException('Active IP not exist');
    await this.activeIPsRepository.update(id, { tags });
  }
}

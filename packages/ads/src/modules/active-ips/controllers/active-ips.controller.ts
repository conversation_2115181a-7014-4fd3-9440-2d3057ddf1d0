import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';

import { Brackets, SelectQueryBuilder } from 'typeorm';
import { isArray, isEmpty } from '@/utils/types';
import { PageOption } from '@/utils/orm/builder';

import { IPAddress } from '@/modules/sync-ops/entities/ip';
import { IPSubnet } from '@/modules/sync-ops/entities/subnet';

import { ActiveIPsService } from '../services/active-ips.service';
import { ActiveIPs } from '../entities/active-ips.entity';

@Controller('activeIps')
export class ActiveIpsController {
  constructor(private readonly activeIPsService: ActiveIPsService) {}

  pageQueryOp(query): PageOption {
    return {
      select: [
        'a.*',
        'b.description AS description',
        `COALESCE(NULLIF(b.customerNo, ''),NULLIF(c.customerNo, '')) AS customerNo`,
        `COALESCE(NULLIF(b.serviceNo, ''),NULLIF(c.serviceNo, '')) AS serviceNo`,
      ],
      keyWordLikeFields: ['b.customerNo', 'c.customerNo', 'b.description', 'b.serviceNo', 'c.serviceNo'],
      fieldEq: ['a.status', 'a.ipSubnet', 'a.lastActiveSourceDevice', 'lastActiveSourceLocation'],
      join: [
        {
          type: 'leftJoin',
          alias: 'b',
          condition: 'a.ipAddressNum = b.ipAddressNum',
          entity: IPAddress,
        },
        {
          type: 'leftJoin',
          alias: 'c',
          condition: 'a.ipSubnet = c.subnet',
          entity: IPSubnet,
        },
      ],
      extend: (builder: SelectQueryBuilder<ActiveIPs>) => {
        if (query.noExistCustomer) {
          builder.andWhere(`COALESCE(NULLIF(b.customerNo, ''), NULLIF(c.customerNo, '')) IS NULL`);
        }

        if (query.noExistService) {
          builder.andWhere(`COALESCE(NULLIF(b.serviceNo, ''), NULLIF(c.serviceNo, '')) IS NULL`);
        }

        if (query.ipAddressLike) {
          builder.andWhere('a.ipAddress like :ipAddressLike', {
            ipAddressLike: `${query.ipAddressLike}%`,
          });
        }

        if (query.customerNoLike) {
          builder.andWhere(
            new Brackets((subQuery) => {
              subQuery
                .where('b.customerNo like :customerNoLike', {
                  customerNoLike: `%${query.customerNoLike}%`,
                })
                .orWhere('c.customerNo like :customerNoLike', {
                  customerNoLike: `%${query.customerNoLike}%`,
                });
            }),
          );
        }

        if (query.serviceNoLike) {
          builder.andWhere(
            new Brackets((subQuery) => {
              subQuery
                .where('b.serviceNo like :serviceNoLike', {
                  serviceNoLike: `%${query.serviceNoLike}%`,
                })
                .orWhere('c.serviceNo like :serviceNoLike', {
                  serviceNoLike: `%${query.serviceNoLike}%`,
                });
            }),
          );
        }

        if (query.customerNo) {
          const customerNo = isArray(query.customerNo) ? query.customerNo : [query.customerNo];

          builder.andWhere(
            new Brackets((subQuery) => {
              subQuery
                .where('b.customerNo IN (:...customerNo)', { customerNo })
                .orWhere('c.customerNo IN (:...customerNo)', { customerNo });
            }),
          );
        }

        if (query.serviceNo) {
          const serviceNo = isArray(query.serviceNo) ? query.serviceNo : [query.serviceNo];

          builder.andWhere(
            new Brackets((subQuery) => {
              subQuery
                .where('b.serviceNo IN (:...serviceNo)', { serviceNo })
                .orWhere('c.serviceNo IN (:...serviceNo)', { serviceNo });
            }),
          );
        }

        if (!query.order && !query.sort) {
          builder.orderBy('a.lastActiveTime', 'DESC');
        }
      },
      fieldBetween: ['a.firstActiveTime', 'a.lastActiveTime', 'a.createTime'],
    };
  }

  @Get('minDailyActiveCount')
  async getMinDailyActiveCount() {
    return this.activeIPsService.getMinDailyActiveCount();
  }

  @Post('minDailyActiveCount')
  async setMinDailyActiveCount(@Body('minDailyActiveCount') minDailyActiveCount) {
    return this.activeIPsService.setMinDailyActiveCount(minDailyActiveCount);
  }

  @Post('push')
  async onPushActiveIPs(@Body() body: any) {
    if (isEmpty(body.ipAddressList) || !isArray(body.ipAddressList)) return;
    const { ipAddressList, srcLocation, srcDevice } = body;

    try {
      await this.activeIPsService.handlePushActiveIPs({
        ipAddressList,
        srcLocation,
        srcDevice,
      });
    } catch (error) {
      console.error(error.message);
    }
  }

  @Get()
  async find(@Query() query: any) {
    return this.activeIPsService.find(query, this.pageQueryOp(query));
  }

  @Get('summary')
  async summary(@Query() query) {
    return this.activeIPsService.getSummaryData(query);
  }

  @Patch(':id')
  async patchTags(@Param('id') id: string, @Body('tags') tags: string | string[] = '') {
    tags = isArray(tags) ? tags.join(',') : tags;
    return this.activeIPsService.handlePatchTags(+id, tags);
  }

  @Get('location')
  async getLocationList() {
    return this.activeIPsService.getColumnList('lastActiveSourceLocation');
  }

  @Get('device')
  async getDeviceList() {
    return this.activeIPsService.getColumnList('lastActiveSourceDevice');
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.activeIPsService.findOne(id);
  }
}

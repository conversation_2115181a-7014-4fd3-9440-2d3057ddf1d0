import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ActiveIPs } from './entities/active-ips.entity';
import { ActiveIpsController } from './controllers/active-ips.controller';
import { ActiveIPsService } from './services/active-ips.service';
import { ConfModule } from '../conf/conf.module';

@Module({
  imports: [TypeOrmModule.forFeature([ActiveIPs]), ConfModule],
  controllers: [ActiveIpsController],
  providers: [ActiveIPsService],
  exports: [],
})
export class ActiveIPsModule {}

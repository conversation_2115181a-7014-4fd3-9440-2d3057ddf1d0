import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DataSource } from 'typeorm';
import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';
import { AbnormalHistory } from '../entities';

@Injectable()
export class AbnormalScheduleService {
  constructor(private readonly dataSource: DataSource) {}

  // 每天执行一次删除7天前的组分析异常记录
  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async deleteOldData() {
    const dayAgo = dayjs().subtract(7, 'days').format('YYYY-MM-DD');

    try {
      await this.dataSource
        .createQueryBuilder()
        .delete()
        .from(AbnormalHistory)
        .where('createTime < :date', { date: dayAgo })
        .execute();
    } catch (error) {
      logger.error(`删除组分析历史记录失败，错误信息：${error.message}`);
    }
  }
}

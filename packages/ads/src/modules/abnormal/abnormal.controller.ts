import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { AbnormalHistoryService } from './service/abnormal-history.service';
import { defineOption } from 'src/utils/orm/builder';
import { ActionPolicy } from '../action/entities/action-policy.entity';
import { AnalyzePolicy } from '../analyze/entities/analyze-policy.entity';
import { AnalyzeTerm } from '../analyze/entities/analyze-term.entity';

@Controller('abnormal')
export class AbnormalController {
  private readonly pageQueryOp = defineOption({
    select: ['a.*', 'b.name actionPolicyName', 'c.name analyzePolicyName', 'd.seq termSeq', 'd.message termMessage'],
    keyWordLikeFields: ['a.ipadd'],
    fieldEq: [{ column: 'a.status', requestParam: 'status' }],
    join: [
      {
        entity: ActionPolicy,
        alias: 'b',
        type: 'leftJoin',
        condition: 'a.actionPolicyId = b.id',
      },
      {
        entity: AnalyzePolicy,
        alias: 'c',
        type: 'leftJoin',
        condition: 'a.analyzePolicyId = c.id',
      },
      {
        entity: AnalyzeTerm,
        alias: 'd',
        type: 'leftJoin',
        condition: 'a.termId = d.id',
      },
    ],
    extend: (builder) => {
      return builder.andWhere('a.parentId IS NULL');
    },
  });

  constructor(private readonly abnormalHistoryService: AbnormalHistoryService) {}

  @Post('page')
  async page(@Body() query) {
    return this.abnormalHistoryService.find(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() data: any) {
    return this.abnormalHistoryService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.abnormalHistoryService.update(data.id, data);
  }

  @Post('delete')
  async delete(@Body('ids') ids: any) {
    return this.abnormalHistoryService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.abnormalHistoryService.findOne(id);
  }
}

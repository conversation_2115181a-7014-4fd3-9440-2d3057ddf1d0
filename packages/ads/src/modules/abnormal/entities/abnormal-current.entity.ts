import { ActionPolicy } from 'src/modules/action/entities/action-policy.entity';
import { Device } from 'src/modules/device/entities/device.entity';
import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum AbnormalCurrentStatusEnum {
  PENDING = 'pending',
  FAILED = 'failed',
  SUCCESSFULLY = 'successfully',
}

@Entity('op_ads_abnormal_current')
export class AbnormalCurrent {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '父级ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ comment: 'IP Address' })
  ipadd: string;

  @Column({ comment: '当前异常是否为组', type: 'boolean', default: false })
  isGroup: boolean;

  @Column({ comment: '上游', default: '' })
  upstream: string;

  @Column({ comment: 'Release Date Time', type: 'int', nullable: true })
  releaseMinuteDuration: number;

  @Column({ comment: 'Release Date Time', type: 'datetime', nullable: true })
  releaseDateTime: Date | string;

  @Column({
    comment: 'Status',
    default: AbnormalCurrentStatusEnum.PENDING,
  })
  status: AbnormalCurrentStatusEnum;

  @ManyToOne(() => Device, { nullable: true })
  device: Device;
  @Column({ comment: 'Device ID', nullable: true })
  deviceId: number;

  @Column({ comment: '阈值', type: 'json', nullable: true })
  term: any;

  @Column({ comment: '分析数据依据', type: 'json', nullable: true })
  values: any;

  @Column({ comment: 'Release Script', default: '' })
  releaseScript: string;

  @ManyToOne(() => ActionPolicy, { nullable: true })
  actionPolicy: ActionPolicy;
  @Column({ comment: '操作策略', nullable: true })
  actionPolicyId: number;

  @Column({ comment: '分析策略', nullable: true })
  analyzePolicyId: number;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export enum AbnormalHistoryStatusEnum {
  PENDING = 'pending',
  PROCESSED = 'processed',
  FAILED = 'failed',
  SUCCESSFULLY = 'successfully',
}

@Entity('op_ads_abnormal_history')
export class AbnormalHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: 'IP Address' })
  ipadd: string;

  @Column({
    comment: '异常处理状态',
    default: AbnormalHistoryStatusEnum.PENDING,
  })
  status: AbnormalHistoryStatusEnum;

  @Column({ comment: '是否是组异常条目', type: 'boolean', default: false })
  isGroup: boolean;

  @Column({ comment: '是否是组异常条目', type: 'int', nullable: true })
  parentId: number | null;

  @Column({ comment: '当前异常项目匹配的Term ID', type: 'int', nullable: true })
  termId: number;

  @Column({ comment: '上游', default: '' })
  upstream: string;

  @Column({ comment: '行为策略ID', nullable: true })
  actionPolicyId: number;

  @Column({ comment: '分析策略ID', nullable: true })
  analyzePolicyId: number;

  @Column({ comment: '触发值', type: 'json', nullable: true })
  values: any;

  @Column({ comment: '触发的分析策略Term详情', type: 'json', nullable: true })
  triggeredTermDetail: any;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

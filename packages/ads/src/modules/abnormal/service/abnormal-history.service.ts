import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import { chain, differenceBy, isUndefined } from 'lodash';
import {
  AbnormalCurrent,
  AbnormalHistory,
  AbnormalCurrentStatusEnum,
  AbnormalHistoryStatusEnum,
} from 'src/modules/abnormal/entities';
import { Conf } from '../../conf/entities/conf.entity';
import { AnalyzeTermItem } from 'src/modules/analyze/entities';
import { isEmpty, isExist } from 'src/utils/types';
import { formatTerm } from 'src/modules/analyze/utils/filter';

/**
 * AbnormalService：异常服务
 * 1. 返回异常IP记录
 * 2. 基本CRUD
 */
@Injectable()
export class AbnormalHistoryService {
  constructor(
    @InjectRepository(AbnormalHistory)
    private readonly abnormalHistoryRepository: Repository<AbnormalHistory>,

    // Tracking Repository
    @InjectRepository(AbnormalCurrent)
    private readonly abnormalCurrentRepository: Repository<AbnormalCurrent>,

    private readonly dataSource: DataSource,
  ) {}

  async isExecAction() {
    return this.dataSource.transaction(async (runner) => {
      const confItem = await runner.findOne(Conf, {
        where: { key: 'maxExecAbnormalId' },
      });

      if (!confItem?.value) return;

      const { value: maxExecAbnormalId } = confItem;

      const { maxId: maxAbnormalId } = await runner
        .createQueryBuilder()
        .select('MAX(a.id) maxId')
        .from(AbnormalHistory, 'a')
        .getRawOne();

      const isLteMaxExecAbnormalId = maxAbnormalId <= maxExecAbnormalId;
      if (isLteMaxExecAbnormalId) return;

      await runner
        .createQueryBuilder()
        .update(Conf)
        .set({ value: maxAbnormalId })
        .where('key = "maxExecAbnormalId"')
        .execute();

      return {
        maxExecAbnormalId,
        maxAbnormalId,
      };
    });
  }

  /**
   * 返回异常IP记录
   * new_abnormal_id > proceeded_abnormal_id && not in ip_tracking list
   */
  async getAbnormal() {
    const result = await this.isExecAction();
    if (isUndefined(result)) return;

    const { maxExecAbnormalId, maxAbnormalId } = result;

    // 获取异常
    // > maxExecAbnormalId
    // termId/actionPolicyId IS NOT NULL
    // parentId IS NULL
    // status: pending
    let abnormals = await this.dataSource
      .createQueryBuilder()
      .select(['a.*', 'a.actionPolicyId actionId'])
      .from(AbnormalHistory, 'a')
      .where('a.id > :maxExecAbnormalId', { maxExecAbnormalId })
      .andWhere('a.id <= :maxAbnormalId', { maxAbnormalId })
      .andWhere('a.termId IS NOT NULL')
      .andWhere('a.actionPolicyId IS NOT NULL')
      .andWhere('a.parentId IS NULL')
      .andWhere('a.status = :status', {
        status: AbnormalHistoryStatusEnum.PENDING,
      })
      .getRawMany();

    if (isEmpty(abnormals)) return;

    // 获取异常的 ipadd （IP、groupItemName）
    const abnormalHistoryIps = chain(abnormals)
      .map((i) => i.ipadd)
      .uniq()
      .value();

    // 获取当前异常
    // IN abnormalHistoryIps
    const trackingList = await this.getTrackingByIps(abnormalHistoryIps);

    // 获取异常组历史记录ID
    // isGroup === 1
    const groupIds = abnormals.filter((a) => a.isGroup).map((a) => a.id);

    // 获取异常组异常历史项目
    if (isExist(groupIds)) {
      const children = await this.dataSource
        .createQueryBuilder()
        .select()
        .from(AbnormalHistory, 'a')
        .where('a.parentId IN (:...pid)', { pid: groupIds })
        .getRawMany();

      abnormals = abnormals.map((a) => {
        if (!a.isGroup) return a;
        a.children = children.filter((c) => c.parentId === a.id);
        return a;
      });
    }

    // 获取当前所有的异常历史ID，包括异常历史组项目
    const allIds = chain(abnormals)
      .map((a) => [a.id, isExist(a.children) ? a.children?.map((c) => c.id) : undefined])
      .flattenDeep()
      .filter(Boolean)
      .value();

    // 修改状态，后续执行操作完成会修改为失败或者成功
    await this.abnormalHistoryRepository
      .createQueryBuilder()
      .update()
      .set({ status: AbnormalHistoryStatusEnum.PROCESSED })
      .where('id IN (:...ids)', { ids: allIds })
      .execute();

    // 获取不在当前异常的异常历史IP，进行去重，只处理第一次告警
    const abnormalList = differenceBy(abnormals, trackingList, (item) => {
      return `${item.ipadd}-${item.analyzePolicyId}`;
    });

    return chain(abnormalList)
      .groupBy('ipadd')
      .map((v) => v[0])
      .value();
  }

  async createHistoryAndChildren(abnormal) {
    const { children, ...abnormalRest } = abnormal;
    const { raw } = await this.abnormalHistoryRepository.insert(abnormalRest);
    await this.abnormalHistoryRepository.insert(children.map((i) => ({ ...i, parentId: raw.insertId })));
  }

  async getTrackingByIps(ips: string[]) {
    if (isEmpty(ips)) return [];

    return this.abnormalCurrentRepository
      .createQueryBuilder()
      .select(['ipadd', 'analyzePolicyId'])
      .where('ipadd IN (:...ips)', { ips })
      .andWhere('status = :status', {
        status: AbnormalCurrentStatusEnum.PENDING,
      })
      .getRawMany();
  }

  getInTrackingAbnormalList(abnormalList: AbnormalHistory[], trackingList: AbnormalCurrent[]) {
    return abnormalList.filter((abnormal) => trackingList.some((tracking) => tracking.ipadd === abnormal.ipadd));
  }

  async find(query: Record<string, any>, option: PageOption) {
    const { list, pagination } = await selectBuilder.page(
      query,
      option,
      this.abnormalHistoryRepository.createQueryBuilder('a'),
    );

    const termIds = list.map((i) => i.termId).filter((termId) => isExist(termId));
    const parentIds = list.map((a) => a.id);

    const [termItems, children] = await Promise.all([
      isExist(termIds)
        ? this.dataSource
            .createQueryBuilder()
            .select()
            .from(AnalyzeTermItem, 'a')
            .where('a.termId IN (:...termIds)', { termIds })
            .getRawMany()
        : [],

      isExist(parentIds)
        ? this.dataSource
            .createQueryBuilder()
            .select()
            .from(AbnormalHistory, 'a')
            .where('a.parentId IN (:...ids)', { ids: parentIds })
            .getRawMany()
        : [],
    ]);

    return {
      list: chain(list).map((abnormal) => {
        return {
          ...abnormal,
          children: children.filter((child) => child.parentId === abnormal.id),
          term: formatTerm(termItems.filter((item) => item.termId === abnormal.termId)),
        };
      }),
      pagination,
    };
  }

  async findOne(id: number) {
    return this.abnormalHistoryRepository.findOne({ where: { id } });
  }

  async create(data: any) {
    return this.abnormalHistoryRepository.insert(data);
  }

  async update(id: number, data: any) {
    return this.abnormalHistoryRepository.update(id, data);
  }

  async remove(id: number | number[]) {
    return this.abnormalHistoryRepository.delete(id);
  }

  async save(data: any) {
    return this.abnormalHistoryRepository.save(data);
  }
}

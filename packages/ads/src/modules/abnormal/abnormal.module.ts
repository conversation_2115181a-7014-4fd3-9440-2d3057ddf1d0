import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AbnormalController } from './abnormal.controller';
import { AbnormalHistoryService } from './service/abnormal-history.service';
import { ConfModule } from '../conf/conf.module';
import { ReleaseModule } from '../release/release.module';
import { RedisModule } from 'src/base';
import { Conf } from '../conf/entities/conf.entity';
import { AbnormalHistory, AbnormalCurrent } from './entities';
import { AbnormalScheduleService } from './schedule/abnormal.schedule';

@Module({
  imports: [TypeOrmModule.forFeature([Conf, AbnormalHistory, AbnormalCurrent]), RedisModule, ConfModule, ReleaseModule],
  controllers: [AbnormalController],
  providers: [AbnormalHistoryService, AbnormalScheduleService],
  exports: [AbnormalHistoryService],
})
export class AbnormalModule {}

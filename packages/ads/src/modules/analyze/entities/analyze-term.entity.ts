import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToOne,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { ActionPolicy } from 'src/modules/action/entities/action-policy.entity';
import { AnalyzePolicy } from './analyze-policy.entity';
import { AnalyzeTermItem } from './analyze-term-item.entity';

@Entity('op_ads_analyze_term')
export class AnalyzeTerm {
  @PrimaryGeneratedColumn()
  id: number;

  @OneToOne(() => ActionPolicy, { onDelete: 'SET NULL' })
  actionPolicy: ActionPolicy;
  @Column({ comment: '操作策略ID', nullable: true })
  actionPolicyId: number;

  @ManyToOne(() => AnalyzePolicy)
  analyzePolicy: AnalyzePolicy;
  @Column({ comment: '策略ID' })
  analyzePolicyId: number;

  @OneToMany(() => AnalyzeTermItem, (termItem) => termItem.term)
  termItems: AnalyzeTermItem[];

  @Column({ comment: '处理序号, 值越小越优先执行' })
  seq: number;

  @Column({ comment: '策略描述', type: 'varchar', default: '', nullable: true })
  message: string;

  @Column({ comment: '显示等级', type: 'varchar', default: '', nullable: true })
  severity: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

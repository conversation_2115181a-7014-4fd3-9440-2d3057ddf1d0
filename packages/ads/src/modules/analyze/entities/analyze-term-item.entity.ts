import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { AnalyzeTerm } from './analyze-term.entity';

@Entity('op_ads_analyze_term_item')
export class AnalyzeTermItem {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '父级ID', type: 'int', nullable: true })
  parentId: number;

  @ManyToOne(() => AnalyzeTerm, { onDelete: 'CASCADE' })
  term: AnalyzeTerm;
  @Column({ comment: 'Term ID' })
  termId: number;

  @Column({ type: 'varchar', default: '', comment: '匹配字段' })
  classifyField: string;

  @Column({ comment: '区间最小值', default: '' })
  minValue: string;

  @Column({ comment: '区间最大值', default: '' })
  maxValue: string;

  @Column({ comment: '匹配字段包含值', default: '', nullable: true })
  stringValue: string;

  @Column({ comment: '时间段开始时间', default: '' })
  start: string;

  @Column({ comment: '时间段结束时间', default: '' })
  end: string;

  @Column({ comment: '时间段结束时间', type: 'int', default: 0 })
  seq: number;

  @Column({
    comment: `
    策略类型。可选值说明：
    - between: 区间
    - include: 包含
    - exclude: 排除
    - gt: 大于
    - gte: 大于等于
    - eq: 等于
    - lt: 小于
    - lte: 小于等于
    - filter: 过滤
    - increaseRate: 增加比例
    - decreaseRate: 减少比例
    - increaseNum: 增加比例
    - decreaseNum: 减少比例
  `,
    default: '',
  })
  classifyMethod:
    | 'between'
    | 'include'
    | 'includes'
    | 'exclude'
    | 'excludes'
    | 'gt'
    | 'gte'
    | 'eq'
    | 'lt'
    | 'lte'
    | 'filter'
    | 'increaseRate'
    | 'decreaseRate'
    | 'increaseNum'
    | 'decreaseNum';

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

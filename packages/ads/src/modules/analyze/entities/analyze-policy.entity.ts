import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { AnalyzeTerm } from './analyze-term.entity';
import { AnalyzePolicyFilterList } from './analyze-policy-filter-list.entity';

export enum ApplicableScopeEnum {
  NFDUMP = 'nfdump',
  ATTACK_OVERVIEW = 'attack_overview',
}

@Entity('op_ads_analyze_policy')
export class AnalyzePolicy {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '是否启用', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ comment: '是否是组策略', type: 'boolean', default: false })
  isGroup: boolean;

  @Column({ comment: '时间范围，单位：秒', type: 'int', default: 60 })
  duration: number;

  @OneToMany(() => AnalyzeTerm, (term) => term.analyzePolicy)
  terms: AnalyzeTerm[];

  @Column({ comment: '策略名称' })
  name: string;

  @Column({ comment: '适用范围', type: 'json', nullable: true })
  applicableScope: string[] | null;

  @Column({ comment: '分组类型', default: '' })
  groupType: string;

  @Column({ comment: '策略绑定 flow 类型', default: 'IP' })
  type: 'IP' | 'Subnet';

  @Column({ comment: '创建用户', default: '' })
  createBy: string;

  @Column({ comment: '更新用户', default: '' })
  updateBy: string;

  @ManyToMany(() => AnalyzePolicyFilterList, (filterList) => filterList.analyzeGroup)
  @JoinTable({
    name: 'op_ads_analyze_filter_list_mapping',
    joinColumn: { name: 'analyzeId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'filterListId', referencedColumnName: 'id' },
  })
  filterList: AnalyzePolicyFilterList[];

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

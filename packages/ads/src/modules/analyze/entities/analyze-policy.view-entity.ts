import { DataSource, ViewEntity } from 'typeorm';
import { AnalyzeTerm } from './analyze-term.entity';
import { AnalyzeTermItem } from './analyze-term-item.entity';
import { AnalyzePolicy } from './analyze-policy.entity';

@ViewEntity({
  name: 'view_ads_analyze_policy',
  expression: (dataSource: DataSource) =>
    dataSource
      .createQueryBuilder()
      .select('p.id', 'analyzeId')
      .addSelect('p.name', 'analyzeName')
      .addSelect('p.type', 'analyzeType')
      .addSelect('p.createBy', 'createBy')
      .addSelect('p.createTime', 'createAt')
      .addSelect('p.updateBy', 'updateBy')
      .addSelect('p.updateTime', 'updateAt')
      .addSelect('t.id', 'termId')
      .addSelect('t.actionPolicyId', 'termActionId')
      .addSelect('t.seq', 'termSeq')
      .addSelect('t.message', 'termMessage')
      .addSelect('ti.id', 'termItemId')
      .addSelect('ti.classifyField', 'field')
      .addSelect('ti.classifyMethod', 'method')
      .addSelect('ti.minValue', 'min')
      .addSelect('ti.maxValue', 'max')
      .addSelect('ti.stringValue', 'str')
      .from(AnalyzeTermItem, 'ti')
      .leftJoin(AnalyzeTerm, 't', 't.id = ti.termId')
      .leftJoin(AnalyzePolicy, 'p', 'p.id = t.analyzePolicyId'),
})
export class ViewAnalyzePolicy {}

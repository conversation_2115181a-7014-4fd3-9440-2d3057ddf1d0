import { Column, CreateDateColumn, Entity, Index, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';

@Entity('op_ads_analyze_log')
export class AnalyzeLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ comment: '父级ID', type: 'int', nullable: true })
  parentId: number;

  @Column({ comment: '匹配的TermID', type: 'int', nullable: true })
  termId: number;

  @Column({ comment: '匹配的Term对应的ActionID', type: 'int', nullable: true })
  actionId: number;

  @Index()
  @Column({ comment: '分析ID', type: 'int', nullable: true })
  analyzeId: number;

  @Index()
  @Column({ comment: '分析项目名称, IP/Subnet/GroupItemName', default: '' })
  itemName: string;

  @Index()
  @PrimaryColumn({ comment: '开始分析的时间', type: 'datetime' })
  start: Date;

  @Index()
  @Column({
    comment: '结束分析的时间',
    type: 'datetime',
    default: () => 'NOW()',
  })
  end: Date;

  @Column({ comment: '匹配项目值的详情', type: 'json', nullable: true })
  values: any;

  @Column({ comment: '匹配的Term详情', type: 'json', nullable: true })
  term: any;

  @Column({ comment: '是否匹配Term', type: 'boolean', default: false })
  isMatch: boolean;

  @Column({ comment: '是否是一个组项目', type: 'boolean', default: false })
  isGroup: boolean;

  @CreateDateColumn()
  createTime: Date;
}

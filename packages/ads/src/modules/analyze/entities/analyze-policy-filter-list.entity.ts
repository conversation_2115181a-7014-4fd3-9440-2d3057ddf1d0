import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn, ManyToMany } from 'typeorm';
import { AnalyzePolicy } from './analyze-policy.entity';

export enum FilterListTypeEnum {
  CUSTOMER = 'customer',
  LOCATION = 'location',
  ASN = 'originAsn',
  SUBNET = 'subnet',
  IP = 'ipadd',
  UPSTREAM = 'upstream',
  ACL = 'acl',
}

export enum FilterListActionEnum {
  PERMIT = 'permit',
  DENY = 'deny',
}

@Entity('op_ads_analyze_filter_list')
export class AnalyzePolicyFilterList {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ comment: '名称', default: '' })
  name: string;

  @Column({ comment: '描述', default: '' })
  description: string;

  @Column({ comment: '操作', type: 'enum', enum: ['permit', 'deny'] })
  action: FilterListActionEnum;

  @Column({ comment: '类型：customer、location、asn...', default: '' })
  type: FilterListTypeEnum;

  @Column({ comment: '过滤值', type: 'text', nullable: true })
  value: string;

  @ManyToMany(() => AnalyzePolicy, (group) => group.filterList)
  analyzeGroup: AnalyzePolicy[];

  @Column({ comment: '创建用户', default: '' })
  createBy: string;

  @Column({ comment: '更新用户', default: '' })
  updateBy: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

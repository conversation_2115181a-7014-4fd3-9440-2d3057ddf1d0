import { parentPort } from 'worker_threads';
import bigUtils from 'src/utils/big';
import { forEach, isArray, maxBy } from 'lodash';
import { isEmpty } from 'src/utils/types';
import { getBestTermItems } from '../utils/filterTerms';
import { dayjs } from 'src/utils/dayjs';

function isNumberClassifyMethod(method: string) {
  return ['between', 'gte', 'gt', 'eq', 'lte', 'lt'].includes(method);
}

/**
 * 返回匹配值
 * @param field
 * @param flows
 * @param duration
 * @returns
 */
function getMatchValue(method: string, key: string, source: any) {
  if (isEmpty(source)) return; // 源数据为空，直接返回
  if (!isArray(source)) return source?.[key]; // 源数据为对象，直接返回key的值
  if (isNumberClassifyMethod(method)) return bigUtils.reduce(source, key); // 如果方法时数字匹配的方法，则返回总和
  return source[0][key]; // 返回第一个对象的key
}

// 过滤源数据
function filterSource(source: any[], termItems: any[]) {
  if (!isArray(source)) {
    // console.log('IsArray', isArray(source));
    // console.log(source);
    return source;
  }

  return termItems.reduce((acc, item) => {
    const { classifyField: field, stringValue: str } = item;
    return acc?.filter((flow) => str.split(',').includes(flow?.[field]));
  }, source);
}

/**
 * 匹配 IP Flow 和 Terms
 * @param terms 当前 IP 配置的分析策略下的所有 terms
 * @param flows 当前 IP 的所有流量数据（5m）
 * @returns Null | Term
 */
export function getMatchTerm(terms: any[], flows: any[]) {
  if (!terms.length || !flows.length) return;

  // Find Terms
  // 处理策略 Or 逻辑
  // 如果有一个 Term 匹配成功，则不继续进行匹配下一个 Term
  return terms
    .filter((term) => !isEmpty(term?.termItems))
    .map((term) => {
      // 获取当前时间段的termItem
      term.termItems = getBestTermItems(term.termItems, 'classifyField');
      return term;
    })
    .find((term) => {
      // 1. 获取 flow 数据源
      // 1.1 从 TermItems 中获取 filter 项
      const filterTermItems = term.termItems.filter((item) => {
        return item.classifyMethod === 'filter';
      });

      // 1.2 去除 filter 项并获取最匹配的termItems，时间段
      const termItems = term.termItems.filter((item) => {
        return item.classifyMethod !== 'filter';
      });

      // 2. 过滤流量数据
      if (filterTermItems.length > 0) {
        flows = filterSource(flows, filterTermItems);
      }

      // Every: 遍历当前 termItems
      // 处理策略项目中 And 逻辑
      // 如果有一个 TermItem 匹配失败，则不继续进行匹配下一个 TermItem
      // 当匹配失败，进入下一个 Term 匹配

      let values: Record<string, any> = {};

      // 时间范围过滤
      if (isEmpty(term.sampleDuration)) {
        const lastFlow = maxBy(flows, 'id');
        flows = [lastFlow];
        values = { ...lastFlow };
      } else {
        flows = flows
          .filter((flow) => {
            if (isEmpty(flow)) return;
            return dayjs(flow.createTime).isAfter(dayjs().subtract(term.sampleDuration, 'm'));
          })
          .filter((v) => !isEmpty(v));
      }

      const matchTerm = termItems.every((item) => {
        const { classifyMethod: method, classifyField: key, stringValue: str = '' } = item;

        let { minValue: min = 0, maxValue: max = 0 } = item;

        switch (method) {
          case 'between':
            min = bigUtils.convertMetricSuffix(min);
            max = bigUtils.convertMetricSuffix(max);
            break;
          case 'gte':
          case 'gt':
          case 'eq':
          case 'lte':
          case 'lt':
            min = bigUtils.convertMetricSuffix(min);
        }

        // 获取匹配值
        // 如果存在 duration，则使用 field 的总和
        // 不存在 duration，则使用最新的Flow数据中的 field 的值
        const value = getMatchValue(method, key, flows);

        if (isNumberClassifyMethod(method)) {
          values[key] = bigUtils.convertToMetricSuffix(value);
        } else {
          values[key] = value;
        }

        // 匹配逻辑
        switch (method) {
          case 'between':
            return +value >= +min && +value <= +max;
          case 'gte':
            return +value >= +min;
          case 'gt':
            return +value > +min;
          case 'eq':
            return +value === +min;
          case 'lte':
            return +value <= +min;
          case 'lt':
            return +value < +min;
          case 'include':
            return str
              .split(',')
              .map((i) => i.trim())
              .includes(value);
          case 'exclude':
            return !str
              .split(',')
              .map((i) => i.trim())
              .includes(value);
          default:
            return false;
        }
      });

      if (matchTerm) {
        forEach(filterTermItems, (item) => {
          values[item.classifyField] = item.stringValue;
        });
        term.values = values;
      }

      return matchTerm;
    });
}

parentPort.on('message', (results: any[]) => {
  parentPort.postMessage(results);
});

import { Modu<PERSON> } from '@nestjs/common';

import { RedisModule } from 'src/base';
import { FlowModule } from 'src/modules/flow/flow.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AnalyzePolicy } from './entities/analyze-policy.entity';
import { AnalyzeTerm } from './entities/analyze-term.entity';
import { AnalyzeTermItem } from './entities/analyze-term-item.entity';
import { AnalyzeService } from './service/analyze.service';
import { AnalyzeTermService } from './service/analyze-term.service';
import { AnalyzeTermItemService } from './service/analyze-term-item.service';
import { AnalyzeController } from './controller/analyze.controller';
import { AnalyzeIPFlowService } from './service/analyze-ip-flow.service';
import { AnalyzeSubnetFlowService } from './service/analyze-subnet-flow.service';
import { ActionModule } from '../action/action.module';
import { AnalyzeScheduleService } from './schedule/analyze.schedule';
import { ConfModule } from '../conf/conf.module';
import { AbnormalModule } from '../abnormal/abnormal.module';
import { IpConfigModule } from '../ip-config/ip-config.module';
import { ViewAnalyzePolicy } from './entities/analyze-policy.view-entity';
import { CommonModule } from 'src/common/common.module';
import { IPHistory } from '../flow/entities/ip-history.entity';
import { SubnetHistory } from '../flow/entities/subnet-history.entity';
import { AnalyzePolicyFilterList } from './entities/analyze-policy-filter-list.entity';
import { AnalyzeLog } from './entities/analyze-log.entity';
import { AnalyzeLogService } from './service/analyze-log.service';
import { AnalyzeLogScheduleService } from './schedule/analyze-log.schedule';
import { AnalyzeLogController } from './controller/analyze-log.controller';
import { AnalyzeTermItemController } from './controller/analyze-term-item.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AnalyzePolicy,
      AnalyzeTerm,
      AnalyzeTermItem,
      ViewAnalyzePolicy,
      IPHistory,
      SubnetHistory,
      AnalyzePolicyFilterList,
      AnalyzeLog,
    ]),
    RedisModule,
    CommonModule,
    ConfModule,
    IpConfigModule,
    ActionModule,
    FlowModule,
    AbnormalModule,
  ],
  controllers: [AnalyzeController, AnalyzeLogController, AnalyzeTermItemController],
  providers: [
    AnalyzeScheduleService,
    AnalyzeLogScheduleService,
    AnalyzeService,
    AnalyzeTermService,
    AnalyzeTermItemService,
    AnalyzeIPFlowService,
    AnalyzeSubnetFlowService,
    AnalyzeLogService,
  ],
  exports: [AnalyzeService, AnalyzeIPFlowService, AnalyzeSubnetFlowService],
})
export class AnalyzeModule {}

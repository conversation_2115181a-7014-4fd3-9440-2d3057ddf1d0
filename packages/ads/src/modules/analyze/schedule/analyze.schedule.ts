import { Injectable } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { logger } from '@/utils/logger';
import { AnalyzeIPFlowService } from '../service/analyze-ip-flow.service';

@Injectable()
export class AnalyzeScheduleService {
  constructor(private readonly analyzeIPService: AnalyzeIPFlowService) {}

  // 每五秒执行一次IP流量分析
  @Cron('*/5 * * * * *')
  async analyzeIP() {
    try {
      await this.analyzeIPService.startAnalyze();
    } catch (error) {
      logger.error(`分析IP异常，错误信息：${error.message}`);
    }
  }
}

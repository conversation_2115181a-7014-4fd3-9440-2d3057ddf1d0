import bigUtils from 'src/utils/big';
import { forEach } from 'lodash';
import { isEmpty } from 'src/utils/types';
import { getBestTermItems } from './filterTerms';

/**
 * 匹配 IP Flow 和 Terms
 * @param terms 当前 IP 配置的分析策略下的所有 terms
 * @param flows 当前 IP 的所有流量数据（5m）
 * @returns Null | Term
 */
export function getMatchTerm(terms: any[], flow: Record<string, any>) {
  if (isEmpty(terms)) return;

  // Find Terms
  // 处理策略 Or 逻辑
  // 如果有一个 Term 匹配成功，则不继续进行匹配下一个 Term
  return terms
    .filter((term) => !isEmpty(term?.termItems))
    .map((term) => {
      // 获取当前时间段的termItem
      term.termItems = getBestTermItems(term.termItems, 'classifyField');
      return term;
    })
    .find((term) => {
      const values: Record<string, any> = { ...flow };
      const matchTerm = term.termItems.every((item) => {
        const { classifyMethod: method, classifyField: key, stringValue: str = '' } = item;

        let { minValue: min = 0, maxValue: max = 0 } = item;

        switch (method) {
          case 'between':
            min = bigUtils.convertMetricSuffix(min);
            max = bigUtils.convertMetricSuffix(max);
            break;
          case 'gte':
          case 'gt':
          case 'eq':
          case 'lte':
          case 'lt':
            min = bigUtils.convertMetricSuffix(min);
        }

        // 获取匹配值
        const value = flow[key];

        if (isNumberClassifyMethod(method)) {
          values[key] = bigUtils.convertToMetricSuffix(value);
        } else {
          values[key] = value;
        }

        // 匹配逻辑
        switch (method) {
          case 'between':
            return +value >= +min && +value <= +max;
          case 'gte':
            return +value >= +min;
          case 'gt':
            return +value > +min;
          case 'eq':
            return +value === +min;
          case 'lte':
            return +value <= +min;
          case 'lt':
            return +value < +min;
          case 'includes':
          case 'filter':
            return str
              .split(',')
              .map((i) => i.trim())
              .includes(value);
          case 'excludes':
            return !str
              .split(',')
              .map((i) => i.trim())
              .includes(value);
          default:
            return false;
        }
      });

      if (matchTerm) {
        term.values = values;
      }

      return matchTerm;
    });
}

function isNumberClassifyMethod(method: string) {
  return ['between', 'gte', 'gt', 'eq', 'lte', 'lt'].includes(method);
}

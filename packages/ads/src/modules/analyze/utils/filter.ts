/**
 * 过滤信息详情，主要获取termItems中字段及判断逻辑，最大值、最小值、字符串值等，并获取匹配信息
 */

import { chain } from 'lodash';
import { bigUtils } from '@/utils/big';
import { isEmpty } from '@/utils/types';

const suffixKeys = ['bps', 'pps', 'bytes'];

export function formatTerm(termItems) {
  if (isEmpty(termItems)) return;

  return chain(termItems)
    .map((item) => {
      const {
        classifyField: key,
        classifyMethod: method,
        minValue: min,
        maxValue: max,
        stringValue: str,
        duration,
        start,
        end,
      } = item;

      switch (method) {
        case 'between':
          return {
            key,
            duration,
            method,
            min,
            max,
            start,
            end,
          };
        case 'gte':
        case 'gt':
        case 'eq':
        case 'lte':
        case 'lt':
          return {
            key,
            duration,
            method,
            min,
            start,
            end,
          };
        case 'include':
        case 'exclude':
        case 'filter':
          return {
            key,
            duration,
            method,
            str,
            start,
            end,
          };
        default:
          return {
            key,
            duration,
            method,
            min,
            max,
            start,
            end,
            str,
          };
      }
    })
    .groupBy('key')
    .mapValues((items) => items[0])
    .value();
}

const defaultPick = [
  'customer',
  'location',
  'originAsn',
  'productName',
  'subnet',
  'ipadd',
  'bps',
  'pps',
  'bytes',
  'addressCount',
  'abnormalCount',
  'matchItemCount',
];
export function formatValues(values: Record<string, any>, pickProps?: string[]) {
  pickProps = defaultPick.concat(pickProps);
  return chain(values)
    .mapValues((v, k) => {
      if (!suffixKeys.includes(k)) return v;
      return bigUtils.convertToMetricSuffix(v);
    })
    .pick(pickProps)
    .value();
}

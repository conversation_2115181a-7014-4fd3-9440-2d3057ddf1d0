import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { chain, groupBy, keys } from 'lodash';
import { dayjs } from 'src/utils/dayjs';
import { IPHistoryService } from 'src/modules/flow/service/ip-history.service';
import { getMatchTerm } from 'src/modules/analyze/utils/match';
import { isEmpty, isExist } from 'src/utils/types';
import { ConfService } from 'src/modules/conf/conf.service';
import { AbnormalHistory } from 'src/modules/abnormal/entities/abnormal-history.entity';
import { AbnormalHistoryService } from 'src/modules/abnormal/service';
import { AnalyzeTermService } from './analyze-term.service';
import { formatTerm, formatValues } from '../utils/filter';
import { bigUtils } from 'src/utils/big';
import { AnalyzeLog } from '../entities/analyze-log.entity';

/**
 * AnalyzeIPFlowService：分析 IP 流量服务
 */
@Injectable()
export class AnalyzeIPFlowService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly confService: ConfService,
    private readonly abnormalService: AbnormalHistoryService,
    private readonly ipHistoryService: IPHistoryService,
    private readonly analyzeTermService: AnalyzeTermService,
  ) {}

  /**
   * 开始分析 IP 流量
   * 1. 获取已分析的最大 IP Flow History ID
   * 2. 从数据库中获取未分析的 IP Flow IPs（大于 analyzedFlowHistoryID）
   * 3. 修改已分析的最大 IP Flow History ID
   * 4. 遍历未分析的 IP Flow IPs
   *  4.1 从 Redis 中根据 IP 获取对应分析策略 Terms
   *  4.2 从 Redis 中获取 IP Flow
   *  4.3 调用 getMatchTerm 方法，匹配 IP Flow 和 Terms
   * 5. 将匹配的 IP Flow 生成 AnalyzeMark
   * 6. 调用 AnalyzeMark Service 的 create 方法，保存 AnalyzeMark
   * 7. 调用 ActionExecService 的 execAction 方法，执行动作
   * @returns
   */
  async startAnalyze() {
    // 1. 获取已分析的最大 IP Flow History ID
    const maxAnalyzedId = await this.confService.getValue('maxAnalyzedIPId');

    // 2. 获取当前最大的 IP Flow History ID
    const currentMaxId = await this.ipHistoryService.getMaxId();
    const isCurMaxIdLtMaxAnalyzedId = currentMaxId <= maxAnalyzedId;

    // 如果当前最大的 IP Flow History ID 小于等于已分析的最大 IP Flow History ID，则直接返回
    // 否则，将当前最大的 IP Flow History ID 保存到 数据库中
    if (isCurMaxIdLtMaxAnalyzedId) {
      return;
    } else {
      await this.confService.setValue('maxAnalyzedIPId', currentMaxId + '');
    }

    // 3. 获取大于 analyzedMaxId 小于等于 currentMaxId 的 IP Flow IPs
    const unanalyzedFlows = await this.ipHistoryService.getBetween(+maxAnalyzedId, +currentMaxId);

    if (unanalyzedFlows.length === 0) return;

    const analyzePolicyIds = unanalyzedFlows.map((i) => i.analyzePolicyId);
    const terms = await this.analyzeTermService.getTermsByAnalyzeId(analyzePolicyIds);
    const termGroupByAnalyzeId = groupBy(terms, 'analyzePolicyId');

    const analyzeResult = chain(unanalyzedFlows)
      .map((flow) => {
        const { id, ipadd, analyzeId, upstream } = flow;
        if (!analyzeId) return;

        const terms = termGroupByAnalyzeId[analyzeId + ''];

        // 获取流量及分析项目
        // 4.4 调用 getMatchTerm 方法，匹配 IP Flow 和 Terms
        // 如果匹配成功，则返回匹配的 Term
        const term = getMatchTerm(terms, flow);
        const isMatch = !isEmpty(term);

        return {
          id,
          term,
          flow,
          isMatch,
          upstream,
          ip: ipadd,
        };
      })
      .value();

    // 4. 遍历未分析的 IP Flows(Promises)
    const results = analyzeResult.filter((i) => !isEmpty(i) && i.isMatch);
    if (results.length === 0) return;

    // 4. 将匹配的 IP Flow 生成 AnalyzeMark
    const abnormals = chain(results)
      .map(({ ip, upstream, term, flow }) => {
        const values = chain(flow)
          .pick([
            'ipadd',
            'customer',
            'location',
            'originAsn',
            'productNo',
            'productName',
            'upstream',
            'srcDevice',
            'srcLocation',
            'duration',
            'bps',
            'pps',
            'bytes',
          ])
          .mapValues((v, k) => {
            if (!'bps,pps,bytes'.includes(k)) return v;
            return bigUtils.convertToMetricSuffix(v);
          })
          .value();

        // 生成 AnalyzeMark
        const abnormalHistory = new AbnormalHistory();
        abnormalHistory.ipadd = ip;
        abnormalHistory.termId = term.id;
        abnormalHistory.upstream = upstream;
        abnormalHistory.analyzePolicyId = term.analyzePolicyId;
        abnormalHistory.actionPolicyId = term.actionPolicyId;
        abnormalHistory.values = values;
        return abnormalHistory;
      })
      .value();

    this.dataSource.query(
      chain(results)
        .filter((i) => i.isMatch)
        .map(({ id, term }) => {
          return `UPDATE \`op_ads_ip_flow_history\` SET \`severity\` = \'${term.severity}\', \`matchTermFields\` = \'${keys(formatTerm(term?.termItems))}\' WHERE \`id\` = ${id}`;
        })
        .join(';')
        .value(),
    );

    // 6. 调用 AnalyzeMark Service 的 create 方法，保存 AnalyzeMark
    // 如果没有 AnalyzeMark，则直接返回
    if (abnormals.length !== 0) {
      this.abnormalService.create(abnormals);
    }

    // 7. 新增分析日志
  }
}

import { Injectable } from '@nestjs/common';
import { chain, groupBy, keys } from 'lodash';
import { ConfService } from 'src/modules/conf/conf.service';
import { SubnetHistoryService } from 'src/modules/flow/service/subnet-history.service';
import { isEmpty } from 'src/utils/types';
import { getMatchTerm } from '../utils/match';
import { formatTerm } from '../utils/filter';
import { AbnormalHistory } from 'src/modules/abnormal/entities/abnormal-history.entity';
import { AbnormalHistoryService } from 'src/modules/abnormal/service';
import { InjectRepository } from '@nestjs/typeorm';
import { SubnetHistory } from 'src/modules/flow/entities/subnet-history.entity';
import { Repository } from 'typeorm';
import { AnalyzeTermService } from './analyze-term.service';

@Injectable()
export class AnalyzeSubnetFlowService {
  constructor(
    @InjectRepository(SubnetHistory)
    private readonly subnetHistoryRepository: Repository<SubnetHistory>,

    private readonly confService: ConfService,
    private readonly subnetHistoryService: SubnetHistoryService,
    private readonly analyzeTermService: AnalyzeTermService,
    private readonly abnormalService: AbnormalHistoryService,
  ) {}

  /**
   * 开始分析子网流量
   * 1. 获取已分析的最大子网流量历史 ID
   * 2. 从数据库中获取未分析的子网流量 IPs（大于 analyzedFlowHistoryID）
   * 2.1 如果当前最大的子网流量历史 ID 小于等于已分析的最大子网流量历史 ID，则直接返回，否则将当前最大的子网流量历史 ID 保存到数据库中
   * 3. 修改已分析的最大子网流量历史 ID
   * 4. 遍历未分析的子网流量 IPs
   * 4.1 从 Redis 中根据 IP/prefix 获取对应分析策略 Terms
   * 4.2 从 Redis 中获取子网流量
   * 4.3 调用 getMatchTerm 方法，匹配子网流量和 Terms
   * 5. 将匹配的子网流量生成 Abnormal
   * 6. 调用 Abnormal Service 的 create 方法，保存 Abnormal
   */
  async startAnalyze() {
    // 1. 获取已分析的最大子网流量历史 ID
    const maxAnalyzedIdPromise = this.confService.getValue('maxAnalyzedSubnetId');

    // 2. 获取当前最大的子网流量历史 ID
    const currentMaxIdPromise = this.subnetHistoryService.getMaxId();

    const currentMaxId = await currentMaxIdPromise;
    const maxAnalyzedId = await maxAnalyzedIdPromise;
    const isCurMaxIdLtMaxAnalyzedId = currentMaxId <= maxAnalyzedId;

    // 2.1 如果当前最大的子网流量历史 ID 小于等于已分析的最大子网流量历史 ID，则直接返回，否则将当前最大的子网流量历史 ID 保存到数据库中
    if (isCurMaxIdLtMaxAnalyzedId) {
      return;
    } else {
      await this.confService.setValue('maxAnalyzedSubnetId', currentMaxId + '');
    }

    // 3. 获取大于 analyzedMaxId 小于等于 currentMaxId 的子网流量 IPs
    const unanalyzedFlows = await this.subnetHistoryService.getBetween(+maxAnalyzedId, currentMaxId);

    // 如果没有未分析的子网流量 IPs，则直接返回
    if (isEmpty(unanalyzedFlows)) {
      return;
    }

    const analyzePolicyIds = unanalyzedFlows.map((i) => i.analyzePolicyId);
    const terms = await this.analyzeTermService.getTermsByAnalyzeId(analyzePolicyIds);
    const termGroupByAnalyzeId = groupBy(terms, 'analyzePolicyId');

    // 4. 遍历未分析的子网流量 IPs
    const analyzePromises = chain(unanalyzedFlows)
      .map(async (flow) => {
        const { id, ip, analyzePolicyId: analyzeId } = flow;
        if (!analyzeId) return;

        // 4.1 根据分析策略 ID 获取分析策略 Terms，如果没有分析策略 Terms，则直接返回
        const terms = termGroupByAnalyzeId[analyzeId + ''];

        // 4.4 调用 getMatchTerm 方法，匹配子网流量和 Terms, 如果匹配成功，则返回匹配的 Term, 否则返回 undefined
        const term = getMatchTerm(terms, flow);
        const isMatch = !isEmpty(term);
        if (!isMatch) return;

        return {
          id,
          ip,
          term,
          values: term.values,
          isMatch,
        };
      })
      .value();

    // 5. 将匹配的子网流量生成 Abnormal
    const results = (await Promise.all(analyzePromises)).filter((i) => !isEmpty(i) && i.isMatch);

    if (results.length === 0) return;

    this.subnetHistoryRepository.manager.transaction(async (manager) => {
      const updatePromises = results.map((result) => {
        const { id, term } = result;
        const termFields = keys(formatTerm(term.termItems));
        return manager.query(
          `UPDATE \`op_ads_subnet_flow_history\` SET \`severity\` = \'${term.severity}\', \`matchTermFields\` = \'${termFields}\' WHERE \`id\` = ${id}`,
        );
      });

      await Promise.all(updatePromises);
    });

    const abnormals = chain(results)
      .map(({ ip, term, values }) => {
        const abnormalHistory = new AbnormalHistory();
        abnormalHistory.ipadd = ip;
        abnormalHistory.actionPolicyId = term.actionPolicyId;
        abnormalHistory.termId = term.id;
        abnormalHistory.analyzePolicyId = term.analyzePolicyId;
        abnormalHistory.values = values;
        return abnormalHistory;
      })
      .value();

    // 6. 保存 Abnormal
    if (isEmpty(abnormals)) return;
    await this.abnormalService.save(abnormals);
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { groupBy } from 'lodash';
import { selectBuilder, PageOption } from 'src/utils/orm/builder';
import { AnalyzeTermItem } from '../entities/analyze-term-item.entity';

import { getDifferences } from 'src/utils/differences';

@Injectable()
export class AnalyzeTermItemService {
  constructor(
    @InjectRepository(AnalyzeTermItem)
    private readonly analyzeTermItemRepository: Repository<AnalyzeTermItem>,
  ) {}

  async saveByTermId(items: AnalyzeTermItem[], termId: number) {
    const oldItems = await this.analyzeTermItemRepository.find({
      where: { termId },
    });

    const { aItems, uItems, dKeys } = getDifferences(items, oldItems);
    const addItems = aItems.map((item) => ({ ...item, termId }));
    const updateItems = uItems.map((item) => ({ ...item, termId }));

    const result: Record<string, any> = {};

    if (aItems.length) {
      aItems.forEach((item) => {
        item.termId = termId;
      });
      result.addItems = await this.analyzeTermItemRepository.save(addItems);
    }

    if (uItems.length) {
      result.updateItems = await this.analyzeTermItemRepository.save(updateItems);
    }

    if (dKeys.length) {
      result.deleteIds = await this.analyzeTermItemRepository.delete(dKeys);
    }

    return result;
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    const { list, pagination } = await selectBuilder.page(
      query,
      option,
      this.analyzeTermItemRepository.createQueryBuilder('a'),
    );

    const children = await this.analyzeTermItemRepository.find({
      where: {
        parentId: In(list.map((item) => item.id)),
      },
    });
    const groupChildren = groupBy(children, 'parentId');

    const items = list
      .sort((a, b) => a['termSeq'] - b['termSeq'])
      .map((item) => {
        const childrenList = groupChildren[String(item.id)] || [];
        return {
          ...item,
          children: childrenList.sort((a, b) => a.seq - b.seq),
        };
      });

    return {
      pagination,
      list: items,
    };
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AnalyzeTermItem) {
    return this.analyzeTermItemRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: AnalyzeTermItem) {
    await this.analyzeTermItemRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.analyzeTermItemRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.analyzeTermItemRepository.findOne({ where: { id } });
  }
}

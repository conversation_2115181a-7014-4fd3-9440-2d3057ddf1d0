import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { uniq } from 'lodash';
import { AnalyzeTerm } from '../entities/analyze-term.entity';
import { getDifferences } from 'src/utils/differences';
// import { AnalyzePolicy } from '../entities';

@Injectable()
export class AnalyzeTermService {
  constructor(
    @InjectRepository(AnalyzeTerm)
    private readonly analyzeTermRepository: Repository<AnalyzeTerm>,
  ) {}

  async saveByAnalyzeId(terms: AnalyzeTerm[], analyzeId: number) {
    const oldTerms = await this.analyzeTermRepository.find({
      where: { analyzePolicyId: analyzeId },
    });

    const { aItems, uItems, dKeys } = getDifferences(terms, oldTerms);

    const result: Record<string, any> = {};

    if (aItems.length) {
      result.addItems = await this.analyzeTermRepository.save(aItems);
    }

    if (uItems.length) {
      result.updateItems = await this.analyzeTermRepository.save(uItems);
    }

    if (dKeys.length) {
      result.deleteIds = await this.analyzeTermRepository.delete(dKeys);
    }

    return result;
  }

  async getTermsByAnalyzeId(analyzeId: number | number[]) {
    const analyzeIds = Array.isArray(analyzeId) ? uniq(analyzeId) : [analyzeId];

    // return this.analyzeTermRepository
    //   .createQueryBuilder('a')
    //   .select('a.*')
    //   .leftJoin(AnalyzePolicy, 'b', 'b.id = a.analyzePolicyId')
    //   .where('a.analyzePolicyId IN (:...analyzeIds)', analyzeIds)
    //   .andWhere('b.isActive = :isActive', { isActive: true })
    //   .getRawMany();

    return this.analyzeTermRepository.find({
      relations: ['termItems'],
      where: { analyzePolicyId: In(analyzeIds) },
    });
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any) {
    return this.analyzeTermRepository.find({ where: query });
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AnalyzeTerm) {
    return this.analyzeTermRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: AnalyzeTerm) {
    await this.analyzeTermRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.analyzeTermRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.analyzeTermRepository.findOne({ where: { id } });
  }
}

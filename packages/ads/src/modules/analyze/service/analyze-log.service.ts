import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isArray, omit } from 'lodash';
import { AnalyzeLog } from 'src/modules/analyze/entities/analyze-log.entity';
import { isExist } from 'src/utils/types';
import { DataSource, Repository, Brackets } from 'typeorm';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import dayjs from 'src/utils/dayjs';
import { AnalyzePolicy } from '../entities';
import { ActionPolicy } from 'src/modules/action/entities/action-policy.entity';

@Injectable()
export class AnalyzeLogService {
  constructor(
    @InjectRepository(AnalyzeLog)
    private readonly analyzeLogRepository: Repository<AnalyzeLog>,

    private readonly dataSource: DataSource,
  ) {}

  /**
   * 事务创建父子级分析日志
   */
  async createAnalyzeLogs(logs: (AnalyzeLog & { children?: AnalyzeLog[] })[]) {
    try {
      await this.dataSource.transaction((runner) => {
        return Promise.all(
          logs.map(async (log) => {
            const children = log.children;
            const { raw } = await runner.insert(AnalyzeLog, omit(log, 'children'));

            if (isExist(children)) {
              await runner.insert(
                AnalyzeLog,
                children.map((item) => {
                  item.parentId = raw.insertId;
                  return item;
                }),
              );
            }
          }),
        );
      });
    } catch (err) {
      console.error(err.message);
    }
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    option = {
      ...option,
      extend: (builder) => {
        builder.andWhere('a.parentId IS NULL');

        if (query.itemNameLike) {
          builder.andWhere('a.itemName LIKE :itemNameLike', {
            itemNameLike: `${query.itemNameLike}%`,
          });
        }

        if (query.between && isArray(query.between) && query.between.length === 2) {
          const start = dayjs(query.between[0]).format('YYYY-MM-DD HH:mm:ss');
          const end = dayjs(query.between[1]).format('YYYY-MM-DD HH:mm:ss');
          builder.andWhere('a.start > :start AND a.start <= :end', {
            start,
            end,
          });
        }
      },
    } as PageOption;

    const { list, pagination } = await selectBuilder.page(
      query,
      option,
      this.analyzeLogRepository.createQueryBuilder('a'),
      false,
    );

    const ids = list.map((item) => item.id);
    const analyzeIds = list.filter((i) => i.analyzeId).map((i) => i.analyzeId);
    const actionIds = list.filter((i) => i.actionId).map((i) => i.actionId);

    const [children, analyzes, actions] = await Promise.all([
      isExist(ids)
        ? this.analyzeLogRepository
            .createQueryBuilder('a')
            .select('a.*')
            .where('a.parentId IN (:...ids)', { ids })
            .getRawMany()
        : [],
      isExist(analyzeIds)
        ? this.dataSource
            .createQueryBuilder()
            .select('a.*')
            .from(AnalyzePolicy, 'a')
            .where('a.id IN (:...analyzeIds)', { analyzeIds })
            .getRawMany()
        : [],
      isExist(actionIds)
        ? this.dataSource
            .createQueryBuilder()
            .select('a.*')
            .from(ActionPolicy, 'a')
            .where('a.id IN (:...actionIds)', { actionIds })
            .getRawMany()
        : [],
    ]);

    return {
      list: list.map((item: any) => {
        item.children = children.filter((c) => c.parentId === item.id);
        item.analyzeName = analyzes.find((i) => i.id === item.analyzeId)?.name;
        item.actionName = actions.find((i) => i.id === item.actionId)?.name;
        return item;
      }),
      pagination,
    };
  }

  /**
   * 新增
   * @param data
   */
  async create(data: AnalyzeLog) {
    return this.analyzeLogRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: AnalyzeLog) {
    await this.analyzeLogRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.analyzeLogRepository.delete(id);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async clear() {
    await this.analyzeLogRepository.clear();
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.analyzeLogRepository.findOne({ where: { id } });
  }
}

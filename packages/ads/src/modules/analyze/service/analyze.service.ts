import { FindOptionsWhere, In, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { selectBuilder, PageOption } from 'src/utils/orm/builder';
import { AnalyzePolicy } from '../entities/analyze-policy.entity';
import { AnalyzeTerm } from '../entities/analyze-term.entity';
import { chain, omit } from 'lodash';
import { getDifferences } from 'src/utils/differences';
import { AnalyzeTermItemService } from './analyze-term-item.service';
import { isExist } from 'src/utils/types';
import { AnalyzeTermItem } from '../entities';

@Injectable()
export class AnalyzeService {
  constructor(
    @InjectRepository(AnalyzePolicy)
    private readonly analyzePolicyRepository: Repository<AnalyzePolicy>,

    @InjectRepository(AnalyzeTerm)
    private readonly analyzeTermRepository: Repository<AnalyzeTerm>,

    @InjectRepository(AnalyzeTermItem)
    private readonly analyzeTermItemRepository: Repository<AnalyzeTermItem>,

    private readonly analyzeTermItemService: AnalyzeTermItemService,
  ) {}

  async getAnalyzeDetail(findOptionsWhere?: FindOptionsWhere<AnalyzePolicy>) {
    const analyzeList: any[] = await this.analyzePolicyRepository.find({
      where: findOptionsWhere,
      relations: ['terms', 'terms.termItems', 'filterList'],
    });

    analyzeList.forEach((analyze) => {
      analyze.terms.forEach((term) => {
        term.termItems = term.termItems?.filter((item) => {
          return !item.parentId;
        });
      });

      analyze.filterListIds = analyze.filterList.map((filterItem) => filterItem.id);
    });

    const termItemIds = chain(analyzeList)
      .map((analyze) => {
        return analyze.terms.map((term) => term.termItems.map((i) => i.id));
      })
      .flattenDeep()
      .filter((id) => isExist(id))
      .value();

    if (isExist(termItemIds)) {
      const subTermItems = await this.analyzeTermItemRepository.find({
        where: {
          parentId: In(termItemIds),
        },
      });

      analyzeList.forEach((analyze) => {
        analyze.terms?.forEach((term) => {
          term.termItems?.forEach((item) => {
            item.children = subTermItems.filter((subItem) => subItem.parentId === item.id);
          });
        });
      });
    }

    return analyzeList;
  }

  /**
   * 分页查询
   * @param query
   * @param option
   * @returns
   */
  async page(query: any, option: PageOption) {
    const builder = this.analyzePolicyRepository.createQueryBuilder('a');

    const { list, pagination } = await selectBuilder.page<any>(query, option, builder);

    return {
      pagination,
      list: list.map((i) => {
        i.termItem = i.termItem.sort((a, b) => {
          if (a.seq === b.seq) {
            return +a.itemSeq - +b.itemSeq;
          }
          return +a.seq - +b.seq;
        });
        return i;
      }),
    };
  }

  async create(data: any) {
    const { id } = await this.save(data);
    return {
      id,
    };
  }

  async update(data: any) {
    await this.save(data);
  }

  /**
   * 保存分析策略
   */
  async save(data: any) {
    const analyzePolicy = new AnalyzePolicy();
    analyzePolicy.id = data.id;
    analyzePolicy.name = data.name;
    analyzePolicy.type = data.type;
    analyzePolicy.createBy = data.createBy;
    analyzePolicy.updateBy = data.updateBy;

    // 1. 保存分析策略
    const savedAnalyzePolicy = await this.analyzePolicyRepository.save(analyzePolicy);

    // 2. 处理 Term
    const termIds = chain(data.items)
      .map((i) => ({ id: i.termId }))
      .uniqBy('id')
      .value();

    // 2.1 删除旧的并且不存在的 Term
    const oldTerms = await this.analyzeTermRepository.find({
      where: { analyzePolicyId: savedAnalyzePolicy.id },
    });

    const { dKeys } = getDifferences(termIds, oldTerms);
    if (dKeys.length > 0) await this.analyzeTermRepository.delete(dKeys);

    // 3. 处理 TermItem
    const termItemPromises = chain(data.items)
      .groupBy('seq')
      .mapValues((items, seq) => {
        return items.map((i) => ({ ...i, seq, parent: analyzePolicy.id }));
      })
      .mapValues(async (items, seq) => {
        const { termId, message, actionPolicyId, severity } = items[0];
        const term = new AnalyzeTerm();
        term.id = termId;
        term.seq = +seq;
        term.message = message;
        term.analyzePolicyId = savedAnalyzePolicy.id;
        term.actionPolicyId = actionPolicyId;
        term.severity = severity;
        const savedTerm = await this.analyzeTermRepository.save(term);
        // 处理 TermItem 的 seq 字段
        const termItem = items.map((item) => ({ ...item, seq: item.itemSeq }));
        await this.analyzeTermItemService.saveByTermId(termItem, savedTerm.id);
      })
      .map((v) => v)
      .value();

    await Promise.all(termItemPromises);
    return {
      id: savedAnalyzePolicy.id,
    };
  }

  /**
   * 获取分析策略详情
   */
  async info(id: number) {
    const analyzePolicy = await this.analyzePolicyRepository.findOne({
      where: { id },
    });

    const terms = await this.analyzeTermRepository.find({
      where: { analyzePolicyId: id },
      relations: ['termItems'],
    });

    const items = chain(terms)
      .map((term) => {
        term.termItems = term.termItems.map((item) => ({
          ...item,
          seq: term.seq,
          itemSeq: item.seq,
          actionPolicyId: term.actionPolicyId,
          parentId: term.analyzePolicyId,
          message: term.message,
          severity: term.severity,
        }));

        return term.termItems;
      })
      .flatten()
      .value();

    return {
      ...omit(analyzePolicy, ['terms']),
      items,
    };
  }

  /**
   * 删除分析策略
   */
  async delete(id: number | number[]) {
    id = Array.isArray(id) ? id : [id];

    const connection = this.analyzePolicyRepository.manager.connection;
    const runner = connection.createQueryRunner();
    await runner.startTransaction();

    try {
      // 通过级联删除termItem
      // await runner.manager.delete(AnalyzeTermItem, { termId: In(<termIds>) });
      await runner.manager.delete(AnalyzeTerm, { analyzePolicyId: In(id) });
      await runner.manager.delete(AnalyzePolicy, id);
      await runner.commitTransaction();
    } catch (error) {
      await runner.rollbackTransaction();
      console.log(error.message);
      throw new Error(error.message);
    } finally {
      await runner.release();
    }
  }
}

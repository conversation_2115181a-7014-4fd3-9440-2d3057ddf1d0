import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { AnalyzeTermService } from '../service/analyze-term.service';

@Controller('analyze/term')
export class AnalyzeTermController {
  constructor(private readonly analyzeTermService: AnalyzeTermService) {}

  @Post('page')
  async find(@Query() query: any) {
    return this.analyzeTermService.find(query);
  }

  @Post('add')
  async create(@Body() data: any) {
    return this.analyzeTermService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.analyzeTermService.update(data.id, data);
  }

  @Post('delete')
  async remove(@Body('ids') ids: number | number[]) {
    return this.analyzeTermService.remove(ids);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.analyzeTermService.findOne(id);
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { AnalyzeLogService } from '../../analyze/service/analyze-log.service';
import { defineOption } from 'src/utils/orm/builder';
import { AnalyzePolicy } from '../entities';
import { ActionPolicy } from 'src/modules/action/entities/action-policy.entity';
import { isArray, isUndefined } from 'lodash';
import { dayjs } from 'src/utils/dayjs';

@Controller('analyze/log')
export class AnalyzeLogController {
  private readonly pageQueryOp = defineOption({
    select: ['a.*'],
    // fieldLike: [{ column: 'a.itemName', requestParam: 'itemName' }],
    fieldEq: [
      { column: 'a.analyzeId', requestParam: 'analyzeId' },
      { column: 'a.isGroup', requestParam: 'isGroup' },
      { column: 'a.isMatch', requestParam: 'isMatch' },
    ],
    // join: [
    //   {
    //     type: 'leftJoin',
    //     entity: AnalyzePolicy,
    //     alias: 'b',
    //     condition: 'a.analyzeId = b.id',
    //   },
    //   {
    //     type: 'leftJoin',
    //     entity: ActionPolicy,
    //     alias: 'c',
    //     condition: 'a.actionId = c.id',
    //   },
    // ],
  });

  constructor(private readonly analyzeLogService: AnalyzeLogService) {}

  @Get()
  async find(@Query() query: any) {
    return this.analyzeLogService.find(query, this.pageQueryOp);
  }

  @Post()
  async create(@Body() data: any) {
    return this.analyzeLogService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.analyzeLogService.update(data.id, data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[], @Body('isClear') isClear: boolean) {
    if (isClear) return this.analyzeLogService.clear();
    return this.analyzeLogService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.analyzeLogService.findOne(id);
  }
}

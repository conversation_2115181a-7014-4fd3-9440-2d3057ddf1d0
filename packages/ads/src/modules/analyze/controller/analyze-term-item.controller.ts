import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { defineOption } from 'src/utils/orm/builder';
import { SelectQueryBuilder } from 'typeorm';
import { AnalyzeTermItemService } from '../service/analyze-term-item.service';
import { AnalyzeTerm } from '../entities/analyze-term.entity';
import { AnalyzeTermItem } from '../entities/analyze-term-item.entity';
import { ActionPolicy } from '../../action/entities/action-policy.entity';

@Controller('analyze/term/item')
export class AnalyzeTermItemController {
  constructor(private readonly analyzeTermItemService: AnalyzeTermItemService) {}

  pageQueryOp = defineOption({
    select: ['a.*', 'b.message message', 'b.severity severity', 'b.seq termSeq', 'c.name actionName'],
    fieldEq: [
      { column: 'b.analyzePolicyId', requestParam: 'analyzePolicyId' },
      { column: 'a.classifyField', requestParam: 'classifyField' },
      { column: 'b.actionPolicyId', requestParam: 'actionPolicyId' },
    ],
    fieldLike: [{ column: 'b.message', requestParam: 'message' }],
    join: [
      {
        entity: AnalyzeTerm,
        alias: 'b',
        condition: 'a.termId = b.id',
        type: 'leftJoin',
      },
      {
        entity: ActionPolicy,
        alias: 'c',
        condition: 'b.actionPolicyId = c.id',
        type: 'leftJoin',
      },
    ],
    extend: (builder: SelectQueryBuilder<AnalyzeTermItem>) => {
      builder.andWhere('a.parentId IS NULL');
    },
  });

  @Post('page')
  async find(@Body() body: any) {
    return this.analyzeTermItemService.find(body, this.pageQueryOp);
  }

  @Post('add')
  async create(@Body() data: any) {
    return this.analyzeTermItemService.create(data);
  }

  @Post('update')
  async update(@Body() data: any) {
    return this.analyzeTermItemService.update(data.id, data);
  }

  @Post('delete')
  async remove(@Body('id') id: number | number[]) {
    return this.analyzeTermItemService.remove(id);
  }

  @Get('info')
  async info(@Query('id') id: number) {
    return this.analyzeTermItemService.findOne(id);
  }
}

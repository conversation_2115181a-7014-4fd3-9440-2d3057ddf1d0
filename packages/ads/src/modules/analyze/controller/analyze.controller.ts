import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { AnalyzeService } from '../service/analyze.service';
import { defineOption } from 'src/utils/orm/builder';
import { AnalyzeTerm } from '../entities/analyze-term.entity';
import { AnalyzeTermItem } from '../entities/analyze-term-item.entity';
import { ActionPolicy } from '../../action/entities/action-policy.entity';
import { SelectQueryBuilder } from 'typeorm';

@Controller('analyze')
export class AnalyzeController {
  pageQueryOp = defineOption({
    select: [
      'a.*',
      `JSON_ARRAYAGG(
        JSON_OBJECT(
          'id', CONCAT(a.id, '-', c.id),
          'cId', c.id,
          'seq', b.seq,
          'itemSeq', c.seq,
          'message', b.message,
          'actionName', d.name,
          'actionPolicyId', d.id,
          'classifyMethod', c.classifyMethod,
          'classifyField', c.classifyField,
          'stringValue', c.stringValue,
          'maxValue', c.maxValue,
          'minValue', c.minValue,
          'start', c.start,
          'end', c.end,
          'severity', b.severity
        )
      ) AS termItem
      `,
    ],
    keyWordLikeFields: ['a.name'],
    fieldEq: [{ column: 'a.isGroup', requestParam: 'isGroup' }],
    join: [
      {
        entity: AnalyzeTerm,
        alias: 'b',
        condition: 'a.id = b.analyzePolicyId',
        type: 'leftJoin',
      },
      {
        entity: AnalyzeTermItem,
        alias: 'c',
        condition: 'c.termId = b.id',
        type: 'leftJoin',
      },
      {
        entity: ActionPolicy,
        alias: 'd',
        condition: 'b.actionPolicyId = d.id',
        type: 'leftJoin',
      },
    ],
    extend: async (find: SelectQueryBuilder<AnalyzeTermItem>) => {
      find.groupBy('a.id');
    },
  });

  constructor(private readonly analyzeService: AnalyzeService) {}

  @Post('page')
  async page(@Body() query) {
    return this.analyzeService.page(query, this.pageQueryOp);
  }

  @Post('add')
  async add(@Body() params) {
    return this.analyzeService.create(params);
  }

  @Post('update')
  async update(@Body() params) {
    return this.analyzeService.update(params);
  }

  @Post('delete')
  async delete(@Body('ids') ids) {
    return this.analyzeService.delete(ids);
  }

  @Get('info')
  async info(@Query('id') id) {
    return this.analyzeService.info(id);
  }
}

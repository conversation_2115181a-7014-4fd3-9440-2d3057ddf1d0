import { Module } from '@nestjs/common';
import { PullSchedule } from './schedule/pull.schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PullFlowHistory } from './entities/pull_flow_history';
import { PullController } from './controller/pull.controller';
import { PullService } from './service/pull.service';
import { PullFormatSchedule } from './schedule/format.schedule';
import { PullFlowFormatted } from './entities/pull_flow_formatted';
import { PullFormattedController } from './controller/pull-formatted.controller';
import { PullFormattedService } from './service/pull-formatted.service';
import { RedisModule } from 'src/base';
import { PullFormattedStatusSchedule } from './schedule/status.schedule';
import {
  PullCMIService,
  PullCTGService,
  PullCTGOldService,
  PullDDGService,
  PullDPKService,
  PullUtilsService,
} from './service';
import { PullFormattedDataAdditionalInfoSchedule } from './schedule/additionalInfo.schedule';
import { ConfModule } from '../conf/conf.module';
import { PullJDHKG3Service } from './service/pull-jd-hkg3.service';
import { PullJDLAX1Service } from './service/pull-jd-lax1.service';
import { PullJDSGP1Service } from './service/pull-jd-sgp1.service';
import { SyncOPSModule } from '../sync-ops/sync-ops.module';
import { OAuth2Module } from '@/base/oauth2/oauth2.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PullFlowHistory, PullFlowFormatted]),
    ConfModule,
    RedisModule,
    SyncOPSModule,
    OAuth2Module,
  ],
  providers: [
    PullService,
    PullFormattedService,

    // 任务
    PullSchedule,
    PullFormatSchedule,
    PullFormattedStatusSchedule,
    PullFormattedDataAdditionalInfoSchedule,

    // 数据拉取服务
    PullCMIService,
    PullCTGService,
    PullCTGOldService,
    PullDDGService,
    PullDPKService,
    PullJDHKG3Service,
    PullJDLAX1Service,
    PullJDSGP1Service,
    PullUtilsService,
  ],
  controllers: [PullController, PullFormattedController],
})
export class PullModule {}

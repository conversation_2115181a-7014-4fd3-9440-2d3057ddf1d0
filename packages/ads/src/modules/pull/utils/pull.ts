import { InsertResult } from 'typeorm';
import { PullFlowHistory } from '../entities/pull_flow_history';

export interface PullPlatformService<T = any> {
  /**
   * 执行函数
   */
  handlePull: () => Promise<void>;

  /**
   * 请求平台API数据函数
   * @returns T[]
   */
  fetchCurrentData: () => Promise<T[]>;

  /**
   * 获取当前请求攻击进行中的项目
   * @param items
   * @returns
   */
  getCurrentOngoingItems: (items: T[]) => T[];

  /**
   * 获取上一次进行中的项目IDs
   * @returns
   */
  getPrevOngoingIds(): Promise<string[]>;

  /**
   * 根据当前正在进行中的项目和上一次进行中的项目Ids获取已经结束的Ids
   */
  getCompletedIds(prevIds: string[], currentIds: string[]): string[];

  /**
   * 返回已经结束的项目
   * @param completedIds
   * @param items
   */
  getCompletedItems?: (completedIds: string, items: T[]) => T[];
  fetchCompletedItems?: (completedIds: string, items: T[]) => T[];

  /**
   * 合并正在攻击的项目和已经结束的项目
   * @param ids
   * @returns
   */
  mergeOngoingAndCompletedItems(ongoingItems: T[], completedItems: T[]): T[];

  /**
   * 格式化数据
   * @param items
   */
  formatItems(items: T[]): Partial<PullFlowHistory>[];

  /**
   * 插入数据库
   * @param items
   */
  insertData(items: PullFlowHistory[]): Promise<InsertResult>;

  /**
   *  将本次请求的ID设置为上一次
   * @param ids
   * @returns
   */
  setPrevOngoingIds: (ids: string[]) => void;

  setCurrentOngoingIdIpMap?: (items: any[]) => void;
  getCurrentOngoingIdIpMap?: (...args: any[]) => { ip: string; id: number }[];
}

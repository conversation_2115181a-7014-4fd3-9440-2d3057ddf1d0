import { chain } from 'lodash';
import dayjs from 'src/utils/dayjs';
import bigUtils from 'src/utils/big';

export function formatParsedMessages(messages) {
  return chain(messages)
    .map((message) => {
      const { subject, from, date, body } = message;
      const lines = body.split('\n');
      const splitLines = splitByUUID(lines);

      return splitLines.map((lines) => {
        const formattedBody = {};
        lines.forEach((line) => {
          const match = line.match(/^(.*?):\s*(.*)$/);
          if (match) {
            const key = match[1].trim();
            const value = match[2].trim();
            formattedBody[key] = value;
          }
        });
        return {
          subject,
          from,
          date,
          body: formattedBody,
        };
      });
    })
    .flatten()
    .value();
}

export function formatDate(value) {
  if (!dayjs.utc(value, 'DD.MM.YYYY HH:mm [UTC]').isValid()) return '';

  return dayjs.utc(value, 'DD.MM.YYYY HH:mm [UTC]').tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
}

export function formatDPKBytes(valueStr) {
  if (!valueStr) return 0;

  const [value, unit] = valueStr.split(' ');
  if (!unit) return value;

  valueStr = value + unit[0];

  return bigUtils.convertMetricSuffix(valueStr);
}

export function splitByUUID(data) {
  const result = [];
  let currentSegment = [];
  let containsUUID = false;

  data.forEach((line) => {
    if (line.startsWith('UUID:')) {
      currentSegment.push(line);
      containsUUID = true;
      result.push(currentSegment);
      currentSegment = [];
      containsUUID = false;
    } else {
      currentSegment.push(line);
    }
  });

  if (currentSegment.length > 0 && containsUUID) {
    result.push(currentSegment);
  }

  return result.filter((segment) => segment.some((line) => line.startsWith('UUID:')));
}

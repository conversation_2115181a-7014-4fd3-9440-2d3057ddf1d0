import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { SelectQueryBuilder } from 'typeorm';
import { isArray } from 'lodash';
import { defineOption } from 'src/utils/orm/builder';
import { PullService } from '../service/pull.service';
import { PullSchedule } from '../schedule/pull.schedule';
import { PullFlowHistory } from '../entities/pull_flow_history';
import { PullFormatSchedule } from '../schedule/format.schedule';

@Controller('pull')
export class PullController {
  private pageQueryOp(query) {
    return defineOption({
      select: ['a.*'],
      fieldEq: [{ column: 'a.source', requestParam: 'source' }],
      fieldLike: [{ column: 'a.ip', requestParam: 'ipLike' }],
      extend: (builder: SelectQueryBuilder<PullFlowHistory>) => {
        const { firstTime, gteBps } = query;

        if (isArray(firstTime) && firstTime.length === 2) {
          builder.andWhere('a.firstTime > :start AND a.firstTime <= :end', {
            start: firstTime[0],
            end: firstTime[1],
          });
        }

        if (gteBps) {
          builder.andWhere('CAST(a.bps AS UNSIGNED) >= :gteBps', { gteBps });
        }
      },
    });
  }
  constructor(
    private readonly pullService: PullService,
    private readonly pullScheduleService: PullSchedule,
    private formatSchedule: PullFormatSchedule,
  ) {}

  @Post('format')
  async format() {
    return this.formatSchedule.formatPullDataHandler();
  }

  @Post('manual')
  async test() {
    return this.pullScheduleService.handlePull();
  }

  @Get()
  async find(@Query() query: any) {
    return this.pullService.find(query, this.pageQueryOp(query));
  }

  @Post()
  async create(@Body() body: any) {
    return this.pullService.create(body);
  }

  @Get('/merge/time/slot')
  async getMergeTimeSlot() {
    return this.pullService.getMergeTimeSlot();
  }

  @Post('/merge/time/slot')
  async setMergeTimeSlot(@Body('value') value: number) {
    return this.pullService.setMergeTimeSlot(value);
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { PullFormattedService } from '../service/pull-formatted.service';
import { defineOption } from 'src/utils/orm/builder';
import { SelectQueryBuilder } from 'typeorm';
import { PullFlowFormatted } from '../entities/pull_flow_formatted';
import { isArray, isExist } from 'src/utils/types';

@Controller('pull-formatted')
export class PullFormattedController {
  pageOptions = (query: Record<string, any>) =>
    defineOption({
      select: ['a.*'],
      fieldEq: [
        { column: 'a.ip', requestParam: 'ip' },
        { column: 'a.id', requestParam: 'id' },
        { column: 'a.customer', requestParam: 'customer' },
        { column: 'a.location', requestParam: 'location' },
      ],
      findInSet: [{ column: 'a.source', requestParam: 'source' }],
      fieldLike: [{ column: 'a.type', requestParam: 'typeLike' }],
      extend: (builder: SelectQueryBuilder<PullFlowFormatted>) => {
        const { firstTime, ipLike, gteBps, customerLike } = query;
        if (isExist(query.firstTime) && isArray(query.firstTime)) {
          builder.andWhere('a.firstTime >= :start AND a.firstTime <= :end', {
            start: firstTime[0],
            end: firstTime[1],
          });
        }

        if (ipLike) {
          builder.andWhere('a.ip LIKE :ipLike', {
            ipLike: `${ipLike}%`,
          });
        }

        if (gteBps) {
          builder.andWhere('CAST(a.bps AS UNSIGNED) >= :gteBps', { gteBps });
        }

        if (customerLike) {
          builder.andWhere('a.customer LIKE :customerLike', {
            customerLike: `${customerLike}%`,
          });
        }
      },
    });

  constructor(private readonly pullFormattedService: PullFormattedService) {}

  @Get(':id/children')
  async findChildren(@Param('id') id: number) {
    return this.pullFormattedService.findChildren(id);
  }

  @Get()
  async find(@Query() query: any) {
    return this.pullFormattedService.find(query, this.pageOptions(query));
  }

  @Post()
  async create(@Body() data: any) {
    return this.pullFormattedService.create(data);
  }

  @Put()
  async update(@Body() data: any) {
    return this.pullFormattedService.update(data.id, data);
  }

  @Delete()
  async remove(@Body('id') id: number | number[]) {
    return this.pullFormattedService.remove(id);
  }

  @Get(':id')
  async info(@Param('id') id: number) {
    return this.pullFormattedService.findOne(id);
  }
}

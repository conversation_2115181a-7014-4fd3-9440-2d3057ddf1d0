import { Entity, PrimaryGeneratedColumn, CreateDateColumn, Column, Index, UpdateDateColumn } from 'typeorm';
import { StatusEnum } from '../enum';

@Entity('op_ads_pull_flow_formatted')
export class PullFlowFormatted {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ comment: '开始时间', type: 'datetime', nullable: true })
  firstTime: string;

  @Index()
  @Column({ comment: '结束时间', type: 'datetime', nullable: true })
  lastTime: string;

  @Index()
  @Column({ comment: 'AS', default: '' })
  as: string;

  @Index()
  @Column({ comment: 'IP', default: '' })
  ip: string;

  @Index()
  @Column({
    comment: 'BPS',
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
  })
  bps: string;

  @Index()
  @Column({
    comment: 'PPS',
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
  })
  pps: string;

  @Column({ comment: '异常类型', type: 'text', nullable: true })
  type: string;

  @Column({ comment: 'MO', default: '' })
  mo: string;

  @Index()
  @Column({ comment: '源', default: '' })
  source: string;

  @Index()
  @Column({ comment: '客户', default: '' })
  customer: string;

  @Index()
  @Column({ comment: '服务编号', default: '' })
  serviceNo: string;

  @Index()
  @Column({ comment: '区域', default: '' })
  location: string;

  @Column({ comment: '描述', default: '' })
  description: string;

  @Column({ comment: '状态', default: StatusEnum.ONGOING })
  status: StatusEnum;

  @Column({ comment: 'Children IDs', type: 'text', nullable: true })
  childrenIds: string;

  @Column({
    comment: '当前IP攻击的各平台流量项目最大Bps ID',
    type: 'text',
    nullable: true,
  })
  maxBpsItemIds: string;

  @Column({ comment: '时间窗口', default: '' })
  timeSlot: string;

  @CreateDateColumn()
  createTime: Date;

  @UpdateDateColumn()
  updateTime: Date;
}

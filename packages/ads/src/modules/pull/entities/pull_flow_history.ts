import { Entity, PrimaryGeneratedColumn, CreateDateColumn, Column, Index } from 'typeorm';
// import { StatusEnum } from '../enum';

@Entity('op_ads_pull_flow_history')
export class PullFlowHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ comment: 'UUID', nullable: true })
  uuid: string;

  @Index()
  @Column({ comment: 'UUID', nullable: true })
  platformUuid: string;

  @Index()
  @Column({ comment: '开始时间', type: 'datetime', nullable: true })
  firstTime: string;

  @Index()
  @Column({ comment: '结束时间', type: 'datetime', nullable: true })
  lastTime: string;

  @Index()
  @Column({
    comment: '拉取时间',
    type: 'datetime',
    nullable: true,
    default: () => 'CURRENT_TIMESTAMP',
  })
  pullTime: string;

  @Index()
  @Column({ comment: 'AS', default: '' })
  as: string;

  @Index()
  @Column({ comment: 'IP', default: '' })
  ip: string;

  @Index()
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
    comment: 'BPS',
  })
  bps: string;

  @Index()
  @Column({
    type: 'decimal',
    precision: 20,
    scale: 2,
    default: 0,
    comment: 'PPS',
  })
  pps: string;

  @Column({ comment: '异常类型', default: '' })
  type: string;

  @Column({ comment: 'MO', default: '' })
  mo: string;

  @Column({ comment: '详情', type: 'json', nullable: true })
  detail: any;

  @Index()
  @Column({ comment: '源', default: '' })
  source: string;

  // @Column({ comment: '状态', default: '' })
  // status: StatusEnum;

  @CreateDateColumn()
  createTime: Date;
}

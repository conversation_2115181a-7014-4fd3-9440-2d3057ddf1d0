import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';
import { chain, differenceBy, isUndefined } from 'lodash';

import dayjs from '@/utils/dayjs';
import { requestClient } from '@/utils/http/axios';
import { logger } from '@/utils/logger';
import { isEmpty, isExist } from '@/utils/types';
import { getParsedMessage } from '@/utils/gmail';
import { RedisService } from '@/base/redis/redis.service';

import { PullFlowHistory } from '../entities/pull_flow_history';
import { SourceEnum } from '../enum';
import { formatParsedMessages, formatDate, formatDPKBytes } from '../utils/format-dpk-email';
import { PullUtilsService } from './pull-util.service';
import { isValidLastTime } from '../utils/valid';
import { OAuth2Service } from '@/base/oauth2/oauth2.service';

@Injectable()
export class PullDPKService {
  pid = 'UUID';
  prevOngoingKey = 'ads:pull:dpk:prev:ongoing:ids';
  prevAllKey = 'ads:pull:dpk:prev:all:ids';

  constructor(
    @InjectRepository(PullFlowHistory)
    private readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly pullUtilsService: PullUtilsService,
    readonly redisService: RedisService,
    readonly oauth2Service: OAuth2Service,
  ) {}

  async handlePull() {
    try {
      const items = await this.fetchCurrentData();

      if (isEmpty(items)) return;

      const results = await Promise.all([
        // 获取当前正在攻击的数据
        this.getOngoingItems(items),
        // 获取已经结束的数据
        this.getCompletedItems(items),
        // 获取新的并且不存在ongoing中的数据
        this.getNewNonOngoingItems(items),
      ]);

      const insertItems = chain(results)
        .flatten()
        .filter((item) => isExist(item))
        .map((item) => this.formatItem(item))
        .value();

      if (insertItems.length) {
        await this.pullFlowHistoryRepository.insert(insertItems);
      }

      const [ongoingItems] = results;

      await Promise.all([
        this.pullUtilsService.setPrevValues(
          this.prevOngoingKey,
          ongoingItems.map((item) => item[this.pid]),
        ),
        this.pullUtilsService.setPrevValues(
          this.prevAllKey,
          items.map((item) => item[this.pid]),
        ),
      ]);
    } catch (error) {
      logger.error(`拉取DPK攻击信息失败，错误信息：${error.message}`);
    }
  }

  formatItem(item: any) {
    return {
      platformUuid: item.UUID,
      firstTime: item.Start,
      lastTime: item.End,
      ip: item.IP,
      bps: item.BPS,
      pps: item.PPS,
      type: item.Type,
      source: SourceEnum.DPK,
      pullTime: item.Start,
    } as PullFlowHistory;
  }

  /**
   * 获取正在攻击中的项目
   * @param items 原始数据
   * @returns
   */
  getOngoingItems(items: any[]) {
    return chain(items)
      .groupBy('UUID')
      .filter((group) => group.length === 1 && !isValidLastTime(group[0].End))
      .flatten()
      .value();
  }

  /**
   * 获取已经结束的项目
   * @param items 原始数据
   * @returns
   */
  async getCompletedItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).map((item) => {
      return item[this.pid];
    });
    // 2. 获取上一次正在攻击中的Ids
    const prevOngoingIds = await this.pullUtilsService.getPrevValues(this.prevOngoingKey);

    // 3. 对比这次和上一次正在攻击中的Ids，得到已经结束的Ids
    const completedItemIds = differenceBy(prevOngoingIds, ongoingIds);

    // 4. 获取已经结束的Items，并返回
    return items.filter((item) => {
      return completedItemIds.includes(item[this.pid]) && isValidLastTime(item.End);
    });
  }

  /**
   * 获取新的并且不存在于正在攻击中的数据
   * @param items 原始数据
   * @returns
   */
  async getNewNonOngoingItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).filter((item) => {
      return item[this.pid];
    });

    // 2. 获取上一次请求数据的Ids
    const prevIds = await this.pullUtilsService.getPrevValues(this.prevAllKey);

    const currentIds = items.map((item) => item[this.pid]);

    const newIds = differenceBy(currentIds, prevIds);

    const newNonOngoingIds = differenceBy(newIds, ongoingIds);

    return items.filter((item) => {
      return newNonOngoingIds.includes(item[this.pid]);
    });
  }

  async fetchCurrentData() {
    const accessToken = await this.fetchAccessToken();
    const after = dayjs().subtract(1, 'day').format('YYYY/MM/DD');
    const before = dayjs().add(1, 'day').format('YYYY/MM/DD');
    const q = `from:<EMAIL> after:${after} before:${before}`;
    const Authorization = `Bearer ${accessToken}`;

    const data = await requestClient.request({
      url: 'https://www.googleapis.com/gmail/v1/users/me/messages',
      method: 'GET',
      headers: { Authorization },
      params: {
        maxResults: 10, // 获取前10封邮件以便进行过滤
        q,
      },
    });

    const messages = data.messages;

    if (isUndefined(messages)) return;

    const detailedMessages = await Promise.all(
      messages.map(async (message) => {
        return requestClient.request({
          url: `https://www.googleapis.com/gmail/v1/users/me/messages/${message.id}`,
          method: 'GET',
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
      }),
    );

    const parsedMessages = detailedMessages.map(getParsedMessage);
    const formattedMessages = formatParsedMessages(parsedMessages);

    return formattedMessages
      .map((msg) => msg.body as any)
      .map((item) => {
        const UUID = item.UUID;
        const IP = item['Target IP address'];
        const Type = item['Attack type'];
        const Start = formatDate(item.Start);
        const End = formatDate(item.End);
        const BPS = formatDPKBytes(item['Number of bits per second']);
        const PPS = formatDPKBytes(item['Number of IP packets per second']);
        return {
          UUID,
          Start,
          End,
          BPS,
          PPS,
          IP,
          Type,
        };
      });
  }

  async fetchAccessToken() {
    try {
      const data = await this.oauth2Service.refreshToken();
      return data.access_token;
    } catch (error) {
      logger.error(`获取DPK鉴权信息失败`);
    }
  }
}

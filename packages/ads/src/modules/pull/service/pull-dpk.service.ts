import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { chain, differenceBy } from 'lodash';
import axios from 'axios';

import { logger } from '@/utils/logger';
import { isEmpty, isExist } from '@/utils/types';

import { PullFlowHistory } from '../entities/pull_flow_history';
import { SourceEnum } from '../enum';
import { PullUtilsService } from './pull-util.service';
import { isValidLastTime } from '../utils/valid';

interface DataPacketCookies {
  'intercom-id-rf2o90c4': string;
  'intercom-device-id-rf2o90c4': string;
  'intercom-session-rf2o90c4': string;
  access_token?: string;
}

interface DdosEvent {
  hurricaneId: string;
  startAt: string;
  endAt: string;
  targets: Array<{ targetIps: string[] }>;
}

interface DdosChartData {
  time: string;
  packets: number;
  bps: number;
}

@Injectable()
export class PullDPKService {
  pid = 'UUID';
  prevOngoingKey = 'ads:pull:dpk:prev:ongoing:ids';
  prevAllKey = 'ads:pull:dpk:prev:all:ids';

  // 固定的cookies配置 - 完全按照Python程序
  private readonly FIXED_COOKIES: DataPacketCookies = {
    'intercom-id-rf2o90c4': 'ce7b0275-e523-4898-a253-ae3752e7da4d',
    'intercom-device-id-rf2o90c4': '138115c4-b787-4d37-bb36-6acf606aeed5',
    'intercom-session-rf2o90c4':
      'M3hPdFY4TTNUUGhsWmtyUEVyVEExc2hjZFhyQTZZVlFK0hHZDhRempOMHI3SFo3TFlMRG9NR3dKNTBBckpyUC9idE9CTWN4N3RGRmlwUm52QTFka0ZPVGo1RnZQZGpDMzl5bEZiUnRUN3c9LS1SN0ZVdUUyVnlTZ0dibW9OcDhqTGd3PT0=--476e16224c5032a95d79d9f42e95268259b61e9f',
  };

  constructor(
    @InjectRepository(PullFlowHistory)
    private readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly pullUtilsService: PullUtilsService,
  ) {}

  async handlePull() {
    try {
      const items = await this.fetchDataPacketData();

      if (isEmpty(items)) {
        logger.info('DPK: 没有获取到新的攻击数据');
        return;
      }

      const results = await Promise.all([
        this.getOngoingItems(items),
        this.getCompletedItems(items),
        this.getNewNonOngoingItems(items),
      ]);

      const insertItems = chain(results)
        .flatten()
        .filter((item) => isExist(item))
        .map((item) => this.formatItem(item))
        .value();

      if (insertItems.length) {
        await this.pullFlowHistoryRepository.insert(insertItems);
        logger.info(`DPK: 成功插入 ${insertItems.length} 条攻击数据`);
      }

      const [ongoingItems] = results;

      await Promise.all([
        this.pullUtilsService.setPrevValues(
          this.prevOngoingKey,
          ongoingItems.map((item) => item[this.pid]),
        ),
        this.pullUtilsService.setPrevValues(
          this.prevAllKey,
          items.map((item) => item[this.pid]),
        ),
      ]);
    } catch (error) {
      logger.error(`拉取DPK攻击信息失败，错误信息：${error.message}`);
    }
  }

  // 完全按照Python程序的get_cookies函数
  private async getDataPacketCookies(email: string, password: string): Promise<DataPacketCookies> {
    try {
      const url = 'https://app.datapacket.com/login';
      const headers = {
        accept: 'application/json',
        'content-type': 'application/x-www-form-urlencoded',
        origin: 'https://app.datapacket.com',
        referer: 'https://app.datapacket.com/login',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      };
      const data = { email, password };

      const response = await axios.post(url, data, {
        headers,
        timeout: 10000,
        withCredentials: true,
      });

      // 先尝试从 Set-Cookie 中获取 access_token
      let token = response.headers['set-cookie']
        ?.find((cookie) => cookie.startsWith('access_token='))
        ?.split(';')[0]
        ?.split('=')[1];

      // 如果没从 Cookie 拿到，尝试从响应 JSON 体取
      if (!token) {
        try {
          const jsonResp = response.data;
          token = jsonResp.access_token || jsonResp.data?.access_token || '';
        } catch (error) {
          token = '';
        }
      }

      const cookies = { ...this.FIXED_COOKIES };
      cookies.access_token = token;
      return cookies;
    } catch (error) {
      logger.error(`获取DataPacket cookies失败: ${error.message}`);
      throw error;
    }
  }

  // 完全按照Python程序的get_Shield_attack_log函数
  private async getShieldAttackLog(cookies: DataPacketCookies): Promise<DdosEvent[]> {
    try {
      const url = 'https://app.datapacket.com/graphql';

      const query = `
        query DdosEvents($first:Int,$skip:Int){
          ddosEvents(first:$first,skip:$skip){
            hasNextPage
            ddosEvents{
              hurricaneId
              startAt
              endAt
              targets{targetIps}
            }
          }
        }`;

      const data = { query, variables: { first: 50, skip: 0 } };

      const headers = {
        accept: 'application/json',
        'content-type': 'application/json',
        origin: 'https://app.datapacket.com',
        referer: 'https://app.datapacket.com/shield',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      };

      const response = await axios.post(url, data, {
        headers: {
          ...headers,
          Cookie: Object.entries(cookies)
            .map(([key, value]) => `${key}=${value}`)
            .join('; '),
        },
        timeout: 10000,
      });

      const events = response.data.data.ddosEvents.ddosEvents;
      const tz = 8; // 东八区

      // 按照Python程序的逻辑处理数据
      for (const e of events) {
        const hid = e.hurricaneId;
        const ips = e.targets && e.targets[0] ? e.targets[0].targetIps.join(', ') : '—';
        const start = new Date(e.startAt.replace('Z', '+00:00'));
        start.setHours(start.getHours() + tz);
        const end = e.endAt ? new Date(e.endAt.replace('Z', '+00:00')) : null;
        if (end) {
          end.setHours(end.getHours() + tz);
        }

        const startStr = start.toISOString().replace('T', ' ').substring(0, 19);
        const endStr = end ? end.toISOString().replace('T', ' ').substring(0, 19) : '—';
        const period = end ? `${startStr} → ${endStr}` : `${startStr} → —`;
      }

      return events;
    } catch (error) {
      logger.error(`获取DataPacket攻击日志失败: ${error.message}`);
      throw error;
    }
  }

  // 完全按照Python程序的get_Shield_attack_log_detailed函数
  private async getShieldAttackLogDetailed(cookies: DataPacketCookies, attackId: string): Promise<DdosChartData[]> {
    try {
      const url = `https://app.datapacket.com/rest/api/ddosChartData/${attackId}`;

      const headers = {
        Accept: '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
        Priority: 'u=1, i',
        Referer: `https://app.datapacket.com/shield/attack/${attackId}`,
        'Sec-CH-UA': '"Google Chrome";v="137", "Chromium";v="137", "Not;A=Brand";v="99"',
        'Sec-CH-UA-Mobile': '?0',
        'Sec-CH-UA-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
        'Apollo-GraphQL-Client-Name': 'app',
        'Content-Type': 'application/json',
        Origin: 'https://app.datapacket.com',
      };

      const payload = {
        query: `
            query getDdosChart($attackId: UUID!) {
                ddosChartData(attackId: $attackId) {
                    timestamp
                    pps
                    bps
                }
            }
        `,
        variables: {
          attackId: attackId,
        },
      };

      const response = await axios.get(url, {
        headers: {
          ...headers,
          Cookie: Object.entries(cookies)
            .map(([key, value]) => `${key}=${value}`)
            .join('; '),
        },
        timeout: 15000,
      });

      const data = response.data;

      for (const entry of data) {
        const t = new Date(entry.time);
        const timeStr = t.toISOString().replace('T', ' ').substring(0, 19);
        const pps = entry.packets;
        const bps = entry.bps;
        const gbps = bps / 1_000_000_000;
      }

      return data;
    } catch (error) {
      logger.error(`获取DataPacket详细攻击数据失败: ${error.message}`);
      throw error;
    }
  }

  // 完全按照Python程序的主调用逻辑
  async fetchDataPacketData() {
    try {
      const email = '<EMAIL>';
      const password = 'DR6qhqPZjyAiHYG5oJ';

      const cookies = await this.getDataPacketCookies(email, password);

      const events = await this.getShieldAttackLog(cookies);

      if (!events || events.length === 0) {
        return [];
      }

      // 转换数据格式
      const items = await Promise.all(
        events.map(async (event) => {
          const startTime = new Date(event.startAt.replace('Z', '+00:00'));
          const endTime = event.endAt ? new Date(event.endAt.replace('Z', '+00:00')) : null;

          let bps = 0;
          let pps = 0;

          if (endTime) {
            try {
              const detailedData = await this.getShieldAttackLogDetailed(cookies, event.hurricaneId);
              if (detailedData && detailedData.length > 0) {
                const totalBps = detailedData.reduce((sum, item) => sum + item.bps, 0);
                const totalPps = detailedData.reduce((sum, item) => sum + item.packets, 0);
                bps = Math.round(totalBps / detailedData.length);
                pps = Math.round(totalPps / detailedData.length);
              }
            } catch (error) {
              logger.warn(`获取攻击 ${event.hurricaneId} 详细数据失败: ${error.message}`);
            }
          }

          return {
            UUID: event.hurricaneId,
            Start: startTime.toISOString(),
            End: endTime?.toISOString(),
            BPS: bps,
            PPS: pps,
            IP: event.targets?.[0]?.targetIps?.join(', ') || '—',
            Type: 'DDoS',
          };
        }),
      );

      return items;
    } catch (error) {
      logger.error(`获取DataPacket数据失败: ${error.message}`);
      return [];
    }
  }

  formatItem(item: any) {
    return {
      platformUuid: item.UUID,
      firstTime: item.Start,
      lastTime: item.End,
      ip: item.IP,
      bps: item.BPS,
      pps: item.PPS,
      type: item.Type,
      source: SourceEnum.DPK,
      pullTime: item.Start,
    } as PullFlowHistory;
  }

  getOngoingItems(items: any[]) {
    const result = chain(items)
      .groupBy(this.pid)
      .filter((group) => {
        return group.length === 1 && !isValidLastTime(group[0].End);
      })
      .flatten()
      .value();

    return result;
  }

  async getCompletedItems(items: any[]) {
    const ongoingIds = this.getOngoingItems(items).map((item) => {
      return item[this.pid];
    });
    const prevOngoingIds = await this.pullUtilsService.getPrevValues(this.prevOngoingKey);
    const completedItemIds = differenceBy(prevOngoingIds, ongoingIds);
    return items.filter((item) => {
      return completedItemIds.includes(item[this.pid]) && isValidLastTime(item.End);
    });
  }

  async getNewNonOngoingItems(items: any[]) {
    const ongoingIds = this.getOngoingItems(items).filter((item) => {
      return item[this.pid];
    });
    const prevIds = await this.pullUtilsService.getPrevValues(this.prevAllKey);
    const currentIds = items.map((item) => item[this.pid]);
    const newIds = differenceBy(currentIds, prevIds);
    const newNonOngoingIds = differenceBy(newIds, ongoingIds);
    return items.filter((item) => {
      return newNonOngoingIds.includes(item[this.pid]);
    });
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { In, Repository } from 'typeorm';
import { chain, difference, isUndefined, maxBy, minBy } from 'lodash';

import bigUtils from '@/utils/big';
import dayjs, { czDayjs } from '@/utils/dayjs';
import { logger } from '@/utils/logger';
import { requestClient } from '@/utils/http/axios';
import { RedisService } from '@/base/redis/redis.service';
import { czLodash } from '@/utils/lodash';
import { isExist } from '@/utils/types';

import { SourceEnum } from '../enum';
import { formatIp } from '../utils/format';
import { PullFlowHistory } from '../entities/pull_flow_history';
import { PullUtilsService } from './pull-util.service';

@Injectable()
export class PullCMIService {
  constructor(
    @InjectRepository(PullFlowHistory)
    readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly pullUtilsService: PullUtilsService,
    readonly redisService: RedisService,
  ) {}

  async handlePull() {
    try {
      // 1. 获取实时数据（原始）
      const currentData = await this.fetchCurrentData();
      if (isUndefined(currentData)) return;

      // 2. 获取正在进行中的攻击数据
      const currentOngoingItems = currentData;
      const currentOngoingItemIds = currentData.map((i) => i.dstip);
      const previousOngoingIds = await this.pullUtilsService.getPreviousOngoingIds('ads:pull:cmi:prev:ips');

      const completedItemIds = difference(previousOngoingIds, currentOngoingItemIds);

      if (completedItemIds.length) {
        const lastIdValues = await this.getPrevDataIpMapLastIds(completedItemIds);
        const updateIds = lastIdValues.filter((id) => !!id);
        if (updateIds.length > 0) {
          this.pullFlowHistoryRepository
            .createQueryBuilder()
            .update()
            .set({ lastTime: czDayjs.current() })
            .where('id IN (:...updateIds)', { updateIds })
            .execute();
        }
      }

      // 3. 合并数据并格式化
      const mergedItems = currentOngoingItems;
      const formattedItems = this.formatItems(mergedItems);
      const insertedResult = await this.pullFlowHistoryRepository.insert(formattedItems);

      const insertedIds = insertedResult.identifiers.map(({ id }) => id);

      if (insertedIds.length === 0) return;

      const insertedData = await this.pullFlowHistoryRepository.find({
        where: { id: In(insertedIds) },
      });

      await this.pullUtilsService.setPreviousOngoingIds(
        'ads:pull:cmi:prev:ips',
        insertedData.map((i) => i.ip),
      );

      await this.setCurrentOngoingIdIpMap(insertedData);
    } catch (error) {
      logger.error(`拉取CMI攻击失败，错误信息：${error.message}`);
    }
  }

  async fetchCurrentData(): Promise<any[]> {
    try {
      const [smallerAttack, largerAttack] = await Promise.all([
        // smallerAttack
        requestClient
          .request({
            method: 'get',
            url: 'https://adbos.nsfocus.cloud/api/v1/scrubbingmonitorgroups/156/points/225/monitor/ads/events/ongoing_event/',
            params: {
              chart: 'table',
              orderby: '-time',
              limit: 10,
              offset: 0,
            },
            headers: {
              Accept: 'application/json',
              Authorization: 'cToken JIJKv3JPbo5_TI9rn7dnEObGjCbO-Y2QqkMOKrXga3o',
            },
          })
          .then((data) => data.results)
          .catch((error) => {
            console.error(`Fetch CMI Small Attack Error: ${error.message}`);
          }),
        // largerAttack
        requestClient
          .request({
            method: 'get',
            url: 'https://adbos.nsfocus.cloud/api/v1/scrubbingmonitorgroups/156/points/225/monitor/nta/events/?chart=table&orderby=-time&limit=100&offset=0',
            headers: {
              Accept: 'application/json',
              Authorization: 'cToken JIJKv3JPbo5_TI9rn7dnEObGjCbO-Y2QqkMOKrXga3o',
            },
          })
          .then((data) => data.results)
          .catch((error) => {
            console.error(`Fetch CMI Larger Attack Error: ${error.message}`);
          }),
      ]);

      // 1. 对相同IP相同攻击类型取最大值
      const sameTypeMaxBpsResults = chain([...(smallerAttack || []), ...(largerAttack || [])])
        .filter((item) => isExist(item))
        .map((item) => {
          item.max_in_bps = item.max_in_bps || item.max_bps;
          item.max_in_pps = item.max_in_pps || item.max_pps;
          return item;
        })
        .groupBy((item) => `${item.dstip}_${item.attack_type}`)
        .map((items) => maxBy(items, (item) => +item.max_in_bps))
        .value();

      // 2. 对相同IP不同攻击类型进行合并
      const results = chain(sameTypeMaxBpsResults)
        .groupBy('dstip')
        .map((items) => {
          const firstItem = items[0];

          if (!firstItem) return;

          return {
            ...firstItem,
            attack_type: czLodash.joinUniqStringByArray(items, 'attack_type'),
            attack_port: czLodash.joinUniqStringByArray(items, 'attack_port'),
            max_in_bps: bigUtils.reduce(items, 'max_in_bps'),
            max_in_pps: bigUtils.reduce(items, 'max_in_pps'),
            max_drop_bps: bigUtils.reduce(items, 'max_drop_bps'),
            max_drop_pps: bigUtils.reduce(items, 'max_drop_pps'),
            country: czLodash.joinUniqStringByArray(items, 'country'),
            start_time: minBy(items, 'start_time')?.start_time,
          };
        })
        .filter((item) => isExist(item))
        .value();

      return results;
    } catch (error) {
      console.error(`PULL - CMI Error: ${error.message}`);
    }
  }

  formatItems(items: any) {
    return items.map((item) => {
      return {
        firstTime: dayjs(item.start_time).format('YYYY-MM-DD HH:mm:ss'),
        lastTime: dayjs(item.end_time).format('YYYY-MM-DD HH:mm:ss'),
        ip: formatIp(item.dstip),
        type: item.attack_type,
        bps: item.max_in_bps,
        pps: item.max_in_pps,
        mo: item.mo,
        source: SourceEnum.CMI,
        // status: item.status,
        platformUuid: item.id,
        detail: item,
      } as PullFlowHistory;
    });
  }

  async setCurrentOngoingIdIpMap(items: PullFlowHistory[]) {
    const pairs = chain(items)
      .mapKeys((item) => {
        return `ads:pull:cmi:prev:${item.ip}`;
      })
      .mapValues((item) => {
        return item.id;
      })
      .value();

    await this.redisService.setMultipleKeysUsingPipeline(pairs, 5, 'minutes');
  }

  async getPrevDataIpMapLastIds(ips: string[]) {
    return this.redisService.getMultipleKeysUsingPipeline(
      ips.map((ip) => {
        return `ads:pull:cmi:prev:${ip}`;
      }),
    );
  }
}

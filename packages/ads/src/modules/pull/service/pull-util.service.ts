import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { chain } from 'lodash';
import { RedisService } from 'src/base/redis/redis.service';
import { PullFlowHistory } from '../entities/pull_flow_history';
import { isValidLastTime } from '../utils/valid';

@Injectable()
export class PullUtilsService {
  constructor(
    @InjectRepository(PullFlowHistory)
    readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly redisService: RedisService,
  ) {}

  // 获取上一次拉取的未结束的攻击id
  async getPreviousOngoingIds(key: string) {
    const ongoingIdString = await this.redisService.get(key);
    if (!ongoingIdString) return [];
    return chain(ongoingIdString)
      .split(',')
      .map((item) => item.trim())
      .uniq()
      .value();
  }

  async setPreviousOngoingIds(key: string, values: (string | number)[]) {
    if (!key || !values) {
      throw new Error('Key Or Values is Required!');
    }

    await this.redisService.set(key, values.join(','));
  }

  async getPrevValues(key: string) {
    const ongoingIdString = await this.redisService.get(key);
    if (!ongoingIdString) return [];
    return chain(ongoingIdString)
      .split(',')
      .map((item) => item.trim())
      .uniq()
      .value();
  }

  async setPrevValues(key: string, values: (string | number)[]) {
    if (!key || !values) {
      throw new Error('Key Or Values is Required!');
    }

    await this.redisService.set(key, values.join(','));
  }

  getOngoingItems(items: any[], endTimeKey: string) {
    return items.filter((item) => {
      return !isValidLastTime(item[endTimeKey]);
    });
  }
}

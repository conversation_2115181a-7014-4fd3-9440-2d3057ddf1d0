import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import axios from 'axios';
import * as xml2js from 'xml2js';
import { Repository } from 'typeorm';
import { chain, differenceBy, mapValues } from 'lodash';

import dayjs from '@/utils/dayjs';
import bigUtils from '@/utils/big';
import { logger } from '@/utils/logger';
import { isEmpty, isExist } from '@/utils/types';
import { requestClient } from '@/utils/http/axios';

import { PullFlowHistory } from '../entities/pull_flow_history';
import { SourceEnum } from '../enum';
import { formatIp } from '../utils/format';
import { PullUtilsService } from './pull-util.service';
import { isValidLastTime } from '../utils/valid';

@Injectable()
export class PullJDLAX1Service {
  // 平台id
  pid = 'sid';
  prevOngoingKey = 'ads:pull:jd:lax1:prev:ongoing:ids';
  prevAllKey = 'ads:pull:jd:lax1:prev:all:ids';

  constructor(
    @InjectRepository(PullFlowHistory)
    private readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    private pullUtilsService: PullUtilsService,
  ) {}

  async handlePull() {
    try {
      // 1. 获取原始数据，如果原始数据没有，直接返回
      const items = await this.fetch();

      if (!items || items.length === 0) return;

      const results = await Promise.all([
        // 获取当前正在攻击的数据
        this.getOngoingItems(items),
        // 获取已经结束的数据
        this.getCompletedItems(items),
        // 获取新的并且不存在ongoing中的数据
        this.getNewNonOngoingItems(items),
      ]);

      const insertItems = await Promise.all(
        chain(results)
          .flatten()
          .filter((item) => isExist(item))
          .map(async (item) => {
            try {
              const itemDetail = await this.fetchItemDetail(item);
              item.pps = itemDetail?.status_host?.host?.find((i) => i.type[0] === 'host')?.input_pps[0];
            } catch (error) {
              console.error(
                `Request ${SourceEnum.JD} host ${item.host?.hostname} to attack IP PPS abnormality, Error Message: ${error.message}`,
              );
            } finally {
              return this.formatItem(item);
            }
          })
          .value(),
      );

      if (insertItems.length > 0) {
        await this.pullFlowHistoryRepository.insert(insertItems);
      }

      const [ongoingItems] = results;

      await Promise.all([
        this.pullUtilsService.setPrevValues(
          this.prevOngoingKey,
          ongoingItems.map((item) => item[this.pid]),
        ),
        this.pullUtilsService.setPrevValues(
          this.prevAllKey,
          items.map((item) => item[this.pid]),
        ),
      ]);
    } catch (error) {
      logger.error(`拉取金盾LAX1攻击信息失败，错误信息：${error.message}`);
    }
  }

  async fetch() {
    const results = await Promise.all(
      hosts.map(async (host: any) => {
        if (!host.cookie) await login(host);

        // 开始请求数据
        const responseData = await requestClient.get(host.dataUrl, {
          params: { param_view: 0 },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            Accept:
              'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            Cookie: host.cookie,
          },
        });
        try {
        } catch (error) {
          console.error(`JD Fetch Data ${host.hostname} Error`);
          return;
        }

        const parser = new xml2js.Parser();
        const result = await parser.parseStringPromise(responseData);
        if (result.failure) {
          login(host);
          return;
        }

        const data = chain(result.logs_report_attack.report || [])
          .map((item) => {
            item = mapValues(item, (value) => value[0]);
            item.sid = host.hostname + '-' + item.sid;
            item.hostname = host.hostname;
            item.source = `${SourceEnum.JD}-${host.hostname}`;
            item.host = host;
            return item;
          })
          .value();

        return data;
      }),
    );

    return chain(results)
      .flatten()
      .filter((item) => isExist(item))
      .value();
  }

  /**
   * 获取正在攻击中的项目
   * @param items 原始数据
   * @returns
   */
  getOngoingItems(items: any[]) {
    return items.filter((item) => {
      return !isValidLastTime(item.end);
    });
  }

  /**
   * 获取已经结束的项目
   * @param items 原始数据
   * @returns
   */
  async getCompletedItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).map((item) => {
      return item[this.pid];
    });
    // 2. 获取上一次正在攻击中的Ids
    const prevOngoingIds = await this.pullUtilsService.getPreviousOngoingIds(this.prevOngoingKey);

    // 3. 对比这次和上一次正在攻击中的Ids，得到已经结束的Ids
    const completedItemIds = differenceBy(prevOngoingIds, ongoingIds);
    // 4. 获取已经结束的Items，并返回
    return items.filter((item) => {
      return completedItemIds.includes(item[this.pid]);
    });
  }

  /**
   * 获取新的并且不存在于正在攻击中的数据
   * @param items 原始数据
   * @returns
   */
  async getNewNonOngoingItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).filter((item) => {
      return item[this.pid];
    });

    // 2. 获取上一次请求数据的Ids
    const prevIds = await this.pullUtilsService.getPrevValues(this.prevAllKey);

    const currentIds = items.map((item) => item[this.pid]);

    const newIds = differenceBy(currentIds, prevIds);

    const newNonOngoingIds = differenceBy(newIds, ongoingIds);

    return items.filter((item) => {
      return newNonOngoingIds.includes(item[this.pid]);
    });
  }

  async fetchItemDetail(item) {
    if (isEmpty(item) || isEmpty(item.host)) return;

    const { ip } = item;
    const { detailUrl: url, cookie } = item.host;
    const detailHtmlResponseData = await requestClient.request({
      url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        Cookie: cookie,
      },
      data: {
        param_submit_type: 'query-host',
        param_this_sort: 1,
        param_view: 3,
        param_netaddr: ip,
        [ip]: 0,
      },
    });

    const parser = new xml2js.Parser();
    const result = await parser.parseStringPromise(detailHtmlResponseData);
    return result;
  }

  formatItem(item: any) {
    return {
      firstTime: dayjs(item.begin).format('YYYY-MM-DD HH:mm:ss'),
      lastTime: dayjs(item.end).format('YYYY-MM-DD HH:mm:ss'),
      ip: formatIp(item.dst_address),
      type: item.flags,
      bps: bigUtils.convertMetricSuffix(item.bps_in + 'M'),
      pps: item.pps,
      mo: item.mo,
      source: item.source,
      platformUuid: item.sid,
    };
  }
}

export const hosts = [
  {
    hostname: 'LAX1',
    dataUrl: 'http://************:28099/cgi-bin/logs_report_attack.cgi',
    detailUrl: 'http://************:28099/cgi-bin/status_host.cgi',
    authUrl: 'http://************:28099/cgi-bin/login.cgi',
  },
];

async function login(host) {
  const response = await axios.post(
    host.authUrl,
    {
      param_type: 'login',
      param_username: 'ops3-api',
      param_password: 'iJf0Xhobiqrxjv',
    },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        Accept:
          'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
      },
    },
  );

  // 获取cookie
  const cookies = response.headers['set-cookie'];
  const cookie = cookies.map((item) => item.split(';')[0]).join(';');
  host.cookie = cookie;
}

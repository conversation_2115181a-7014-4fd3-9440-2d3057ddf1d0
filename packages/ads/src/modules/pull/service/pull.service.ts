import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PageOption, selectBuilder } from 'src/utils/orm/builder';
import { PullFlowHistory } from '../entities/pull_flow_history';

@Injectable()
export class PullService {
  constructor(
    @InjectRepository(PullFlowHistory)
    private readonly pullFlowHistory: Repository<PullFlowHistory>,

    private dataSource: DataSource,
  ) {}
  /**
   * 分页查询
   */
  async find(query: Record<string, any>, option: PageOption) {
    return selectBuilder.page(query, option, this.pullFlowHistory.createQueryBuilder('a'));
  }

  async create(data: Record<string, any>) {
    return this.pullFlowHistory.insert(data);
  }

  async getMergeTimeSlot() {
    const [result] = await this.dataSource.query(
      `SELECT value FROM op_ads_conf where \`key\` = "adsPullDataMergeTimeSlot"`,
    );

    return result?.value || 0;
  }

  async setMergeTimeSlot(value: number) {
    await this.dataSource.query(`UPDATE op_ads_conf SET value = ${value} WHERE \`key\` = "adsPullDataMergeTimeSlot"`);
  }
}

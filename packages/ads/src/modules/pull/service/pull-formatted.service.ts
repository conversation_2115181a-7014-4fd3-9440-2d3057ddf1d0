import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { PullFlowFormatted } from '../entities/pull_flow_formatted';
import { PageOption, PageResult, selectBuilder } from 'src/utils/orm/builder';
import { isEmpty, isExist } from 'src/utils/types';
import { PullFlowHistory } from '../entities/pull_flow_history';
import { chain, maxBy, merge } from 'lodash';

@Injectable()
export class PullFormattedService {
  constructor(
    @InjectRepository(PullFlowFormatted)
    private readonly pullFormattedRepository: Repository<PullFlowFormatted>,

    @InjectRepository(PullFlowHistory)
    private readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
  ) {}

  async findChildren(id: number) {
    const formatted = await this.findOne(id);
    if (!formatted || isEmpty(formatted.childrenIds)) return [];

    const children = await this.pullFlowHistoryRepository.find({
      where: { id: In(formatted.childrenIds.split(',').map(Number)) },
    });

    return chain(children)
      .groupBy('source')
      .map((items) => maxBy(items, (i) => +i.bps))
      .filter((item) => isExist(item))
      .value();
  }

  /**
   * 分页查询
   * @param query
   * @returns
   */
  async find(query: any, option: PageOption) {
    const result = await selectBuilder.page(query, option, this.pullFormattedRepository.createQueryBuilder('a'));

    if (!result) return;
    if (!result.list.length) return result;

    const { list, pagination } = result;

    // 获取 Children ID 个数为1的项目
    const oneChileItems = this.getOneChildrenIdItems(list);

    if (oneChileItems.length === 0) return result;

    const childrenIds = chain(oneChileItems)
      .map((i) => i.childrenIds.split(','))
      .flatten()
      .uniq()
      .value();

    if (childrenIds.length === 0) return result;

    const childrenItems = await this.pullFlowHistoryRepository.find({
      where: { id: In(childrenIds) },
    });

    return {
      pagination,
      list: list.map((item) => {
        const childrenIds = item.childrenIds
          ?.split(',')
          .filter((i) => i.trim().length > 0)
          .map((i) => +i);

        if (childrenIds.length === 0) {
          return item;
        } else if (childrenIds.length > 1) {
          return { ...item, isExistChildren: true };
        } else {
          const children = childrenItems.find((i) => {
            return childrenIds[0] === i.id;
          });
          return { ...item, ...children };
        }
      }),
    } as PageResult;
  }

  async getMaxId() {
    const data = await this.pullFormattedRepository.createQueryBuilder().select('MAX(id) id').getRawOne();

    return data?.id;
  }

  getOneChildrenIdItems(items: PullFlowFormatted[]) {
    return items.filter((item) => {
      return (
        item &&
        item.childrenIds &&
        item.childrenIds
          .split(',')
          .map((i) => i.trim())
          .filter((i) => !!i).length === 1
      );
    });
  }

  /**
   * 新增
   * @param data
   */
  async create(data: PullFlowFormatted) {
    return this.pullFormattedRepository.insert(data);
  }

  /**
   * 更新
   */
  async update(id: number, data: PullFlowFormatted) {
    await this.pullFormattedRepository.update(id, data);
  }

  /**
   * 删除
   * @param id
   * @returns
   */
  async remove(id: number | number[]) {
    await this.pullFormattedRepository.delete(id);
  }

  /**
   * 获取详情
   * @param id
   * @returns
   */
  async findOne(id: number) {
    return this.pullFormattedRepository.findOne({ where: { id } });
  }
}

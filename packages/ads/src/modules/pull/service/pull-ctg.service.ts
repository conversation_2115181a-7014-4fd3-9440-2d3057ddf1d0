import * as crypto from 'crypto';
import * as https from 'https';

import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';

import { Repository } from 'typeorm';
import { chain, differenceBy } from 'lodash';

import dayjs from '@/utils/dayjs';
import { logger } from '@/utils/logger';
import { requestClient } from '@/utils/http/axios';
import { isArray, isEmpty, isExist } from '@/utils/types';
import { RedisService } from '@/base/redis/redis.service';
import bigUtils from '@/utils/big';

import { PullFlowHistory } from '../entities/pull_flow_history';
import { SourceEnum } from '../enum';
import { formatIp } from '../utils/format';
import { PullUtilsService } from './pull-util.service';

const httpsAgent = new https.Agent({ rejectUnauthorized: false });

@Injectable()
export class PullCTGService implements OnModuleInit {
  pid = 'id';
  prevOngoingKey = 'ads:pull:ctg2:prev:ongoing:ids';
  prevAllKey = 'ads:pull:ctg2:prev:all:ids';
  tokenKey = 'ads:pull:ctg2:token';

  constructor(
    @InjectRepository(PullFlowHistory)
    readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly pullUtilsService: PullUtilsService,
    readonly redisService: RedisService,
  ) {}

  onModuleInit() {
    this.fetchAccessToken().catch((error) => {
      logger.error(`获取CTG2 Token失败，错误信息：${error.message}`);
    });
  }

  async handlePull() {
    try {
      const items = await this.fetchAlarmSummaryInfo();

      if (isEmpty(items)) return;

      const results = await Promise.all([
        // 获取当前正在攻击的数据
        this.getOngoingItems(items),
        // 获取已经结束的数据
        this.getCompletedItems(items),
        // 获取新的并且不存在ongoing中的数据
        this.getNewNonOngoingItems(items),
      ]);

      const insertItems = chain(results)
        .flatten()
        .filter((item) => isExist(item))
        .map((item) => this.formatItem(item))
        .value();

      if (insertItems.length) {
        await this.pullFlowHistoryRepository.insert(insertItems);
      }

      const [ongoingItems] = results;

      await Promise.all([
        this.pullUtilsService.setPrevValues(
          this.prevOngoingKey,
          ongoingItems.map((item) => item[this.pid]),
        ),
        this.pullUtilsService.setPrevValues(
          this.prevAllKey,
          items.map((item) => item[this.pid]),
        ),
      ]);
    } catch (error) {
      logger.error(`拉取CTG2攻击信息失败，错误信息：${error.message}`);
    }
  }

  /**
   * 获取正在攻击中的项目
   * @param items 原始数据
   * @returns
   */
  getOngoingItems(items: any[]) {
    return this.pullUtilsService.getOngoingItems(items, 'endTime');
  }

  /**
   * 获取已经结束的项目
   * @param items 原始数据
   * @returns
   */
  async getCompletedItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).map((item) => {
      return item[this.pid];
    });
    // 2. 获取上一次正在攻击中的Ids
    const prevOngoingIds = await this.pullUtilsService.getPrevValues(this.prevOngoingKey);

    // 3. 对比这次和上一次正在攻击中的Ids，得到已经结束的Ids
    const completedItemIds = differenceBy(prevOngoingIds, ongoingIds);
    if (completedItemIds.length === 0) return [];

    // 4. 获取已经结束的Items，并返回
    return []; // Testing...
    return Promise.all(
      completedItemIds.map((id) => {
        return this.fetchAlarmDetailInfo(id);
      }),
    );
  }

  /**
   * 获取新的并且不存在于正在攻击中的数据
   * @param items 原始数据
   * @returns
   */
  async getNewNonOngoingItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).filter((item) => {
      return item[this.pid];
    });

    // 2. 获取上一次请求数据的Ids
    const prevIds = await this.pullUtilsService.getPrevValues(this.prevAllKey);

    const currentIds = items.map((item) => item[this.pid]);

    const newIds = differenceBy(currentIds, prevIds);

    const newNonOngoingIds = differenceBy(newIds, ongoingIds);

    return items.filter((item) => {
      return newNonOngoingIds.includes(item[this.pid]);
    });
  }

  formatItem(item: any) {
    return {
      firstTime: dayjs(item.startTime).format('YYYY-MM-DD HH:mm:ss'),
      lastTime: dayjs(item.endTime).format('YYYY-MM-DD HH:mm:ss'),
      ip: formatIp(item.attackIp),
      type: item.attackType && isArray(item.attackType) ? item.attackType.join(',') : item.attackType,
      bps: bigUtils.convertMetricSuffix(item.maxBps),
      pps: bigUtils.convertMetricSuffix(item.maxPps),
      mo: item.moName,
      platformUuid: item.id,
      source: SourceEnum.CTG,
    } as PullFlowHistory;
  }

  async fetchAlarmSummaryInfo() {
    const url = 'https://antiddos.chinatelecomglobal.com:8443/portal/webservice/rest/alarm/queryAlarmSummaryInfo';

    const paramsJson = JSON.stringify({
      startTime: dayjs().subtract(1, 'hour').valueOf(),
      endTime: dayjs().valueOf(),
    });

    const encryptedData = await this.encrypt(paramsJson);
    const responseData = await requestClient.post(url, encryptedData, {
      httpsAgent,
      headers: {
        'Access-Key': 'd85b971c1f59ebf69532aa1393efc444',
      },
    });

    const data = JSON.parse(await this.decrypt(responseData));

    if (data.code === 505 && data.msg?.includes('请求参数解密失败')) {
      this.fetchAccessToken();
      return;
    }

    return data?.result.map((item) => {
      item.id = `${item.id}`;
      item.maxBps = this.cleanString(item.maxBps);
      item.maxPps = this.cleanString(item.maxPps);
      return item;
    });
  }

  async fetchAlarmDetailInfo(alarmId: string | number) {
    const url = 'https://antiddos.chinatelecomglobal.com:8443/portal/webservice/rest/alarm/queryAlarmDetailInfo';

    const paramsJson = JSON.stringify({ alarmId });
    const encryptedData = await this.encrypt(paramsJson);
    const responseData = await requestClient.post(url, encryptedData, {
      httpsAgent,
      headers: {
        'Access-Key': 'd85b971c1f59ebf69532aa1393efc444',
      },
    });

    const data = JSON.parse(await this.decrypt(responseData));

    if (data.code === 505 && data.msg?.includes('请求参数解密失败')) {
      this.fetchAccessToken();
      return;
    }

    return data?.result?.[0];
  }

  @Cron(CronExpression.EVERY_HOUR)
  async fetchAccessToken() {
    try {
      const url = 'https://**************:8443/portal/webservice/rest/authentication/generateToken';
      const data = await requestClient.get(url, {
        httpsAgent,
      });

      const accessToken = data.result;
      await this.redisService.set(this.tokenKey, accessToken);
      return data.result;
    } catch (error) {
      logger.error(`获取CTG2 Token失败，错误信息：${error.message}`);
    }
  }

  // 加密函数
  async encrypt(text) {
    const token = await this.redisService.get(this.tokenKey);
    const cipher = crypto.createCipheriv('aes-256-ecb', Buffer.from(token, 'utf8'), null);
    cipher.setAutoPadding(true);
    let encrypted = cipher.update(text, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
  }

  // 解密函数
  async decrypt(encryptedText) {
    const token = await this.redisService.get(this.tokenKey);
    const decipher = crypto.createDecipheriv('aes-256-ecb', Buffer.from(token, 'utf8'), null);
    decipher.setAutoPadding(true);
    let decrypted = decipher.update(encryptedText, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }

  cleanString(inputStr?: string) {
    if (!inputStr) return;
    // 定义需要移除字符的列表
    const units = ['K', 'M', 'G'];

    // 遍历每个单位字符
    for (const unit of units) {
      const pos = inputStr.indexOf(unit); // 查找单位字符的位置
      if (pos !== -1) return inputStr.substring(0, pos + 1); // 如果找到了单位字符，则截断字符串
    }

    // 如果没有找到单位字符，返回空值
    // return inputStr;
  }
}

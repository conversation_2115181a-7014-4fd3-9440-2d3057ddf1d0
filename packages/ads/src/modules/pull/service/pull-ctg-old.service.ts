import * as https from 'node:https';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';
import { chain, differenceBy } from 'lodash';

import dayjs, { czDayjs } from '@/utils/dayjs';
import { requestClient } from '@/utils/http/axios';
import { logger } from '@/utils/logger';
import { isArray, isEmpty, isExist } from '@/utils/types';
import { RedisService } from '@/base/redis/redis.service';

import { PullFlowHistory } from '../entities/pull_flow_history';
import { SourceEnum } from '../enum';
import { formatIp } from '../utils/format';
import { PullUtilsService } from './pull-util.service';

@Injectable()
export class PullCTGOldService {
  pid = 'alertid';
  prevOngoingKey = 'ads:pull:ctg1:prev:ongoing:ids';
  prevAllKey = 'ads:pull:ctg1:prev:all:ids';

  constructor(
    @InjectRepository(PullFlowHistory)
    readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly pullUtilsService: PullUtilsService,
    readonly redisService: RedisService,
  ) {}

  async handlePull() {
    try {
      const items = await this.fetch();

      if (isEmpty(items)) return;

      const results = await Promise.all([
        // 获取当前正在攻击的数据
        this.getOngoingItems(items),
        // 获取已经结束的数据
        this.getCompletedItems(items),
        // 获取新的并且不存在ongoing中的数据
        this.getNewNonOngoingItems(items),
      ]);

      const insertItems = chain(results)
        .flatten()
        .filter((item) => isExist(item))
        .map((item) => this.formatItem(item))
        .value();

      if (insertItems.length) {
        await this.pullFlowHistoryRepository.insert(insertItems);
      }

      const [ongoingItems] = results;

      await Promise.all([
        this.pullUtilsService.setPrevValues(
          this.prevOngoingKey,
          ongoingItems.map((item) => item[this.pid]),
        ),
        this.pullUtilsService.setPrevValues(
          this.prevAllKey,
          items.map((item) => item[this.pid]),
        ),
      ]);
    } catch (error) {
      logger.error(`同步CTG1信息失败，错误信息：${error.message}`);
    }
  }

  /**
   * 获取正在攻击中的项目
   * @param items 原始数据
   * @returns
   */
  getOngoingItems(items: any[]) {
    return this.pullUtilsService.getOngoingItems(items, 'stop'); //
  }

  /**
   * 获取已经结束的项目
   * @param items 原始数据
   * @returns
   */
  async getCompletedItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).map((item) => {
      return item[this.pid];
    });
    // 2. 获取上一次正在攻击中的Ids
    const prevOngoingIds = await this.pullUtilsService.getPrevValues(this.prevOngoingKey);

    // 3. 对比这次和上一次正在攻击中的Ids，得到已经结束的Ids
    const completedItemIds = differenceBy(prevOngoingIds, ongoingIds);

    // 4. 获取已经结束的Items，并返回
    return items.filter((item) => {
      return completedItemIds.includes(item[this.pid]);
    });
  }

  /**
   * 获取新的并且不存在于正在攻击中的数据
   * @param items 原始数据
   * @returns
   */
  async getNewNonOngoingItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).filter((item) => {
      return item[this.pid];
    });

    // 2. 获取上一次请求数据的Ids
    const prevIds = await this.pullUtilsService.getPrevValues(this.prevAllKey);

    const currentIds = items.map((item) => item[this.pid]);

    const newIds = differenceBy(currentIds, prevIds);

    const newNonOngoingIds = differenceBy(newIds, ongoingIds);

    return items.filter((item) => {
      return newNonOngoingIds.includes(item[this.pid]);
    });
  }

  formatItem(item: any) {
    return {
      firstTime: czDayjs.formatUnix(item.start),
      lastTime: czDayjs.formatUnix(item.stop),
      ip: formatIp(item.ip),
      type: item.type && isArray(item.type) ? item.type.join(',') : item.type,
      bps: item.bps,
      pps: item.pps,
      mo: item.mo,
      // status: item.status,
      platformUuid: item.alertid,
      source: SourceEnum.CTG,
    } as PullFlowHistory;
  }

  async fetch() {
    try {
      const data = await requestClient.request({
        url: 'https://antiddos.chinatelecomglobal.com/rest/alert/list',
        method: 'GET',
        httpsAgent: new https.Agent({ rejectUnauthorized: false }),
        headers: {
          'Content-Type': 'application/json',
          Token: '71764a07b2a8be028c04c7885fbb7ad3',
        },
        data: {
          starttime: dayjs().subtract(1, 'hour').unix(),
          endtime: dayjs().unix(),
        },
      });

      return data.data?.map((item) => {
        item.alertid = '' + item.alertid;
        return item;
      });
    } catch (error) {
      console.error(`Fetch CTG Error: ${error.message}`);
    }
  }
}

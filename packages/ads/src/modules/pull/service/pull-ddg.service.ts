import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Repository } from 'typeorm';
import { chain, differenceBy } from 'lodash';

import { czDayjs } from '@/utils/dayjs';
import { logger } from '@/utils/logger';
import { requestClient } from '@/utils/http/axios';
import { isArray, isEmpty, isExist } from '@/utils/types';
import { RedisService } from '@/base/redis/redis.service';

import { SourceEnum } from '../enum';
import { PullFlowHistory } from '../entities/pull_flow_history';
import { PullUtilsService } from './pull-util.service';
import { formatIp } from '../utils/format';

@Injectable()
export class PullDDGService {
  pid = 'id';
  prevOngoingKey = 'ads:pull:ddg:prev:ongoing:ids';
  prevAllKey = 'ads:pull:ddg:prev:all:ids';

  constructor(
    @InjectRepository(PullFlowHistory)
    readonly pullFlowHistoryRepository: Repository<PullFlowHistory>,
    readonly pullUtilsService: PullUtilsService,
    readonly redisService: RedisService,
  ) {}

  async handlePull() {
    try {
      const items = await this.fetch();

      if (isEmpty(items)) return;

      const results = await Promise.all([
        // 获取当前正在攻击的数据
        this.getOngoingItems(items),
        // 获取已经结束的数据
        this.getCompletedItems(items),
        // 获取新的并且不存在ongoing中的数据
        this.getNewNonOngoingItems(items),
      ]);

      const insertItems = chain(results)
        .flatten()
        .filter((item) => isExist(item))
        .map((item) => this.formatItem(item))
        .value();

      if (insertItems.length > 0) {
        await this.pullFlowHistoryRepository.insert(insertItems);
      }

      const [ongoingItems] = results;

      await Promise.all([
        this.pullUtilsService.setPrevValues(
          this.prevOngoingKey,
          ongoingItems.map((item) => item[this.pid]),
        ),
        this.pullUtilsService.setPrevValues(
          this.prevAllKey,
          items.map((item) => item[this.pid]),
        ),
      ]);
    } catch (error) {
      logger.error(`拉取DDG攻击信息失败，错误信息：${error.message}`);
    }
  }

  /**
   * 获取正在攻击中的项目
   * @param items 原始数据
   * @returns
   */
  getOngoingItems(items: any[]) {
    return this.pullUtilsService.getOngoingItems(items, 'end_time');
  }

  /**
   * 获取已经结束的项目
   * @param items 原始数据
   * @returns
   */
  async getCompletedItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).map((item) => {
      return item[this.pid];
    });
    // 2. 获取上一次正在攻击中的Ids
    const prevOngoingIds = await this.pullUtilsService.getPrevValues(this.prevOngoingKey);

    // 3. 对比这次和上一次正在攻击中的Ids，得到已经结束的Ids
    const completedItemIds = differenceBy(prevOngoingIds, ongoingIds);

    // 4. 获取已经结束的Items，并返回
    return items.filter((item) => {
      return completedItemIds.includes(item[this.pid]);
    });
  }

  /**
   * 获取新的并且不存在于正在攻击中的数据
   * @param items 原始数据
   * @returns
   */
  async getNewNonOngoingItems(items: any[]) {
    // 1. 获取正在攻击中的Ids
    const ongoingIds = this.getOngoingItems(items).filter((item) => {
      return item[this.pid];
    });

    // 2. 获取上一次请求数据的Ids
    const prevIds = await this.pullUtilsService.getPrevValues(this.prevAllKey);

    const currentIds = items.map((item) => item[this.pid]);

    const newIds = differenceBy(currentIds, prevIds);

    const newNonOngoingIds = differenceBy(newIds, ongoingIds);

    return items.filter((item) => {
      return newNonOngoingIds.includes(item[this.pid]);
    });
  }

  formatItem(item: any) {
    return {
      firstTime: czDayjs.formatUnix(item.start_time),
      lastTime: czDayjs.formatUnix(item.end_time),
      ip: item.ip ? formatIp(item.ip) : item.as,
      type: item.type && isArray(item.type) ? item.type.join(',') : item.type,
      bps: item.peak_bps,
      pps: item.peak_pps,
      mo: item.mo,
      // status: item.status,
      platformUuid: item.id,
      source: SourceEnum.DDG,
    };
  }

  async fetch() {
    const data = await requestClient.request({
      url: 'https://webapi.ddos-guard.net/api/attack/data',
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'x-api-key': 'c886fc70d79f4d754b17d3dd469f66c5',
        'X-Client-Id': '177329',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      params: {
        service_id: 59911,
        limit: 20,
        sort: '-start_time',
      },
    });

    const attackList = data.data;

    if (!isArray(attackList) || isEmpty(attackList)) return;

    const attackDetails = await Promise.all(
      chain(attackList)
        .sortBy('end_time')
        .map((item) => {
          return requestClient.request({
            url: 'https://webapi.ddos-guard.net/api/attack/detail',
            method: 'GET',
            headers: {
              Accept: 'application/json',
              'x-api-key': 'c886fc70d79f4d754b17d3dd469f66c5',
              'X-Client-Id': '177329',
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            params: {
              service_id: 59911,
              id: item.id,
            },
          });
        })
        .value(),
    );

    return attackDetails.map((item) => {
      item.id = `${item.id}`;
      return item;
    });
  }
}

#!/usr/bin/env python
# -*- coding:utf-8 -*-
# auther : <PERSON><PERSON><PERSON>
# time :  2025/7/9 11:10
import requests
from datetime import datetime, timezone, timedelta

FIXED_COOKIES = {
    "intercom-id-rf2o90c4": "ce7b0275-e523-4898-a253-ae3752e7da4d",
    "intercom-device-id-rf2o90c4": "138115c4-b787-4d37-bb36-6acf606aeed5",
    "intercom-session-rf2o90c4": (
        "M3hPdFY4TTNUUGhsWmtyUEVyVEExc2hjZFhyQTZZVlFK0hHZDhRempOMHI3SFo3TFlM"
        "RG9NR3dKNTBBckpyUC9idE9CTWN4N3RGRmlwUm52QTFka0ZPVGo1RnZQZGpDMzl5bEZi"
        "UnRUN3c9LS1SN0ZVdUUyVnlTZ0dibW9OcDhqTGd3PT0=--476e16224c5032a95d79d9f4"
        "2e95268259b61e9f"
    )
}

def get_cookies(email: str, password: str) -> dict:
    url = "https://app.datapacket.com/login"
    headers = {
        "accept": "application/json",
        "content-type": "application/x-www-form-urlencoded",
        "origin": "https://app.datapacket.com",
        "referer": "https://app.datapacket.com/login",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    }
    data = {"email": email, "password": password}

    with requests.Session() as s:
        resp = s.post(url, headers=headers, data=data, timeout=10)
        resp.raise_for_status()

        # 先尝试从 Set-Cookie 中获取 access_token
        token = s.cookies.get("access_token")

        # 如果没从 Cookie 拿到，尝试从响应 JSON 体取
        if not token:
            try:
                json_resp = resp.json()
                # 根据接口实际字段改这里
                token = json_resp.get("access_token") or json_resp.get("data", {}).get("access_token") or ""
            except Exception:
                token = ""

    cookies = FIXED_COOKIES.copy()
    cookies["access_token"] = token
    return cookies

def get_Shield_attack_log(cookies):
    url = "https://app.datapacket.com/graphql"

    query = """
    query DdosEvents($first:Int,$skip:Int){
      ddosEvents(first:$first,skip:$skip){
        hasNextPage
        ddosEvents{
          hurricaneId
          startAt
          endAt
          targets{targetIps}
        }
      }
    }"""
    data = {"query": query, "variables": {"first": 50, "skip": 0}}

    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "origin": "https://app.datapacket.com",
        "referer": "https://app.datapacket.com/shield",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    }

    resp = requests.post(url, headers=headers, cookies=cookies, json=data, timeout=10)
    resp.raise_for_status()

    events = resp.json()["data"]["ddosEvents"]["ddosEvents"]
    tz = timezone(timedelta(hours=8))  # 东八区

    for e in events:
        hid = e["hurricaneId"]
        ips = ", ".join(e["targets"][0]["targetIps"]) if e["targets"] else "—"
        start = datetime.fromisoformat(e["startAt"].replace("Z", "+00:00")).astimezone(tz)
        end = datetime.fromisoformat(e["endAt"].replace("Z", "+00:00")).astimezone(tz) if e["endAt"] else None
        period = f"{start:%Y-%m-%d %H:%M:%S} → {end:%Y-%m-%d %H:%M:%S}" if end else f"{start:%Y-%m-%d %H:%M:%S} → —"

        print(f"{hid} | {ips} | {period}")

def get_Shield_attack_log_detailed(cookies):
    # GraphQL 端点
    url = "https://app.datapacket.com/rest/api/ddosChartData/b61c4d23-e72b-4db1-b5d1-92beb6fbf73a"

    # 必要请求头（删掉了重复项与 ^）
    headers = {
        "Accept": "*/*",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7",
        "Priority": "u=1, i",
        "Referer": "https://app.datapacket.com/shield/attack/b61c4d23-e72b-4db1-b5d1-92beb6fbf73a",
        "Sec-CH-UA": '"Google Chrome";v="137", "Chromium";v="137", "Not;A=Brand";v="99"',
        "Sec-CH-UA-Mobile": "?0",
        "Sec-CH-UA-Platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Apollo-GraphQL-Client-Name": "app",
        "Content-Type": "application/json",
        "Origin": "https://app.datapacket.com",
    }

    # Cookie 如果要长期使用建议保存到环境变量或凭证管理器，而不要硬编码
    cookies = cookies

    # GraphQL 请求体
    payload = {
        "query": """
            # 这里写你的 GraphQL 查询，例如：
            query getDdosChart($attackId: UUID!) {
                ddosChartData(attackId: $attackId) {
                    timestamp
                    pps
                    bps
                }
            }
        """,
        "variables": {
            "attackId": "b61c4d23-e72b-4db1-b5d1-92beb6fbf73a"
        }
    }

    # 发送 POST 请求
    resp = requests.get(url, headers=headers, cookies=cookies, json=payload, timeout=15)

    # print(resp.status_code)
    # print(resp.text)
    data = resp.json()
    # data = json.loads(raw_data)

    print(f"{'时间':<20} {'PPS':>10} {'BPS':>15} {'Gbps':>10}")
    print("=" * 60)

    for entry in data:
        t = datetime.strptime(entry['time'], "%Y-%m-%dT%H:%M:%SZ")
        time_str = t.strftime("%Y-%m-%d %H:%M:%S")
        pps = entry['packets']
        bps = entry['bps']
        gbps = bps / 1_000_000_000
        print(f"{time_str:<20} {pps:>10,} {bps:>15,} {gbps:>9.2f}")
# ── 示例调用 ──────────────────────────────────────────────────────────────
if __name__ == "__main__":
    ck = get_cookies("<EMAIL>", "DR6qhqPZjyAiHYG5oJ")
    print(f"cookies = {ck}")
    cookies = ck
    get_Shield_attack_log(cookies)
    get_Shield_attack_log_detailed(cookies)
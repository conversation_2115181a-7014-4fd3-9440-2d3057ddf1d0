import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { PullFlowFormatted } from '../entities/pull_flow_formatted';
import { StatusEnum } from '../enum';
import { Cron, CronExpression } from '@nestjs/schedule';
import { RedisService } from 'src/base/redis/redis.service';

@Injectable()
export class PullFormattedStatusSchedule {
  constructor(
    private dataSource: DataSource,
    private redisService: RedisService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async updateStatus() {
    try {
      const ongoingSumData = await this.dataSource.manager.find(PullFlowFormatted, {
        where: { status: StatusEnum.ONGOING },
      });

      if (ongoingSumData.length === 0) return;

      const timeSlots = await this.redisService.getMultipleKeysUsingPipeline(
        ongoingSumData.map((data) => `ads:pull:time-slot:${data.ip}`),
      );

      const updateIds = ongoingSumData.filter((_, index) => timeSlots[index] === null).map(({ id }) => id);

      if (updateIds.length === 0) return;

      await this.dataSource.manager.update(PullFlowFormatted, updateIds, {
        status: StatusEnum.END,
      });
    } catch (error) {
      console.error(error);
    }
  }
}

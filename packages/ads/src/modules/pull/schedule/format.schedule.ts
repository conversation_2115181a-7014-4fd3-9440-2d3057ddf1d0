import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { DataSource } from 'typeorm';
import { chain, maxBy, minBy, sortBy } from 'lodash';

import bigUtils from '@/utils/big';
import { logger } from '@/utils/logger';
import { dayjs } from '@/utils/dayjs';
import { czLodash } from '@/utils/lodash';
import { isEmpty, isExist } from '@/utils/types';
import { RedisService } from '@/base/redis/redis.service';

import { PullFlowFormatted } from '../entities/pull_flow_formatted';
import { PullFlowHistory } from '../entities/pull_flow_history';

@Injectable()
export class PullFormatSchedule {
  private readonly timeSlotKey = 'pullTime';

  constructor(
    private dataSource: DataSource,
    private redisService: RedisService,
  ) {}

  @Cron(CronExpression.EVERY_10_SECONDS)
  async handler() {
    try {
      await this.formatPullDataHandler();
    } catch (error) {
      logger.error(`执行外部攻击数据汇总异常，错误信息：${error.message}`);
    }
  }

  async formatPullDataHandler() {
    // 1. 获取最大的 formattedId 和 mergeTimeSlot
    const { maxFormattedId, mergeTimeSlot } = await this.getMaxFormattedIdAndMergeTimeSlot();
    // console.log('maxFormattedId', maxFormattedId);
    // console.log('mergeTimeSlot', mergeTimeSlot);

    // 2. 获取原始Pull数据
    const dataResult: any[] = await this.getPullData(maxFormattedId);
    if (isEmpty(dataResult)) return 'no data';
    // console.log('dataResult length', dataResult.length);

    // 3. 获取IP TimeSlot: Record<IP, timeSlot>
    const preIpMarkTimeSlot = await this.getRedisValues(
      chain(dataResult)
        .map('ip')
        .uniq()
        .map((ip) => `ads:pull:time-slot:${ip}`)
        .value(),
    );
    // console.log('preIpMarkTimeSlot', preIpMarkTimeSlot);

    // 4. 标记原始Pull数据，并返回
    const markedData = this.markPullData(dataResult, preIpMarkTimeSlot, {
      mergeTimeSlot,
    });

    // 5. 获取上一次标记的childrenIds
    const preMarkedChildrenIds = await this.getRedisValues(
      chain(markedData)
        .map((item) => `ads:pull:formatted:${item.ip}---${item.timeSlot}`)
        .uniq()
        .value(),
    );

    const childrenIds = chain(preMarkedChildrenIds)
      .filter((item) => isExist(item))
      .map((idStr) => idStr.split(',').map((id) => +id))
      .flatten()
      .uniq()
      .value();

    let childrenData: any[] = [];

    if (isExist(childrenIds)) {
      childrenData = await this.dataSource
        .createQueryBuilder()
        .select('a.*')
        .from(PullFlowHistory, 'a')
        .where('a.id IN (:...ids)', { ids: childrenIds }) // 根据childrenIds获取原始Pull数据
        .getRawMany();
    }

    // 5. 格式化原始Pull数据，并返回
    const formattedData = this.getCalculateFormattedData(markedData, childrenData);

    // 7. 更新Redis
    await Promise.all([
      this.redisService.setMultipleKeysUsingPipeline(
        chain(formattedData)
          .mapKeys((item) => `ads:pull:time-slot:${item.ip}`)
          .mapValues((item) => item.timeSlot)
          .value(),
        mergeTimeSlot,
        'minutes',
      ),

      this.redisService.setMultipleKeysUsingPipeline(
        chain(formattedData)
          .mapKeys((item) => `ads:pull:formatted:${item.ip}---${item.timeSlot}`)
          .mapValues((item) => item.childrenIds)
          .value(),
        mergeTimeSlot,
        'minutes',
      ),
    ]);

    // 7. 更新数据库
    await this.dataSource.transaction(async (manager) => {
      await Promise.all(
        formattedData.map(async (item) => {
          // await manager.upsert(PullFlowFormatted, item, ['ip', 'timeSlot']);
          const formatted = await manager.findOne(PullFlowFormatted, {
            where: { ip: item.ip, timeSlot: item.timeSlot },
          });

          if (formatted) {
            await manager.update(PullFlowFormatted, formatted.id, item);
          } else {
            await manager.insert(PullFlowFormatted, item);
          }
        }),
      );
    });

    // 8. 更新maxFormattedId
    await this.dataSource.query(`UPDATE op_ads_conf SET value = ? WHERE \`key\` = "maxPullDataFormattedId"`, [
      maxBy(dataResult, 'id')?.id,
    ]);

    return formattedData;
  }

  // 获取最大的 formattedId 和 mergeTimeSlot
  async getMaxFormattedIdAndMergeTimeSlot() {
    const results = await this.dataSource.query(
      `SELECT \`key\`, value FROM op_ads_conf WHERE \`key\` = "maxPullDataFormattedId" OR \`key\`="adsPullDataMergeTimeSlot"`,
    );

    const maxFormattedId = results?.find((item) => item.key === 'maxPullDataFormattedId')?.value;
    const mergeTimeSlot = results?.find((item) => item.key === 'adsPullDataMergeTimeSlot')?.value;

    return {
      maxFormattedId: maxFormattedId ? parseInt(maxFormattedId) : 0,
      mergeTimeSlot: mergeTimeSlot ? parseInt(mergeTimeSlot) : 10,
    };
  }

  // 获取原始Pull数据，传入id，返回大于该id的原始数据
  async getPullData(id: number) {
    const dataResult = await this.dataSource.query(
      `SELECT * FROM op_ads_pull_flow_history WHERE source != "TEST" && id > ?`,
      [id],
    );
    return dataResult;
  }

  // 获取PullData IPs，传入IPs，返回Redis中的数据
  async getRedisValues(keys: string[]) {
    const values = await this.redisService.getMultipleKeysUsingPipeline(keys);
    const result = chain(keys)
      .map((key, index) => ({ key, value: values[index] }))
      .mapKeys((value) => value.key)
      .mapValues((value) => value.value)
      .value();

    return result;
  }

  // 传入PullData、ipTimeSlots，进行标记，并返回
  markPullData(data: any, preIpMarkTimeSlot: Record<string, string>, { mergeTimeSlot }) {
    function getMark(time: string) {
      return dayjs(time).format('YYYYMMDDHHmm');
    }

    return chain(data)
      .groupBy('ip')
      .map((items, ip) => {
        let timeSlot = preIpMarkTimeSlot[`ads:pull:time-slot:${ip}`];
        items = sortBy(items, this.timeSlotKey);
        items.forEach((item) => {
          if (isEmpty(timeSlot)) {
            timeSlot = getMark(item[this.timeSlotKey]);
            item.timeSlot = timeSlot;
            return;
          }

          const isNextMark = dayjs(item[this.timeSlotKey]).diff(dayjs(timeSlot), 'minutes') > mergeTimeSlot;

          if (isNextMark) {
            timeSlot = getMark(item[this.timeSlotKey]);
            item.timeSlot = timeSlot;
          } else {
            item.timeSlot = timeSlot;
          }
        });

        return items;
      })
      .flatten()
      .value();
  }

  // 传入标记后的PullData，进行格式化，并返回
  getCalculateFormattedData(markedData: any[], childrenData = []) {
    // 1. 按IP和标记进行分组
    return chain(markedData)
      .groupBy((item) => item.ip + '---' + item.timeSlot)
      .map((items, key) => {
        const ip = key.split('---')[0]; // 取IP
        const timeSlot = key.split('---')[1]; // 取时间标记

        const children = childrenData.filter((item) => item && item.ip === ip);

        if (isExist(children)) {
          items = items.concat(children);
        }

        const maxBpsItems = chain(items)
          .groupBy('source')
          .map((gitems) => maxBy(gitems, (i) => +i.bps))
          .value();

        // 1. 按source进行分组
        const firstTime = minBy(maxBpsItems, 'firstTime')?.['firstTime']; // 取最小的firstTime
        const lastTime = maxBy(items, 'lastTime')?.lastTime; // 取最大的lastTime

        const bps = bigUtils.reduce(maxBpsItems, 'bps');
        const pps = bigUtils.reduce(maxBpsItems, 'pps');
        const type = czLodash.joinUniqStringByArray(items, 'type');
        const source = czLodash.joinUniqStringByArray(items, 'source');
        const mo = czLodash.joinUniqStringByArray(items, 'mo');
        const childrenIds = chain(items)
          .map((item) => item.id)
          .uniq()
          .join(',')
          .value();

        const maxBpsItemIds = chain(maxBpsItems)
          .map((item) => item.id)
          .uniq()
          .join(',')
          .value();

        return {
          ip,
          bps,
          pps,
          type,
          source,
          mo,
          timeSlot,
          lastTime,
          firstTime,
          childrenIds,
          maxBpsItemIds,
        } as any;
      })
      .value();
  }
}

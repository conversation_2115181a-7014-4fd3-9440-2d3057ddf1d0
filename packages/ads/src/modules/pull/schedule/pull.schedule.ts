import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import {
  PullCMIService,
  PullCTGOldService,
  PullCTGService,
  PullDDGService,
  PullDPKService,
  PullJDHKG3Service,
  PullJDLAX1Service,
  PullJDSGP1Service,
} from '../service';
import { SourceEnum } from '../enum';
import { logger } from '@/utils/logger';

@Injectable()
export class PullSchedule {
  // 默认任务间隔
  handlers: Record<string, () => Promise<any>> = {};

  constructor(
    readonly pullCMIService: PullCMIService,
    readonly pullCTGService: PullCTGService,
    readonly pullCTGOldService: PullCTGOldService,
    readonly pullDDGService: PullDDGService,
    readonly pullDPKService: PullDPKService,
    readonly pullJDHKG3Service: PullJDHKG3Service,
    readonly pullJDLAX1Service: PullJDLAX1Service,
    readonly pullJDSGP1Service: PullJDSGP1Service,
  ) {
    this.handlers = {
      [SourceEnum.CMI]: () => this.pullCMIService.handlePull(),
      [SourceEnum.DDG]: () => this.pullDDGService.handlePull(),
      [SourceEnum.DPK]: () => this.pullDPKService.handlePull(),
      [`${SourceEnum.JD}-HKG3`]: () => this.pullJDHKG3Service.handlePull(),
      [`${SourceEnum.JD}-LAX1`]: () => this.pullJDLAX1Service.handlePull(),
      [`${SourceEnum.JD}-SGP1`]: () => this.pullJDSGP1Service.handlePull(),
      [`${SourceEnum.CTG}1`]: () => this.pullCTGOldService.handlePull(),
      [`${SourceEnum.CTG}2`]: () => this.pullCTGService.handlePull(),
    };
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handlePull() {
    await Promise.all(
      Object.keys(this.handlers).map(async (source) => {
        try {
          await this.handlers[source]?.();
        } catch (error) {
          logger.error(`拉取平台${source}异常，错误信息：${error.message}`);
        }
      }),
    );
  }
}

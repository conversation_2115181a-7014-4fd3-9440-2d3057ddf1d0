import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';

import * as BigJS from 'big.js';
import { Repository } from 'typeorm';
import { chain, isUndefined, pick } from 'lodash';

import { isEmpty, isExist } from '@/utils/types';
import { czDayjs } from '@/utils/dayjs';
import { logger } from '@/utils/logger';
import { requestOPSClient } from '@/utils/http/axios';
import { ConfService } from '@/modules/conf/conf.service';

import { PullFormattedService } from '../service/pull-formatted.service';
import { PullFlowFormatted } from '../entities/pull_flow_formatted';
import { IpAddressAndSubnetService } from '@/modules/sync-ops/services/get';

@Injectable()
export class PullFormattedDataAdditionalInfoSchedule {
  // 默认任务间隔

  confKey = 'maxFormattedIdWithExtras';

  constructor(
    @InjectRepository(PullFlowFormatted)
    private pullFormattedRepository: Repository<PullFlowFormatted>,
    private confService: ConfService,
    private pullFormattedService: PullFormattedService,
    private ipAddressAndSubnetService: IpAddressAndSubnetService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handler() {
    try {
      await this.handleSyncExtraInfo();
    } catch (error) {
      logger.error(`增加客户等信息到ADS攻击汇总失败，${error.message}`);
    }
  }

  async handleSyncExtraInfo() {
    const additionInfoId = await this.confService.getValue(this.confKey);

    if (isUndefined(additionInfoId)) return;

    const maxFormattedIdWithExtras = new BigJS(additionInfoId);
    const minFormattedId = new BigJS(maxFormattedIdWithExtras.toNumber());
    const maxFormattedId = new BigJS(await this.pullFormattedService.getMaxId());

    if (maxFormattedId.eq(minFormattedId)) return;

    const data = await this.pullFormattedRepository
      .createQueryBuilder()
      .select(['id', 'ip'])
      .where('id > :minId && id <= :maxId', {
        minId: minFormattedId.toNumber(),
        maxId: maxFormattedId.toNumber(),
      })
      .getRawMany();

    if (isEmpty(data)) return;

    const formattedItemWithIp = this.getIpItems(data);
    const formattedItemWithSubnet = this.getSubnetItem(data);

    const [ipAddressResult, ipSubnetResult] = await Promise.all([
      this.ipAddressAndSubnetService.getIpAddressAndIpSubnetList(
        formattedItemWithIp.map((item) => {
          return item.ip;
        }),
      ),
      this.ipAddressAndSubnetService.getIpSubnetList(
        formattedItemWithSubnet.map((item) => {
          return item.ip;
        }),
      ),
    ]);

    const updateIpAddressList = formattedItemWithIp.map((item) => {
      const ipAddressItem = ipAddressResult.find((ipAddress) => ipAddress.ipAddress === item.ip);
      if (!ipAddressItem) return;
      return {
        id: item.id,
        customer: ipAddressItem.customerNo,
        serviceNo: ipAddressItem.serviceNo,
        location: ipAddressItem.location,
        description: ipAddressItem.description,
      };
    });

    const updateIpSubnetList = formattedItemWithSubnet.map((item) => {
      const ipSubnetItem = ipSubnetResult.find((ipSubnet) => item.ip.startsWith(ipSubnet.subnet));
      if (!ipSubnetItem) return;
      return {
        id: item.id,
        customer: ipSubnetItem.customerNo,
        serviceNo: ipSubnetItem.serviceNo,
        location: ipSubnetItem.location,
      };
    });

    const updateData = [...updateIpAddressList, ...updateIpSubnetList].filter((item) => isExist(item));

    if (updateData.length === 0) return;

    await this.pullFormattedRepository.manager.transaction(async (runner) => {
      return Promise.all(
        updateData.map((item) => {
          return runner.update(
            PullFlowFormatted,
            item.id,
            pick(item, ['customer', 'location', 'serviceNo', 'description']),
          );
        }),
      );
    });

    await this.confService.setValue(this.confKey, maxFormattedId.toString());
  }

  getSubnetItem(items: PullFlowFormatted[]) {
    return items
      .filter((item) => {
        return item.ip.endsWith('.0') || item.ip.includes('/');
      })
      .map((item) => {
        if (item.ip.includes('/')) item.ip = item.ip.split('/')?.[0];
        return item;
      });
  }

  getIpItems(items: PullFlowFormatted[]) {
    return items.filter((item) => !item.ip.endsWith('.0') && !item.ip.includes('/'));
  }
}

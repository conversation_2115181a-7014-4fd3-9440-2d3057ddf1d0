import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Observable } from 'rxjs';
import { isEmpty } from './utils/types';

@Injectable()
export class AppAuthGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    return this.validateRequest(request);
  }

  validateRequest(request: any) {
    const authorization = request.headers.authorization;
    if (isEmpty(authorization) || process.env.AUTH_TOKEN !== authorization) {
      throw new UnauthorizedException();
    }

    return true;
  }
}

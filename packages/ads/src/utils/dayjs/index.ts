// utils/dayjs.js
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

// 设置默认时区为上海
dayjs.tz.setDefault('Asia/Shanghai');

class CZDayjs {
  // 当前时间
  current(format = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs().format(format);
  }

  // 传入时间戳，格式化格式
  formatUnix(unix, format = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs.unix(unix).format(format);
  }

  // 传入时间，计算为时间戳
  calculateUnix(value?) {
    return dayjs(value).unix();
  }

  dayjs;
}

const czDayjs = new CZDayjs();
export { dayjs, czDayjs };
export default dayjs;

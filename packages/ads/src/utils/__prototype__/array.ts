// type Predicate = <T, R>(item: T, index: number, array: T[]) => R;
interface Predicate<T, R> {
  (item: T, index: number, array: T[]): Promise<R>;
}

Array.prototype.findAsync = async function <T = any>(predicate: Predicate<T, T | undefined>) {
  for (const [index, item] of (this as T[]).entries()) {
    const result = await predicate(item, index, this);
    if (result) return item;
  }
};

Array.prototype.everyAsync = async function <T = any>(predicate: Predicate<T, boolean>) {
  for (const [index, item] of (this as T[]).entries()) {
    const result = await predicate(item, index, this);
    if (!result) return false;
  }
  return true;
};

// Test
// (async function setup() {
//   const arr = [1, 2, 3, 4, 5];
//   const result = await arr.findAsync(async (item) => {
//     return item === 3;
//   });
//   console.log(result);
//   const result2 = await arr.everyAsync(async (item) => {
//     return item < 6;
//   });
//   console.log(result2);
// })();

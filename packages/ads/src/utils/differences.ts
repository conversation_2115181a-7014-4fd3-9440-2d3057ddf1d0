import { differenceBy, intersectionBy, isString } from 'lodash';

export function getDifferences<T>(newRows: T[], oldRows: T[], iteratee: any = 'id') {
  const result: any = {};

  // 获取需要删除的 item
  // 通过 id 比较
  // newRows 中没有的，但是 oldRows 中有的
  const dItems = differenceBy(oldRows, newRows, iteratee);

  // 获取需要增加的 item
  // 通过 id 比较
  // oldRows 中没有的，但是 newRows 中有的
  const aItems = differenceBy(newRows, oldRows, iteratee);

  // 获取需要更新的 item
  // 通过 id 比较
  // 两个数组中都有的
  const uItems = intersectionBy(newRows, oldRows, iteratee);

  result.dItems = dItems;
  result.aItems = aItems;
  result.uItems = uItems;

  if (isString(iteratee)) {
    result.dKeys = dItems.map((item) => item[iteratee]);
  }

  return result;
}

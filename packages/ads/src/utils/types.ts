import { isArray, isObject } from 'lodash';

export { isEqual, isArray, isString, isNumber, isDate } from 'lodash';

export const isEmpty = (val: unknown) =>
  (!val && val !== 0) || (isArray(val) && (val as any[]).length === 0) || (isObject(val) && !Object.keys(val).length);

export const isExist = (value: any) => !isEmpty(value);

export function isNumeric(value) {
  return typeof value === 'number' || (typeof value === 'string' && /^\s*-?\d+(\.\d+)?\s*$/.test(value));
}

import * as BigJs from 'big.js';
import { isEmpty } from './types';
import { isNaN, isString } from 'lodash';

/// 定义获取规则的函数

const getConversionFactor = (suffix) => {
  const rules = {
    k: 1000,
    g: 1000000000,
    m: 1000000,
  };
  return rules[suffix.toLowerCase()];
};

const getSuffix = (num) => {
  if (num.gte(1000000000)) {
    return { value: num.div(1000000000).toFixed(2), suffix: 'G' };
  } else if (num.gte(1000000)) {
    return { value: num.div(1000000).toFixed(2), suffix: 'M' };
  } else if (num.gte(1000)) {
    return { value: num.div(1000).toFixed(2), suffix: 'K' };
  } else {
    return { value: num.toFixed(2), suffix: '' };
  }
};

class BigUtils {
  toNumber(n: number) {
    return isNaN(+n) ? 0 : +n;
  }

  reduce(arr: any[], path?: string) {
    // 如果 arr 为空，则返回 0
    if (isEmpty(arr)) return 0;

    // 如果 path 为空，则返回 arr 的和
    if (!path) {
      return arr
        .reduce((acc, cur) => {
          return acc.plus(this.toNumber(cur));
        }, new BigJs(0))
        .toNumber();
    }

    // 返回 arr 中 path 的和
    return arr.reduce((acc, cur) => acc.plus(this.toNumber(cur?.[path]) || 0), new BigJs(0)).toNumber();
  }

  /**
   * 转化M、G（不区分大小写）结尾的字符为B
   * 例如：100M => 100000000
   * @param str
   * @returns
   */
  convertMetricSuffix(str: string) {
    if (!isString(str)) return str;

    // 获取字符串的最后一个字符
    const lastChar = str.slice(-1);

    // 检查最后一个字符是否是数字
    if (/\d/.test(lastChar)) {
      // 如果是数字，直接返回原字符串
      return str;
    }

    // 获取转换因子
    const factor = getConversionFactor(lastChar);

    // 如果存在有效的转换因子
    if (factor !== undefined) {
      // 去掉最后一个字符
      const numberPart = str.slice(0, -1);

      // 尝试将去掉最后一个字符后的部分转换为 Big 数字
      try {
        const number = new BigJs(numberPart);
        return number.times(factor).toFixed(2);
      } catch (e) {
        // 如果转换失败，返回原字符串
        return str;
      }
    }

    // 如果不符合条件，返回原字符串
    // 示例
    // console.log(convertMetricSuffix('123g')); // 123000000
    // console.log(convertMetricSuffix('45m')); // 45000
    // console.log(convertMetricSuffix('78d')); // 78d
    // console.log(convertMetricSuffix('100')); // 100
    // console.log(convertMetricSuffix('15s')); // 15s
    // console.log(convertMetricSuffix('abcG')); // abcG
    return str;
  }

  /**
   * 转化为M、G（不区分大小写）结尾的字符为B
   * 例如：100000 => 100K
   * @param input
   * @returns
   */
  convertToMetricSuffix(input: number | string) {
    const str = typeof input === 'number' ? input.toString() : input;

    // 检查输入是否为字符串
    if (typeof str !== 'string') return input;

    try {
      const num = new BigJs(str);
      const { value, suffix } = getSuffix(num);
      return `${value}${suffix}`;
    } catch (e) {
      // 如果转换失败，返回原输入
      return input;
    }
  }
}

export const bigUtils = new BigUtils();

// 示例
// console.log(bigUtils.convertMetricSuffix('123g')); // 123000000
// console.log(bigUtils.convertMetricSuffix('45m')); // 45000
// console.log(bigUtils.convertMetricSuffix('78d')); // 78d
// bigUtils.convertMetricSuffix('100'); // 100
// console.log(bigUtils.convertMetricSuffix('15s')); // 15s
// console.log(bigUtils.convertMetricSuffix('abcG')); // abcG

export default bigUtils;

import * as cheerio from 'cheerio';

export function getParsedMessage(msg) {
  const { payload } = msg || {};
  let body = '';
  const headers = payload.headers || [];

  const subject = headers.find((header) => header.name === 'Subject')?.value || 'No Subject';
  const from = headers.find((header) => header.name === 'From')?.value || 'Unknown Sender';
  const date = headers.find((header) => header.name === 'Date')?.value || 'Unknown Date';

  if (payload.parts) {
    for (let i = 0; i < payload.parts.length; i++) {
      if (payload.parts[i].mimeType === 'text/plain') {
        body = Buffer.from(payload.parts[i].body.data, 'base64').toString('utf8');
        break;
      } else if (payload.parts[i].mimeType === 'text/html') {
        body = Buffer.from(payload.parts[i].body.data, 'base64').toString('utf8');
      }
    }
  } else {
    body = Buffer.from(payload.body.data, 'base64').toString('utf8');
  }

  // 使用 cheerio 提取纯文本
  const cleanText = cleanHtml(body);

  return {
    subject,
    from,
    date,
    body: cleanText,
  };
}

function cleanHtml(html) {
  const $ = cheerio.load(html);
  const text = $.text();
  return text
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .join('\n');
}

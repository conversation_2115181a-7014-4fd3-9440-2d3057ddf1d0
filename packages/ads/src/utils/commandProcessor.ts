// commandProcessor.js

// 判断指令是否失败
function checkFailure(output, errorStrings = []) {
  errorStrings = ['error', 'invalid'].concat(errorStrings);

  const lowerCaseOutput = output.toLowerCase();
  return errorStrings.some((errorString) => lowerCaseOutput.includes(errorString.toLowerCase()));
}

export function extractAllIdsWithPrefix(str: string) {
  // 定义正则表达式匹配所有的 aid 和 itemid 以及前缀
  const regex = /([\s\S]*?)!!!([\s\S]*?)!!!/g;
  let match;
  const results: { prefix: string; isOk: boolean; [key: string]: any }[] = [];

  // 使用 while 循环来查找所有匹配项
  while ((match = regex.exec(str)) !== null) {
    const prefix = match[1].trim();
    const keyValuePairs = match[2].trim();

    // 将键值对解析为对象
    const keyValueObject = {};
    keyValuePairs.split(',').forEach((pair) => {
      const [key, value] = pair.split(':');
      keyValueObject[key.trim()] = value?.trim();
    });

    // 将前缀和键值对对象添加到结果数组中
    results.push({
      prefix,
      isOk: !checkFailure(prefix),
      ...keyValueObject,
    });
  }

  return results;
}

// 示例字符串
// const str = `xxx\nxxx\nxxx\n#Start--abc:123,aid:12345,itemid:67890,extra:abc--End\nyyy\nyyy\nyyy\n#Start--aid:67890,itemid:12345,another:xyz--End\nzzz\nzzz\nzzz\n#Start--aid:11111,itemid:22222,something:else--End`;
// 提取所有 ID 和前缀
// const allIdsWithPrefix = extractAllIdsWithPrefix(str);
// console.log(allIdsWithPrefix);
// 输出:
// [
//     { prefix: 'xxx\nxxx\nxxx', aid: '12345', itemid: '67890', extra: 'abc' },
//     { prefix: 'yyy\nyyy\nyyy', aid: '67890', itemid: '12345', another: 'xyz' },
//     { prefix: 'zzz\nzzz\nzzz', aid: '11111', itemid: '22222', something: 'else' }
// ]

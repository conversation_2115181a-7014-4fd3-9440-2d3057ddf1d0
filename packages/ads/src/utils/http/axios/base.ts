import type { AxiosInstance, AxiosRequestConfig } from 'axios';
import axios from 'axios';

const baseURL = process.env.OPS_REQUEST_URL || 'http://127.0.0.1:8001';
axios.defaults.baseURL = baseURL;

export class DefHttp {
  instance: AxiosInstance;

  constructor(config?: AxiosRequestConfig) {
    this.instance = axios.create(config);

    this.instance.interceptors.request.use(
      (config) => config,
      (err) => err,
    );

    this.instance.interceptors.response.use(
      (response) => {
        if (response.status >= 200 && response.status < 400) {
          return response.data;
        }

        throw Object.assign({}, response, { response });
      },
      (err) => {
        throw err;
      },
    );
  }

  async request(config): Promise<any> {
    return this.instance.request(config);
  }

  async get(url, config: AxiosRequestConfig = {}) {
    config.method = 'get';
    config.url = url;
    return this.request(config);
  }

  async post(url, data = {}, config: AxiosRequestConfig = {}) {
    config.method = 'post';
    config.url = url;
    config.data = data;
    return this.request(config);
  }

  async put(url, data = {}, config: AxiosRequestConfig = {}) {
    config.method = 'put';
    config.url = url;
    config.data = data;
    return this.request(config);
  }

  async delete(url, config: AxiosRequestConfig = {}) {
    config.method = 'delete';
    config.url = url;
    return this.request(config);
  }
}

export const defHttp = new DefHttp({
  baseURL,
  timeout: 10000,
});

import { UnitType } from 'dayjs';

// 根据 unit 处理 ttl
export function formatTTL(ttl: number, unit: UnitType = 'ms') {
  switch (unit) {
    case 'ms':
      return ttl;
    case 's':
    case 'second':
    case 'seconds':
      return ttl * 1000;
    case 'm':
    case 'minute':
    case 'minutes':
      return ttl * 60 * 1000;
    case 'h':
    case 'hour':
    case 'hours':
      return ttl * 60 * 60 * 1000;
    case 'd':
    case 'day':
    case 'days':
      return ttl * 60 * 60 * 24 * 1000;
    default:
      return ttl;
  }
}

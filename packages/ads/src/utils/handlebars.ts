import * as Handlebars from 'handlebars';
import { dayjs } from './dayjs';

// 使用方式：{{getCurrentTime "YYYY-MM-DD HH:mm:ss"}}
Handlebars.registerHelper('getCurrentTime', function (format) {
  return dayjs().format(format);
});

// 使用方式：{{formatUnixTime unitField "YYYY-MM-DD HH:mm:ss"}}
Handlebars.registerHelper('formatUnixTime', function (unixTime, format) {
  return dayjs.unix(unixTime).format(format);
});

// 使用方式：{{calculateUnixTime -1 "m"}}
Handlebars.registerHelper('calculateUnixTime', function (value, unit) {
  // 使用 dayjs 计算当前时间加上指定的值和单位
  const calculatedTime = dayjs().add(value, unit);
  // 返回计算后的 Unix 时间戳
  return calculatedTime.unix();
});

export { Handlebars };
export default Handlebars;

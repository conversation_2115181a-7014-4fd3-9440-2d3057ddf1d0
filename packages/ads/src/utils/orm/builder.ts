import { Brackets, SelectQueryBuilder } from 'typeorm';
import { isArray, isEqual, isNumber, isString } from 'lodash';

export type FieldEq = {
  column: string;
  requestParam: string;
};

export interface JoinOp {
  entity: any;
  alias: string;
  condition: string;
  type?: 'innerJoin' | 'leftJoin';
}

export type QueryOp = {
  keyWordLikeFields?: string[];
  where?: any;
  select?: string[];
  fieldEq?: string[] | FieldEq[];
  addOrderBy?: Record<string, any>;
  join?: JoinOp[];
  extend?: any;
};

type OPSFieldOption = FieldEq & {
  ignore?: any;
};

type FieldOption = string | OPSFieldOption;

export type PageOption = any & {
  fieldEq?: FieldOption[];
  fieldLike?: FieldOption[];
  fieldBetween?: FieldOption[];
  findInSet?: FieldOption[];
};

export type PageResult<T = any> = {
  list: T[];
  pagination: {
    page: number;
    size: number;
    total: number;
  };
};

/**
 * Base ORM
 * TODO: 未完成的
 */

function parse(str: string) {
  const arr = str.split('.');

  if (arr.length === 1) return `\`${arr[0]}\``;

  const alias = arr.shift();
  const column = arr.pop();
  return [alias, column]
    .filter(Boolean)
    .map((i) => `\`${i}\``)
    .join('.');
}

function isIgnore(value: any, ignore: any) {
  return ignore === value || value?.includes?.(ignore) || isEqual(value, ignore);
}

function formatOption(option: FieldOption[]) {
  return option.map((item) => {
    if (typeof item !== 'string') return item;
    return {
      column: item,
      requestParam: item.split('.').pop(),
    };
  });
}

class SelectBuilder {
  handleFieldEq(query: Record<string, any>, fieldEq: FieldOption[], builder: SelectQueryBuilder<any>) {
    const newFieldEq = formatOption(fieldEq);

    if (isArray(newFieldEq) && newFieldEq.length > 0) {
      newFieldEq.forEach((item) => {
        const key = item.requestParam;
        const value = query[item.requestParam];

        if (!value || isIgnore(value, item.ignore)) return;

        if (isArray(value)) {
          builder.andWhere(`${parse(item.column)} IN (:...${key})`, {
            [key]: value,
          });
        } else {
          builder.andWhere(`${parse(item.column)} = :${key}`, {
            [key]: value,
          });
        }
      });
    }
  }

  handleFieldBetween(query: Record<string, any>, fieldBetween: FieldOption[], builder: SelectQueryBuilder<any>) {
    const newFieldBetween = formatOption(fieldBetween);

    if (isArray(newFieldBetween) && newFieldBetween.length > 0) {
      newFieldBetween.forEach((item) => {
        const key = item.requestParam;
        const value = query[item.requestParam];

        if (!value || isIgnore(value, item.ignore)) return;

        if (isArray(value)) {
          const [start, end] = value;
          builder.andWhere(`${parse(item.column)} >= :start && ${parse(item.column)} <= :end `, {
            start,
            end,
          });
        } else {
          builder.andWhere(`${parse(item.column)} <= :date`, {
            date: value,
          });
        }
      });
    }
  }

  handleFieldLike(query: Record<string, any>, fieldLike: FieldOption[], builder: SelectQueryBuilder<any>) {
    const newFieldLike = formatOption(fieldLike);

    if (isArray(newFieldLike) && newFieldLike.length > 0) {
      newFieldLike.forEach((item) => {
        const key = item.requestParam;
        const value = query[item.requestParam];

        if (!value) return;

        builder.andWhere(`${parse(item.column)} LIKE :${key}`, {
          [key]: `%${value}%`,
        });
      });
    }
  }

  handleFindInSet(query: Record<string, any>, findInSet: FieldOption[], builder: SelectQueryBuilder<any>) {
    const newFindInSet = formatOption(findInSet);

    if (isArray(newFindInSet) && newFindInSet.length > 0) {
      newFindInSet.forEach((fieldInSetItem) => {
        const value = query[fieldInSetItem.requestParam];
        const ignoreValue = fieldInSetItem.ignore;
        if (!value) return;
        if (isIgnore(value, ignoreValue)) return;

        const items = isArray(value) ? value : value.split(',');

        builder.andWhere(
          new Brackets((qb) => {
            items.forEach((item) => {
              qb.orWhere(`FIND_IN_SET("${item}", ${parse(fieldInSetItem.column)})`);
            });
          }),
        );
      });
    }
  }

  handleKeyWords(query: Record<string, any>, keyWordLikeFields: string[], builder: SelectQueryBuilder<any>) {
    const value = query.keyWords;

    if (value === undefined || !(isString(value) || isNumber(value)) || !keyWordLikeFields?.length) {
      return builder;
    }

    const values = (value + '')
      .split(' ')
      .map((v) => v.trim())
      .filter((v) => v);

    if (values.length === 0) return builder;

    builder.andWhere(
      new Brackets((b) => {
        values.forEach((v) => {
          keyWordLikeFields.forEach((field) => {
            b.orWhere(`${parse(field)} like '%${v}%'`);
          });
        });
      }),
    );
  }

  handleKeyWordLikeFields(query: Record<string, any>, keyWordLikeFields: string[], builder: SelectQueryBuilder<any>) {
    if (isArray(keyWordLikeFields) && keyWordLikeFields.length > 0) {
      const keyWord = query.keyWord;
      const keyWords = query.keyWords;

      if (keyWords) {
        this.handleKeyWords(query, keyWordLikeFields, builder);
      } else if (keyWord) {
        builder.andWhere(`(${keyWordLikeFields.map((item) => `${parse(item)} LIKE :keyWord`).join(' OR ')})`, {
          keyWord: `%${keyWord}%`,
        });
      }
    }
  }

  builder<T = any>(query: Record<string, any>, option: PageOption, builder: SelectQueryBuilder<T>) {
    const { order, sort = 'DESC' } = query;
    const { keyWordLikeFields, select, fieldEq, fieldLike, fieldBetween, findInSet, join, addOrderBy } = option;

    builder.select(select);

    // 1. 连接查询
    if (isArray(join) && join.length > 0) {
      join.forEach((item) => {
        builder[item.type](item.entity, item.alias, item.condition);
      });
    }

    // 2. 关键字精确查询
    if (isArray(fieldEq) && fieldEq.length > 0) {
      this.handleFieldEq(query, fieldEq, builder);
    }

    if (isArray(fieldBetween) && fieldEq.length > 0) {
      this.handleFieldBetween(query, fieldBetween, builder);
    }

    // 3. 关键字模糊查询
    if (isArray(fieldLike) && fieldLike.length > 0) {
      this.handleFieldLike(query, fieldLike, builder);
    }

    // 4. findInSet
    if (isArray(findInSet) && findInSet.length > 0) {
      this.handleFindInSet(query, findInSet, builder);
    }

    // 4. keyWord
    if (keyWordLikeFields) {
      this.handleKeyWordLikeFields(query, keyWordLikeFields, builder);
    }

    // 5. 排序
    if (order && sort) {
      builder.orderBy(order, sort);
    }

    if (addOrderBy) {
      Object.keys(addOrderBy).forEach((key) => {
        builder.addOrderBy(key, addOrderBy[key]);
      });
    }

    if (option.extend) {
      option.extend(builder);
    }

    return builder;
  }

  async getPageResult<T = any>(
    page: number,
    size: number,
    builder: SelectQueryBuilder<T>,
    useTotal: boolean,
  ): Promise<PageResult<T>> {
    const offset = (page - 1) * size;
    let total: number;

    if (useTotal) {
      total = await builder.getCount();
    } else {
      total = offset + 2 * size;
    }

    const list = await builder.limit(size).offset(offset).getRawMany<T>();

    return {
      list,
      pagination: {
        page,
        size,
        total,
      },
    };
  }

  async page<T = any>(
    query: Record<string, any>,
    option: PageOption,
    builder: SelectQueryBuilder<T>,
    useTotal = true,
  ): Promise<PageResult<T>> {
    const { page = 1, size = 15 } = query;

    const selectBuilder = this.builder(query, option, builder);
    return this.getPageResult(page, size, selectBuilder, useTotal);
  }

  async list(query: Record<string, any>, option: PageOption, builder: SelectQueryBuilder<any>) {
    return this.builder(query, option, builder).getRawMany();
  }

  emptyPageResult(page = 1, size = 20): PageResult {
    return {
      list: [],
      pagination: {
        page,
        size,
        total: 0,
      },
    };
  }
}

export const selectBuilder = new SelectBuilder();

export function defineOption(option: PageOption) {
  return option;
}

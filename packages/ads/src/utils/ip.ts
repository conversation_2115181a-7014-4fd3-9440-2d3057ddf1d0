import { cidrSubnet, isV4Format, fromPrefixLen } from 'ip';

export { isV4Format } from 'ip';

/**
 * 检查一个IP地址是否包含在给定的子网数组中。
 *
 * @param {string} ipAddress - 要检测的IP地址。
 * @param {string[]} subnets - 子网数组，每个子网以CIDR表示法表示。
 * @returns {boolean} 如果IP地址包含在任一子网中，则返回true；否则返回false。
 */
export function isIpInSubnets(ipAddress: string, subnets: string | string[]) {
  if (typeof subnets === 'string') subnets = subnets.split(',');

  for (const subnet of subnets) {
    if (cidrSubnet(subnet).contains(ipAddress)) {
      return true;
    }
  }
  return false;
}

/**
 * 检查一个IP地址是否包含在给定的子网数组中，并返回最匹配的子网。
 *
 * @param {string} ipAddress - 要检测的IP地址。
 * @param {string[]} subnets - 子网数组，每个子网以CIDR表示法表示。
 * @returns {string|null} 返回最匹配的子网，如果没有匹配的子网则返回null。
 */
export function getMostSpecificSubnet(ipAddress, subnets) {
  let mostSpecificSubnet = null;
  let longestPrefixLength = -1;

  for (const subnet of subnets) {
    const subnetInfo = cidrSubnet(subnet);
    if (subnetInfo.contains(ipAddress)) {
      const prefixLength = subnetInfo.subnetMaskLength;
      if (prefixLength > longestPrefixLength) {
        mostSpecificSubnet = subnet;
        longestPrefixLength = prefixLength;
      }
    }
  }

  return mostSpecificSubnet;
}

/**
 * 将 IP 地址转换为表示 /24 子网的地址
 * @param {string} ipadd - 要转换的 IP 地址
 * @returns {string} - 转换后的子网地址
 */
export function toSubnetAddress(ipadd, usePrefixLength = true) {
  // 确保输入的 IP 地址是有效的
  if (!isV4Format(ipadd)) return;

  return ipadd
    .split('.')
    .slice(0, 3)
    .concat(usePrefixLength ? '0/24' : '0')
    .join('.');
}

export function convertSubnetLengthToMask(subnetLength) {
  return fromPrefixLen(subnetLength);
}

/**
 * 判断是否是一个IPv4并且不是subnet
 * @param ipAddress
 * @returns
 */
export function isIPv4AndNotEndsWithZero(ipAddress) {
  // 检查是否是有效的 IPv4 地址
  if (!isV4Format(ipAddress)) {
    return false;
  }

  // 检查是否以 .0 结尾
  if (ipAddress.endsWith('.0')) {
    return false;
  }

  return true;
}

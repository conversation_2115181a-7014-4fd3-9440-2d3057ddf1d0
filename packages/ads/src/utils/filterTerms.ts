import { groupBy, forEach } from 'lodash';
import dayjs from 'src/utils/dayjs';

// function parseTimeRange(value) {
//   const timeRanges = value.split(',');
//   const parsedRanges = timeRanges.map((range) => {
//     const match = range.match(/\[(\d{2}:\d{2})-(\d{2}:\d{2})\](\d+)/);
//     if (match) {
//       return {
//         start: match[1],
//         end: match[2],
//         value: match[3],
//       };
//     }
//     return { value: range };
//   });
//   return parsedRanges;
// }

// function selectValueByTime(obj) {
//   const now = dayjs().tz('Asia/Shanghai'); // 使用北京时间
//   console.log(now.format('HH:mm'));
//   const result = {};

//   for (const key in obj) {
//     const parsedRanges = parseTimeRange(obj[key]);
//     let selectedValue = parsedRanges[parsedRanges.length - 1].value; // 默认值

//     for (const range of parsedRanges) {
//       if (range.start && range.end) {
//         const [startHour, startMinute] = range.start.split(':').map(Number);
//         const [endHour, endMinute] = range.end.split(':').map(Number);

//         const start = now
//           .clone()
//           .hour(startHour)
//           .minute(startMinute)
//           .second(0)
//           .millisecond(0);
//         const end = now
//           .clone()
//           .hour(endHour)
//           .minute(endMinute)
//           .second(0)
//           .millisecond(0);

//         console.log(start.format('HH:mm'), end.format('HH:mm'));

//         if (now.isAfter(start) && now.isBefore(end)) {
//           selectedValue = range.value;
//           break;
//         }
//       }
//     }

//     result[key] = selectedValue;
//   }

//   return result;
// }

// // 示例对象
// const obj = { max: '[12:00-14:00]900,[14:00-17:00]800,100' };

// // 调用函数
// const result = selectValueByTime(obj);
// console.log(result);
function selectValueByTime(arr) {
  const now = dayjs(); // 使用北京时间
  let selectedValue = arr.find(({ start, end }) => !start && !end);

  for (const item of arr) {
    if (item.start && item.end) {
      const [startHour, startMinute] = item.start.split(':').map(Number);
      const [endHour, endMinute] = item.end.split(':').map(Number);

      const start = now.clone().hour(startHour).minute(startMinute).second(0).millisecond(0);
      const end = now.clone().hour(endHour).minute(endMinute).second(0).millisecond(0);

      if (now.isAfter(start) && now.isBefore(end)) {
        selectedValue = item;
        break;
      }
    }
  }

  return selectedValue;
}

export function getBestTermItems(obj, groupByField = 'field') {
  const grouped = groupBy(obj, groupByField);
  const result = [];

  forEach(grouped, (items, field) => {
    if (items.length === 1) {
      result.push(items[0]);
    } else {
      result.push(selectValueByTime(items));
    }
  });

  return result;
}

// 示例数组
// const obj = [
//   { field: 'a', max: 900, start: '9:00', end: '10:00' },
//   { field: 'a', max: 800, start: '10:00', end: '18:00' },
//   { field: 'a', max: 100 },
//   { field: 'b', min: 900, start: '9:00', end: '10:00' },
//   { field: 'b', min: 800, start: '10:00', end: '18:00' },
//   { field: 'b', min: 100 },
// ];
// 调用函数
// const result = filterTermItems(obj);
// console.log(result);

export default getBestTermItems;

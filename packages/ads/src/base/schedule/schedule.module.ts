import { DynamicModule, Module } from '@nestjs/common';
import { ScheduleModule as NestScheduleModule } from '@nestjs/schedule';

const isSchedulerEnabled = process.env.ADS_SCHEDULE_ENABLED === 'true';

@Module({})
export class ScheduleModule {
  static forRoot(): DynamicModule {
    if (!isSchedulerEnabled) {
      return {
        module: ScheduleModule,
        imports: [],
      };
    }

    return {
      module: ScheduleModule,
      imports: [NestScheduleModule.forRoot()],
    };
  }
}

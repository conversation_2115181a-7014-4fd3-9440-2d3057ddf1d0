import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

const type = 'mysql';
const host = process.env.ADS_DB_HOST || '127.0.0.1';
const port = +process.env.ADS_DB_PORT || 3306;
const username = process.env.ADS_DB_USERNAME;
const password = process.env.ADS_DB_PASSWORD;
const database = process.env.ADS_DB_DATABASE;
const synchronize = process.env.ADS_DB_SYNCHRONIZE === 'true';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type,
      host,
      port,
      username,
      password,
      database,
      synchronize,
      autoLoadEntities: true,
      dateStrings: true,
      multipleStatements: true, // 每个查询允许多个 mysql 语句。请注意，它可能会增加 SQL 注入攻击的范围。
      // logging: true,
      connectTimeout: 60000,
    }),
  ],
})
export class MysqlDBModule {}

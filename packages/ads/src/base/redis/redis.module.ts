import { CacheModule } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-yet';
import { Module } from '@nestjs/common';
import { RedisService } from './redis.service';
import { RedisController } from './redis.controller';

const host = process.env.REDIS_HOST;
const port = +process.env.REDIS_PORT;
const password = process.env.REDIS_PASSWORD;
const database = +process.env.REDIS_DATABASE;
const url = `redis://${password ? `:${password}@` : ''}${host}:${port}/${database}`;

@Module({
  imports: [
    CacheModule.registerAsync({
      useFactory: async () => ({
        store: await redisStore({ url }),
        isGlobal: true,
        ttl: 60, // default TTL in seconds
      }),
    }),
  ],
  controllers: [RedisController],
  providers: [RedisService],
  exports: [RedisService],
})
export class RedisModule {}

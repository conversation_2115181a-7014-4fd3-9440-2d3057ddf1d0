import { Inject, Injectable } from '@nestjs/common';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

import { Redis } from 'ioredis';
import { Cache, Store } from 'cache-manager';

import { UnitType } from 'dayjs';
import { formatTTL } from 'src/utils/date/unit';

@Injectable()
export class RedisService {
  private readonly store: Store;
  private readonly redisClient;

  constructor(@Inject(CACHE_MANAGER) private cacheManager: Cache) {
    this.store = this.cacheManager.store;
    this.redisClient = new Redis(+process.env.REDIS_PORT || 6379, process.env.REDIS_HOST || '127.0.0.1', {
      db: +process.env.REDIS_DATABASE || 0,
      password: process.env.REDIS_PASSWORD || '',
    });
  }

  async getMultipleKeysUsingPipeline(keys: string[]) {
    const pipeline = this.redisClient.pipeline();
    keys.forEach((key) => pipeline.get(key));
    const results = await pipeline.exec();
    return results.map((result) => (result[0] ? null : result[1])); // 处理错误和结果
  }

  async setMultipleKeysUsingPipeline(keyValuePairs: { [key: string]: any }, ttl = 0, unit: UnitType = 'ms') {
    const pipeline = this.redisClient.pipeline();
    Object.entries(keyValuePairs).forEach(([key, value]) => {
      pipeline.set(key, value, 'PX', formatTTL(ttl, unit));
    });
    return pipeline.exec();
  }

  /**
   * 设置缓存
   * @param key
   * @param value
   * @param ttl
   * @returns
   */
  async set(key: string, value: any, ttl = 0, unit: UnitType = 'ms') {
    return this.cacheManager.set(key, value, formatTTL(ttl, unit));
  }

  /**
   * 获取缓存
   * @param key
   * @returns
   */
  async get<T = any>(key: string): Promise<T> {
    return this.cacheManager.get(key);
  }

  /**
   * 删除缓存
   * @param key
   * @returns
   */
  async del(key: string) {
    return this.cacheManager.del(key);
  }

  /**
   * 重置缓存
   * @returns
   */
  async reset() {
    return this.cacheManager.reset();
  }

  async keys(pattern: string) {
    return this.cacheManager.store.keys(pattern);
  }

  /**
   * 获取所有key的列表
   * @param pattern
   * @returns
   */
  async mget(pattern: string): Promise<any[]> {
    const keys = await this.store.keys(pattern);
    if (keys.length === 0) return [];
    return this.store.mget(...keys);
  }

  /**
   * 删除所有key
   * @param arguments_
   * @param ttl
   * @returns
   */
  async mdel(pattern: string) {
    const keys = await this.store.keys(pattern);
    if (keys.length === 0) return;
    return this.store.mdel(...keys);
  }
}

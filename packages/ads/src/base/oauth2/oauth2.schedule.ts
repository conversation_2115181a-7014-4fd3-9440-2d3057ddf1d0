import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { logger } from '@/utils/logger';
import { OAuth2Service } from './oauth2.service';

@Injectable()
export class OAuth2Schedule {
  constructor(private readonly oauth2Service: OAuth2Service) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async refreshToken() {
    this.oauth2Service.refreshToken().catch((error) => {
      logger.error(`刷新OAuth2认证失败，错误信息：${error.message}`);
    });
  }
}

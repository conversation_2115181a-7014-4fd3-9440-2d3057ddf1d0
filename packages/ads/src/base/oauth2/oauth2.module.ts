import { Module } from '@nestjs/common';
import { OAuth2Service } from './oauth2.service';
import { OAuth2Controller } from './oauth2.controller';
import { RedisModule } from '../redis/redis.module';
import { OAuth2Schedule } from './oauth2.schedule';

@Module({
  imports: [RedisModule],
  controllers: [OAuth2Controller],
  providers: [OAuth2Schedule, OAuth2Service],
  exports: [OAuth2Service],
})
export class OAuth2Module {}

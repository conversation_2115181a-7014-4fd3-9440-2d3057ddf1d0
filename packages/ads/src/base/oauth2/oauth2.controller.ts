import { Controller, Get, Query, Res } from '@nestjs/common';
import { OAuth2Service } from './oauth2.service';
import { Response } from 'express';

@Controller('oauth2')
export class OAuth2Controller {
  constructor(private readonly authService: OAuth2Service) {}

  @Get('authorize')
  authorize(@Res() res: Response) {
    const url = this.authService.getAuthUrl();
    res.redirect(url);
  }

  @Get('oauth2callback')
  async oauth2callback(@Query('code') code: string, @Res() res: Response) {
    if (code) {
      await this.authService.handleAuthCode(code);
      res.send('Authorization successful! You can close this window.');
    } else {
      res.status(400).send('No authorization code found.');
    }
  }

  @Get('refresh')
  async refresh(@Res() res: Response) {
    try {
      const token = await this.authService.refreshToken();
      res.send({
        token,
        message: 'Token refreshed successfully.',
      });
    } catch (error) {
      res.status(400).send('Error refreshing access token');
    }
  }

  @Get('token')
  async getAccessToken() {
    return this.authService.getAccessToken();
  }

  @Get('labels')
  async getLabels(@Res() res: Response) {
    try {
      const labels = await this.authService.getLabels();
      if (labels.length) {
        res.send('Labels: <br>' + labels.map((label) => label.name).join('<br>'));
      } else {
        res.send('No labels found.');
      }
    } catch (error) {
      res.status(400).send('The API returned an error: ' + error.message);
    }
  }
}

import { Injectable, OnModuleInit } from '@nestjs/common';
import { google } from 'googleapis';
import * as fs from 'fs';
import * as path from 'path';
import { RedisService } from '../redis/redis.service';

@Injectable()
export class OAuth2Service implements OnModuleInit {
  private oAuth2Client;
  private readonly SCOPES = ['https://www.googleapis.com/auth/gmail.readonly'];
  private readonly TOKEN_PATH = path.join(__dirname, 'token.json');
  private readonly CREDENTIALS_PATH = path.join(__dirname, 'credentials.json');

  constructor(private readonly redisService: RedisService) {}

  async onModuleInit() {
    try {
      const content = fs.readFileSync(this.CREDENTIALS_PATH, 'utf-8');
      const credentials = JSON.parse(content);
      const { client_secret, client_id, redirect_uris } = credentials.installed;
      this.oAuth2Client = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);
    } catch (err) {
      console.error(`OAUTH2 - onModuleInit - Error: ${err.message}`);
    }
  }

  getAuthUrl() {
    return this.oAuth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: this.SCOPES,
    });
  }

  async handleAuthCode(code: string) {
    const { tokens } = await this.oAuth2Client.getToken(code);
    this.oAuth2Client.setCredentials(tokens);
    fs.writeFileSync(this.TOKEN_PATH, JSON.stringify(tokens));
    this.redisService.set('ads:oauth2:token', tokens);
    return tokens;
  }

  async refreshToken() {
    const originToken = fs.readFileSync(this.TOKEN_PATH, 'utf-8');
    this.oAuth2Client.setCredentials(JSON.parse(originToken));
    const { credentials: token } = await this.oAuth2Client.refreshAccessToken();
    fs.writeFileSync(this.TOKEN_PATH, JSON.stringify(token));
    this.redisService.set('ads:oauth2:token', token);
    return token;
  }

  async getLabels() {
    const token = fs.readFileSync(this.TOKEN_PATH, 'utf-8');
    this.oAuth2Client.setCredentials(JSON.parse(token));
    const gmail = google.gmail({ version: 'v1', auth: this.oAuth2Client });
    const res = await gmail.users.labels.list({ userId: 'me' });
    return res.data.labels;
  }

  async getAccessToken() {
    return this.redisService.get('ads:oauth2:token');
  }
}

// bull.module.ts
import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';

const host = process.env.REDIS_HOST;
const port = +process.env.REDIS_PORT;
const password = process.env.REDIS_PASSWORD;
const db = +process.env.REDIS_DATABASE;

@Module({
  imports: [
    BullModule.forRoot({
      redis: {
        host,
        port,
        db,
        password,
      },
    }),
  ],
})
export class BullQueueModule {}

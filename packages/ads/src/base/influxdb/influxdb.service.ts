import type { DayjsDate } from 'types';
import { Injectable } from '@nestjs/common';
import { InfluxDB, Point } from '@influxdata/influxdb-client';
import { DeleteAPI } from '@influxdata/influxdb-client-apis';

import { chain } from 'lodash';
import * as dayjs from 'dayjs';

const org = process.env.INFLUXDB_ORG;
const bucket = process.env.INFLUXDB_BUCKET;
const token = process.env.INFLUXDB_TOKEN;
const url = process.env.INFLUXDB_URL;

@Injectable()
export class InfluxDBService {
  private readonly influxdb: InfluxDB;

  constructor() {
    this.influxdb = new InfluxDB({ url, token });
  }

  // 写入influxdb
  async write(callback: (point: typeof Point) => Point) {
    const writeApi = this.influxdb.getWriteApi(org, bucket, 'ns');

    const point = callback(Point);

    try {
      writeApi.writePoint(point);
      await writeApi.close();
      return 'success';
    } catch (e) {
      return e.message;
    }
  }

  async query(measurement: string, param) {
    const queryAPI = this.influxdb.getQueryApi(org);

    const start = dayjs().subtract(1, 'day').toISOString();
    const stop = dayjs().toISOString();
    const filterResult = chain(param || {})
      .mapValues((v, k) => `r.${k} == "${v}"`)
      .map((v) => v)
      .join(' and ')
      .value();

    console.log(filterResult);

    const query = `
      from(bucket: "${bucket}")
        |> range(start: ${start}, stop: ${stop})
        |> filter(fn: (r) => r._measurement == "${measurement}" ${filterResult ? `and ${filterResult}` : ''})
    `;

    const rows = [];

    return new Promise((resolve, reject) => {
      queryAPI.queryRows(query, {
        next(row, tableMeta) {
          const o = tableMeta.toObject(row);
          rows.push(o);
        },
        error(error) {
          reject(error);
        },
        complete() {
          resolve(rows);
        },
      });
    });
  }

  async delete(measurement: string, start: DayjsDate, stop: DayjsDate) {
    start = dayjs(start).toISOString();
    stop = dayjs(stop).toISOString();

    const deleteAPI = new DeleteAPI(this.influxdb);
    return deleteAPI.postDelete({
      org,
      bucket,
      body: {
        start,
        stop,
        predicate: `_measurement="${measurement}"`,
      },
    });
  }
}

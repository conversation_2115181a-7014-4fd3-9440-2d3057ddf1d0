# python3 -m venv myenv
# source myenv/bin/activate
# pip install netmiko paramiko pyinstaller

import warnings
from cryptography.utils import CryptographyDeprecationWarning

warnings.filterwarnings(action='ignore', category=CryptographyDeprecationWarning)

from netmiko import ConnectHandler
import sys

def main():
    if len(sys.argv) < 6:
        print("Usage: python script.py <device_type> <host> <username> <password> <commands> [<optional_params>...]")
        sys.exit(1)

    device_type = sys.argv[1]
    host = sys.argv[2]
    username = sys.argv[3]
    password = sys.argv[4]
    commands = sys.argv[5]
    optional_params = sys.argv[6:]

    device = {
        'device_type': device_type,
        'host': host,
        'username': username,
        'password': password,
        'fast_cli': False,  # 如果需要更快的CLI响应，可以设置为True
    }

    # 添加可选的连接参数
    for param in optional_params:
        key, value = param.split('=')
        device[key] = value

    try:
        net_connect = ConnectHandler(**device)

        # 检查设备类型是否为 juniper_junos
        if device['device_type'] == 'juniper_junos':
          # 进入 config private 模式
          net_connect.send_command('configure private')

        output = net_connect.send_config_set(commands.split(';'))
        print(output)

        # 提交配置
        if device['device_type'] == 'juniper_junos':
            net_connect.commit()

        net_connect.disconnect()
    except Exception as e:
        print(f"Failed to connect or execute commands: {e}")

if __name__ == "__main__":
    main()

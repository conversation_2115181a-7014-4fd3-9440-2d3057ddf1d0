const { exec } = require('child_process');

const deviceType = 'cisco_ios'; // 替换为你的设备类型
const host = '127.0.0.1'; // 替换为你的设备IP
const username = 'Username'; // 替换为你的用户名
const password = '123456'; // 替换为你的密码

// 使用分号分隔多条命令
const commandsToExecute = 'config t;ip route *********** *************** null 0 tag 4444;end';

const optionalParams = [
  // 'KexAlgorithms=diffie-hellman-group-exchange-sha256',
  // 'HostKeyAlgorithms=ssh-rsa',
  // 'Ciphers=aes128-ctr',
]; // 替换为你的可选参数

const pythonScript = './netmiko_script';
const command = `${pythonScript} ${deviceType} ${host} ${username} ${password} "${commandsToExecute}" ${optionalParams.join(' ')}`;

exec(command, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error: ${error.message}`);
    return;
  }

  if (stderr) {
    console.error(`Stderr: ${stderr}`);
    return;
  }

  console.log(`Output: ${stdout}`);
});

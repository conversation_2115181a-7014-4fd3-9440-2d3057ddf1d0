import { Injectable } from '@nestjs/common';
import { exec } from 'child_process';
import { promisify } from 'util';
import { Device } from 'src/modules/device/entities/device.entity';
import { resolve } from 'path';

const execPromise = promisify(exec);

@Injectable()
export class NetmikoService {
  constructor() {}

  async executeNetmikoScript(device: Partial<Device>, command: string) {
    const { type, host, username, password } = device;
    const netmikoScriptPath = resolve(__dirname, './netmiko_script');
    command = `${netmikoScriptPath} ${type} ${host} ${username} ${password} "${command}"`;

    try {
      const { stdout, stderr } = await execPromise(command);
      if (stderr) {
        throw new Error(`Stderr: ${stderr}`);
      }
      return stdout;
    } catch (error) {
      throw new Error(`Error: ${error.message}`);
    }
  }
}

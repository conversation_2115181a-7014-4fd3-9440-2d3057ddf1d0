import { Injectable } from '@nestjs/common';
import { extractAllIdsWithPrefix } from 'src/utils/commandProcessor';
import { adsHttp } from 'src/utils/http/axios';
import { ActionLogService } from './service/action-log.service';
import { NetmikoService } from 'src/base/netmiko/netmiko.service';

/**
 *
 */
@Injectable()
export class CommonService {
  constructor(
    private readonly netmikoService: NetmikoService,
    readonly actionLogService: ActionLogService,
  ) {}

  async executeScript(device: any, commands: string) {
    const result = await this.netmikoService.executeNetmikoScript(device, commands);

    return extractAllIdsWithPrefix(result);
  }

  async sendActionDiscord(content: string) {
    adsHttp.sendDiscord(content).catch((err) => {
      console.error(err);
    });
  }
}

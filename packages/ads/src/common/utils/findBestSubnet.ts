import { cidrSubnet } from 'ip';

/**
 * 在subnets中匹配targetIP最精确的子网并返回，为空返回null
 * @param targetIp
 * @param subnets
 * @returns
 */
export function findBestSubnet(targetIp: string, subnets: string[]) {
  let mostSpecificSubnet = null;
  let longestMaskLength = -1;

  for (const subnet of subnets) {
    const subnetInfo = cidrSubnet(subnet);
    if (subnetInfo.contains(targetIp)) {
      const maskLength = subnetInfo.subnetMaskLength;
      if (maskLength > longestMaskLength) {
        mostSpecificSubnet = subnet;
        longestMaskLength = maskLength;
      }
    }
  }

  return mostSpecificSubnet;
}

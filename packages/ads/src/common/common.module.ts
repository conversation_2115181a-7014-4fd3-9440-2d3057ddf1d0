import { Modu<PERSON> } from '@nestjs/common';
import { RedisModule } from 'src/base';
import { CommonService } from './common.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DeviceModule } from 'src/modules/device/device.module';
import { ActionLog } from 'src/modules/action/entities/action-log.entity';
import { PreciseSubnetService } from './service/precise-subnet.service';
import { IPConfig } from 'src/modules/ip-config/entities/ip-config.entity';
import { ActionLogService } from './service/action-log.service';
import { NetmikoModule } from 'src/base/netmiko/netmiko.module';
import { ActionCommonService } from './service/action.service';

@Module({
  imports: [TypeOrmModule.forFeature([ActionLog, IPConfig]), RedisModule, NetmikoModule, DeviceModule],
  providers: [CommonService, PreciseSubnetService, ActionCommonService, ActionLogService],
  exports: [CommonService, ActionCommonService, PreciseSubnetService],
})
export class CommonModule {}

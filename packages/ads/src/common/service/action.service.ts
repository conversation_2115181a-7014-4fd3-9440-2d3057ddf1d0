import { Injectable } from '@nestjs/common';

import { chain, merge } from 'lodash';
import { DataSource } from 'typeorm';

import { convertSubnetLengthToMask } from '@/utils/ip';
import { isEmpty, isExist } from '@/utils/types';
import { generateHash } from '@/utils/uuid';
import { dayjs } from '@/utils/dayjs';
import { adsHttp } from '@/utils/http/axios';

import { ActionPolicyItem } from '@/modules/action/entities/action-policy-item.entity';
import { ActionTypeEnum } from '@/modules/action/enums/action-type.enum';
import { Device } from '@/modules/device/entities/device.entity';
import { ActionLog } from '@/modules/action/entities/action-log.entity';
import { AbnormalCurrent, AbnormalCurrentStatusEnum } from '@/modules/abnormal/entities';
import { replacePlaceholders } from '@/modules/action/utils/replace-text';
import { CommonService } from '../common.service';
import { logger } from '@/utils/logger';

export type ExecuteAbnormalOptions = {
  ipSeparator?: string;
  splitIpToAddressAndLengthSeparator?: string;
  useCreateAbnormalCurrent?: boolean;
  useCreateActionLog?: boolean;
  useSendItemExecuteErrorContent?: boolean;
  formatterCurrentAbnormal?: (
    curAb: AbnormalCurrent,
    cmdItem: CommandItem,
    actionItem: ActionPolicyItem,
    abnormal: AbnormalItem,
  ) => AbnormalCurrent;
};

const defaultAbnormalExecuteOptions: ExecuteAbnormalOptions = {
  ipSeparator: ',',
  splitIpToAddressAndLengthSeparator: '/',
  useCreateAbnormalCurrent: true,
  useCreateActionLog: true,
  useSendItemExecuteErrorContent: true,
};

@Injectable()
export class ActionCommonService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly commonService: CommonService,
  ) {}

  /**
   * 执行异常列表操作
   * @param abnormalList 异常列表
   * @param options 执行配置
   */
  async executeAbnormalList(
    abnormalList: AbnormalItem[],
    options?: ExecuteAbnormalOptions,
  ): Promise<(AbnormalItem & { result?: ExecuteResult[]; isOk?: boolean })[]> {
    // 合并配置
    options = merge({}, defaultAbnormalExecuteOptions, options);

    const actionItems = await this.bindActionItems(abnormalList);

    // 执行SSH操作
    const executeResults: ExecuteResult[] = await this.executeSSH(abnormalList, options);

    // 如果存在ssh操作结果
    if (isExist(executeResults)) {
      // 新增操作日志
      if (options.useCreateActionLog) {
        this.createCurrentActionLog(executeResults, actionItems);
      }

      // 发送项目操作失败的Discord消息
      if (options.useSendItemExecuteErrorContent) {
        this.sendItemExecuteErrorDiscord(executeResults, actionItems, abnormalList);
      }
    }

    // 新增当前异常日志;
    if (options.useCreateAbnormalCurrent) {
      this.createCurrentAbnormal(executeResults, actionItems, abnormalList, options);
    }

    // 发送Discord操作消息
    this.sendActionExecuteSuccessDiscord(executeResults, abnormalList);

    return abnormalList.map((abnormal) => {
      if (isEmpty(executeResults)) return abnormal;
      const curExecResults = executeResults.filter((r) => {
        return +r.cmdItem?.id === abnormal.id;
      });

      const existError = curExecResults.some((r) => !r.isOk);
      return { ...abnormal, result: curExecResults, isOk: !existError };
    });
  }

  // 绑定操作项目
  async bindActionItems(abnormalList: AbnormalItem[]) {
    const actionIds = abnormalList.map((i) => i.actionId);

    const actionItems = await this.dataSource
      .createQueryBuilder()
      .select()
      .from(ActionPolicyItem, 'a')
      .where('a.actionPolicyId IN (:...actionIds)', { actionIds })
      .getRawMany<ActionPolicyItem>();

    abnormalList.forEach((abnormal) => {
      abnormal.actionItems = actionItems.filter((item) => {
        return item.actionPolicyId === abnormal.actionId;
      });
    });

    return actionItems;
  }

  // 执行ssh操作项目
  async executeSSH(abnormalList: AbnormalItem[], options?: ExecuteAbnormalOptions) {
    const commandItems: CommandItem[] = chain(abnormalList)
      .map((abnormal) => {
        // 获取当前异常项目ssh操作
        const sshItems = abnormal.actionItems.filter((item) => {
          return item.actionType === ActionTypeEnum.SSH_CMD;
        });

        if (isEmpty(sshItems)) return;

        const children = abnormal.isGroup
          ? abnormal.children
          : abnormal.ip.split(options.ipSeparator).map((ip) => ({ ip }) as any);

        return sshItems.map((item) => {
          const { deviceId, actionScript } = item;
          const script = actionScript.replace(/\n/g, ';');
          return children.map(({ id: cid, ip }) => {
            const id = abnormal.id;
            const aid = item.actionPolicyId;
            const itemid = item.id;
            const [address, length] = ip.split(options.splitIpToAddressAndLengthSeparator);

            const cmd = replacePlaceholders(script, {
              targetIp: address,
              targetSubnetMask: length ? convertSubnetLengthToMask(length) : '',
            });

            const uuid = generateHash('' + id + aid + itemid + ip);
            return {
              id,
              cid,
              ip,
              uuid,
              aid,
              itemid,
              did: deviceId,
              cmd: `${cmd};!!!uuid:${uuid}!!!`,
            } as CommandItem;
          });
        });
      })
      .filter((item) => isExist(item))
      .flattenDeep()
      .value();

    if (isEmpty(commandItems)) return;

    const devices = await this.dataSource
      .createQueryBuilder(Device, 'a')
      .select('a.*')
      .where('a.id IN (:...ids)', { ids: commandItems.map((i) => i.did) })
      .getRawMany<Device>();

    const resultsPromises = chain(commandItems)
      .groupBy('did')
      .map(async (cmdItems, deviceId) => {
        const device = devices.find((device) => device.id === +deviceId);
        const commands = cmdItems.map((item) => item.cmd).join(';');
        let result;
        try {
          result = await this.commonService.executeScript(device, commands);
        } catch (error) {
          console.log(error.message);
          return;
        }

        return result
          .map((result) => {
            const cmdItem = commandItems.find((item) => {
              return item.uuid === result.uuid;
            });
            return { ...result, cmdItem } as ExecuteResult;
          })
          .filter((item) => isExist(item.cmdItem));
      })
      .value();

    const results = await Promise.all(resultsPromises);

    return chain(results)
      .flattenDeep()
      .filter((result) => isExist(result))
      .value();
  }

  /**
   * 根据指令结果集创建操作日志
   * @param executeResultList 执行指令结果集
   * @param actionItems 操作策略项目
   * @returns
   */
  async createCurrentActionLog(executeResultList: ExecuteResult[], actionItems: ActionPolicyItem[]) {
    if (isEmpty(executeResultList)) return;

    const actionLogs = executeResultList.map((result) => {
      const { isOk, prefix, cmdItem } = result;
      const { did, ip, cmd } = cmdItem;
      const actionItem = actionItems.find((item) => {
        return item.id === +cmdItem.itemid;
      });
      return {
        deviceId: did,
        ipadd: ip,
        actionResult: prefix,
        actionDetail: cmd,
        status: isOk,
        actionItemId: actionItem.id,
      } as ActionLog;
    });

    this.dataSource.createQueryBuilder().insert().into(ActionLog).values(actionLogs).execute();
  }

  /**
   * 根据指令结果集创建当前异常项目
   * @param executeResultList 执行指令结果集
   * @param actionItems 操作策略项目
   * @param historyList 操作对象集
   * @returns
   */
  async createCurrentAbnormal(
    executeResultList: ExecuteResult[],
    actionItems: ActionPolicyItem[],
    abnormalList: AbnormalItem[],
    options: ExecuteAbnormalOptions,
  ) {
    // 判断是否存在ssh执行结果，如果不存在意味着操作只有消息的发送
    const isExistExecuteResult = isExist(executeResultList);

    try {
      await this.dataSource.transaction(async (runner) => {
        return Promise.all(
          // 遍历异常，将处理的异常增加到当前异常表中
          chain(abnormalList)
            .map(async (a) => {
              // 公用属性
              let ac = {
                ipadd: a.ip,
                upstream: a.values?.upstream,
                term: a.term,
                values: a.values,
                actionPolicyId: a.actionId,
                analyzePolicyId: a.analyzeId,
                isGroup: a.isGroup,
              } as AbnormalCurrent;

              let children: AbnormalCurrent[];

              if (isExistExecuteResult && a.isGroup) {
                // 存在执行ssh结果并且异常记录为组的类型
                children = executeResultList
                  .filter((r) => r.isOk && r.cmdItem?.id === a.id)
                  .map((r) => {
                    const { ip, itemid, cid } = r.cmdItem;

                    const actionItem = actionItems.find((i) => i.id == itemid);

                    const childrenItem = a.children.find((c) => c.id == cid);

                    if (isEmpty(actionItem) || isEmpty(childrenItem)) return;

                    return {
                      ipadd: ip,
                      upstream: childrenItem.values?.upstream,
                      term: childrenItem.term,
                      values: childrenItem.values,
                      actionPolicyId: a.actionId,
                      analyzePolicyId: a.analyzeId,
                      isGroup: false,
                      status: AbnormalCurrentStatusEnum.PENDING,
                      deviceId: actionItem.deviceId,
                      releaseMinuteDuration: +actionItem.releaseTime,
                      releaseDateTime: dayjs().add(+actionItem.releaseTime, 'minutes').toDate(),
                      releaseScript: replacePlaceholders(actionItem.releaseScript, { targetIp: ip }),
                    } as AbnormalCurrent;
                  });

                ac.releaseDateTime = children?.[0]?.releaseDateTime;
                ac.releaseMinuteDuration = children?.[0]?.releaseMinuteDuration;
                ac.releaseScript = children.map((item) => item.releaseScript).join('\n');
              } else if (isExistExecuteResult && !a.isGroup) {
                // 存在执行结果但异常记录不为组类型（单IP）
                const r = executeResultList.find((r) => {
                  return r.isOk && r.cmdItem?.id === a.id;
                });

                if (isExist(r)) {
                  const { ip, itemid } = r.cmdItem;
                  const actionItem = actionItems.find((i) => i.id == itemid);
                  ac = Object.assign({}, ac, {
                    status: AbnormalCurrentStatusEnum.PENDING,
                    deviceId: actionItem.deviceId,
                    releaseMinuteDuration: +actionItem.releaseTime,
                    releaseDateTime: dayjs().add(+actionItem.releaseTime, 'minutes').toDate(),
                    releaseScript: replacePlaceholders(actionItem.releaseScript, { targetIp: ip }),
                  });
                }
              } else {
                // 不存在执行结果的情况
                const { releaseTime } = actionItems[0];
                ac = Object.assign({}, ac, {
                  releaseMinuteDuration: +releaseTime,
                  releaseDateTime: dayjs().add(+releaseTime, 'minutes').toDate(),
                });
              }

              const { raw } = await runner.insert(AbnormalCurrent, ac);

              if (isExist(children)) {
                return runner.insert(
                  AbnormalCurrent,
                  children.map((c) => ({ ...c, parentId: raw.insertId })),
                );
              }
            })
            .value(),
        );
      });
    } catch (error) {
      console.log('Action Add CurrentAbnormal error.message :>> ', error.message);
    }
  }

  async sendItemExecuteErrorDiscord(
    executeResultList: ExecuteResult[],
    actionItems: ActionPolicyItem[],
    abnormalList: AbnormalItem[],
  ) {
    chain(executeResultList)
      .filter((result) => !result.isOk)
      .forEach((result) => {
        const item = actionItems.find((i) => i.id === +result.cmdItem?.itemid);
        const abnormal = abnormalList.find(({ id }) => id === result.cmdItem?.id);
        const content = replacePlaceholders(item.actionErrorDiscordContent, {
          targetIp: abnormal?.ip,
          values: abnormal?.values,
          term: abnormal?.term,
        });
        adsHttp.sendDiscord(content);
      })
      .value();
  }

  async sendActionExecuteSuccessDiscord(executeResultList: ExecuteResult[], abnormalList: AbnormalItem[]) {
    chain(abnormalList)
      .filter((abnormal) => {
        return abnormal.actionItems?.some((actionItem) => {
          return actionItem.actionType === ActionTypeEnum.SEND_DISCORD_MSG;
        });
      })
      .forEach((abnormal) => {
        const currentExecuteResult = executeResultList?.filter(({ cmdItem }) => {
          return cmdItem.id === abnormal.id;
        });

        const isExistError = isExist(currentExecuteResult) && currentExecuteResult.some((result) => !result.isOk);

        if (isExistError) return;

        const discordActionItems = abnormal.actionItems.filter((actionItem) => {
          return actionItem.actionType === ActionTypeEnum.SEND_DISCORD_MSG;
        });

        discordActionItems.forEach((discordAction) => {
          const content = replacePlaceholders(discordAction.actionScript, {
            targetIp: abnormal.ip,
            values: abnormal.values,
            term: abnormal.term,
            action: {
              to: dayjs().add(+discordAction.releaseTime, 'minutes').format('MM/DD HH:mm'),
            },
          });
          adsHttp
            .sendDiscord(content)
            .then(() => {})
            .catch(() => logger.error(`执行异常操作发送Discord信息失败`));
        });
      })
      .value();
  }
}

export type ExecuteResult = {
  prefix: string;
  isOk: boolean;
  cmdItem: CommandItem;
};

export type AbnormalItem = {
  id: number;
  ip: string;
  actionId: number;
  analyzeId?: number;
  values: Record<string, any>;
  isGroup?: boolean;
  termId?: number;
  term?: Record<string, any>;
  actionItems?: ActionPolicyItem[];
  children?: AbnormalItem[];
};

export type CommandItem = {
  id: number;
  did: number;
  aid: number;
  itemid: number;
  uuid: string;
  ip: string;
  cmd: string;
  cid?: number;
};

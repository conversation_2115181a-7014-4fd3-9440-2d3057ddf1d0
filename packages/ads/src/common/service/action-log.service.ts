import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ActionLog } from 'src/modules/action/entities/action-log.entity';
import { Repository } from 'typeorm';

@Injectable()
export class ActionLogService {
  constructor(
    @InjectRepository(ActionLog)
    private readonly actionLogRepository: Repository<ActionLog>,
  ) {}

  async insert(actionLog: ActionLog | ActionLog[]) {
    return this.actionLogRepository.insert(actionLog);
  }

  async success(actionDetail: string, otherInfo?: Partial<ActionLog>) {
    const actionLog: Partial<ActionLog> = {
      ...otherInfo,
      actionDetail,
      status: true,
    };

    return this.actionLogRepository.insert(actionLog);
  }

  async error(actionDetail: string, otherInfo?: Partial<ActionLog>) {
    const actionLog: Partial<ActionLog> = {
      ...otherInfo,
      actionDetail,
      status: false,
    };

    return this.actionLogRepository.insert(actionLog);
  }
}

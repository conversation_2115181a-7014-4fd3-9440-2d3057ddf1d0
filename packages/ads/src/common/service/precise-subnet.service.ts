import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { chain } from 'lodash';
import { IPConfig } from 'src/modules/ip-config/entities/ip-config.entity';
import { isEmpty } from 'src/utils/types';
import { Brackets, Repository } from 'typeorm';
import { findBestSubnet } from '../utils';

/**
 * 获取IP配置
 */

@Injectable()
export class PreciseSubnetService {
  constructor(
    @InjectRepository(IPConfig)
    private readonly ipConfigRepository: Repository<IPConfig>,
  ) {}

  async bindPreciseSubnet(arr: Record<string, any>[], ipField: string, type: 'IP' | 'Subnet') {
    const ipList = arr.map((a) => a[ipField]);
    const ipMaskGroup = await this.getIPMaskGroup(ipList);

    arr.forEach((a) => {
      const prefix = this.getPrefix(a[ipField]);
      const ipMaskItem = ipMaskGroup[prefix] ?? ipMaskGroup['0.0.0'];

      if (isEmpty(ipMaskItem)) return;

      const ipadd = a[ipField];
      const subnets = ipMaskItem.map((i) => i.split('---')[0]);
      const bestSubnet = findBestSubnet(ipadd, subnets);

      if (bestSubnet) {
        const confStr = ipMaskItem.find((i) => i.startsWith(bestSubnet));
        if (!confStr) return;

        const confArr = confStr.split('---');
        const bestSubnetId = confArr[1] ? +confArr[1] : undefined;
        const analyzePolicyId = type === 'IP' ? confArr[2] : confArr[3];

        a.bestSubnetId = bestSubnetId;
        a.analyzePolicyId = analyzePolicyId ? +analyzePolicyId : undefined;
      }
    });
  }

  async getIPMaskGroup(ipList: string[]) {
    const prefixList = this.getPrefixKeys(ipList);

    const builder = this.ipConfigRepository
      .createQueryBuilder()
      .select(['id', 'ip', 'prefixLength', 'ipAnalyzePolicyId', 'subnetAnalyzePolicyId'])
      .where('status = "Active"');

    // if (type === 'IP') {
    //   builder.andWhere('ipAnalyzePolicyId IS NOT NULL');
    // } else if (type === 'Subnet') {
    //   builder.andWhere('subnetAnalyzePolicyId IS NOT NULL');
    // }

    builder.andWhere(
      new Brackets((qb) => {
        qb.where('ip = "0.0.0.0"');
        prefixList.forEach((prefix, index) => {
          qb.orWhere('ip LIKE :prefix' + index, {
            ['prefix' + index]: `${prefix}%`,
          });
        });
      }),
    );

    const results = await builder.getRawMany();

    return this.groupByPrefix(results);
  }

  getPrefixKeys(ips: string[]) {
    return ips.map((ip) => this.getPrefix(ip));
  }

  getPrefix(ip: string) {
    return ip.split('.').splice(0, 3).join('.');
  }

  groupByPrefix(results: IPConfig[]) {
    return chain(results)
      .groupBy((item) => {
        return item.ip.split('.').slice(0, 3).join('.');
      })
      .mapValues((group) => {
        return group.map((item) => {
          const { id, ip, prefixLength, ipAnalyzePolicyId, subnetAnalyzePolicyId } = item;

          const subnet = `${ip}/${prefixLength}`;

          return [subnet, id, ipAnalyzePolicyId, subnetAnalyzePolicyId].join('---');
        });
      })
      .value();
  }
}

import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';

import { BaseModules } from './base';
import { ReceiveModule } from './modules/receive/receive.module';
import { AnalyzeModule } from './modules/analyze/analyze.module';
import { ActionModule } from './modules/action/action.module';
import { FlowModule } from './modules/flow/flow.module';
import { AbnormalModule } from './modules/abnormal/abnormal.module';
import { DeviceModule } from './modules/device/device.module';
import { AdminLogModule } from './modules/admin-log/admin-log.module';
import { IpConfigModule } from './modules/ip-config/ip-config.module';
import { ConfModule } from './modules/conf/conf.module';
import { ReleaseModule } from './modules/release/release.module';
import { AnalyzeGroupModule } from './modules/analyze-group/analyze-group.module';
import { PullModule } from './modules/pull/pull.module';
import { ActiveIPsModule } from './modules/active-ips/active-ips.module';
import { SyncOPSModule } from './modules/sync-ops/sync-ops.module';
import { WebhookModule } from './modules/webhook/webhook.module';

@Module({
  imports: [
    ...BaseModules,
    ConfModule,
    ReceiveModule,
    AnalyzeModule,
    ActionModule,
    ReleaseModule,
    AbnormalModule,
    AdminLogModule,
    DeviceModule,
    IpConfigModule,
    FlowModule,
    AnalyzeGroupModule,
    PullModule,
    ActiveIPsModule,
    SyncOPSModule,
    WebhookModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}

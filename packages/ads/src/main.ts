import 'dotenv/config';
import 'src/utils/__prototype__';
import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { urlencoded } from 'express';
import { json } from 'express';
import { AllExceptionsFilter } from './filter/all-execptions.filter';
import { AppAuthGuard } from './app.guard';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  const { httpAdapter } = app.get(HttpAdapterHost);
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapter));
  app.useGlobalGuards(new AppAuthGuard());
  app.use(urlencoded({ extended: true, limit: '50mb' }));
  app.use(json({ limit: '50mb' }));

  await app.listen(8004);
}
bootstrap();

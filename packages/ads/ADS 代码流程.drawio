<mxfile host="65bd71144e">
    <diagram name="第 1 页" id="G-C_mfnJFh3uThuToTOx">
        <mxGraphModel dx="2089" dy="1184" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="0" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="24" value="" style="shape=flexArrow;endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="320" y="300" as="sourcePoint"/>
                        <mxPoint x="560" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="Database &amp;amp; Redis" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="650" y="700" width="120" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="" style="shape=flexArrow;endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="599" y="380" as="sourcePoint"/>
                        <mxPoint x="599" y="470" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="Analyze IP/Subnet Flow" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="520" width="280" height="90" as="geometry">
                        <mxRectangle x="810" y="820" width="80" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="58" value="2. 将分析结果存入Abnormal表" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="56" vertex="1">
                    <mxGeometry y="30" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;1. 获取未分析的Flow History数据进行分析&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="56" vertex="1">
                    <mxGeometry y="60" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="Analyze Schedule" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;" parent="1" vertex="1">
                    <mxGeometry x="190" y="520" width="180" height="90" as="geometry">
                        <mxRectangle x="810" y="820" width="80" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="1. Cron(5s) Analyze IP" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="61" vertex="1">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="64" value="2. Cron(5s) Analyze Subnet" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="61" vertex="1">
                    <mxGeometry y="60" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="Push" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry y="240" width="280" height="120" as="geometry">
                        <mxRectangle x="810" y="820" width="80" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="1. 监听 /flow 文件夹中 NFD 文件的创建" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="68" vertex="1">
                    <mxGeometry y="30" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="2. 解析 NDF 文件为 JSON" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="68" vertex="1">
                    <mxGeometry y="60" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="71" value="3. 推送到 Receive 模块" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="68" vertex="1">
                    <mxGeometry y="90" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="&lt;span style=&quot;text-align: left;&quot;&gt;Receive&lt;/span&gt;" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="240" width="300" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="81" value="1. 映射 NDF 字段为数据库 Flow History 表字段" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="80" vertex="1">
                    <mxGeometry y="30" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="2. 存入数据库和Redis" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="80" vertex="1">
                    <mxGeometry y="60" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="82" value="&amp;nbsp;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="80" vertex="1">
                    <mxGeometry y="90" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="85" value="" style="shape=flexArrow;endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="690" y="620" as="sourcePoint"/>
                        <mxPoint x="690" y="690" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="86" value="" style="shape=flexArrow;endArrow=classic;html=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="740" y="680" as="sourcePoint"/>
                        <mxPoint x="740" y="620" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="87" value="Action Exec" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="580" y="880" width="270" height="120" as="geometry">
                        <mxRectangle x="810" y="820" width="80" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="98" value="&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;1. 从 Abnormal 中获取异常IP&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="87">
                    <mxGeometry y="30" width="270" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="88" value="&lt;span style=&quot;color: rgb(240, 240, 240); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(42, 37, 47); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;&quot;&gt;1. 从 Abnormal 中获取异常IP&lt;/span&gt;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="87" vertex="1">
                    <mxGeometry y="60" width="270" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="89" value="2. 将分析结果存入Abnormal表" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" parent="87" vertex="1">
                    <mxGeometry y="90" width="270" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="91" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="62" target="58">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="92" value="Action Schedule" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;align=center;" vertex="1" parent="1">
                    <mxGeometry x="190" y="895" width="180" height="60" as="geometry">
                        <mxRectangle x="810" y="820" width="80" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="93" value="1. Cron(5s) Action Exec" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="92">
                    <mxGeometry y="30" width="180" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="95" style="edgeStyle=none;html=1;exitX=0.983;exitY=-0.033;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="93" target="98">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="570" y="925" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="96" value="" style="shape=flexArrow;endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="684.5" y="800" as="sourcePoint"/>
                        <mxPoint x="684.5" y="870" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="97" value="" style="shape=flexArrow;endArrow=classic;html=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="734.5" y="860" as="sourcePoint"/>
                        <mxPoint x="734.5" y="800" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="99" value="Action Schedule" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="-220" y="1750" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="102" value="Cron(5s) Action Exec" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="99">
                    <mxGeometry y="30" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="103" value="Action-Service-Exec-Action" style="swimlane;fontStyle=0;childLayout=stackLayout;horizontal=1;startSize=30;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="60" y="1730" width="370" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="104" value="1. 获取最新已执行的 Abnormal ID" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="103">
                    <mxGeometry y="30" width="370" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="105" value="2. 获取 Abnormal IPS，去重并以actionPolicyId分组" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="103">
                    <mxGeometry y="60" width="370" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="107" value="3. 获取已经执行过的 triggered&amp;nbsp;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="103">
                    <mxGeometry y="90" width="370" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="106" value="4.&amp;nbsp;" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;spacingLeft=4;spacingRight=4;overflow=hidden;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;rotatable=0;whiteSpace=wrap;html=1;" vertex="1" parent="103">
                    <mxGeometry y="120" width="370" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>

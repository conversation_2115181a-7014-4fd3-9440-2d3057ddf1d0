# 增强 Journal Item 接口查询配置

## 变更日期
2025-07-11

## 变更描述
增强 `trans/journal_item/page` 接口的查询配置，添加 `transManualId` 字段筛选并连接相关表，提供更丰富的查询和显示功能。

## 变更内容

### 新增的筛选字段 (fieldEq)

| 字段 | 列名 | 请求参数 | 说明 |
|------|------|----------|------|
| journalId | a.journalId | journalId | Journal ID（原有） |
| transManualId | journal.transManualId | transManualId | 关联的手工录入交易ID（新增） |
| customerId | a.customerId | customerId | 客户ID |
| vendorId | a.vendorId | vendorId | 供应商ID |
| accountChartId | a.accountChartId | accountChartId | 会计科目ID |
| status | journal.status | status | Journal 状态 |

### 新增的关键字搜索字段 (keyWordLikeFields)

| 字段 | 说明 |
|------|------|
| a.notes | Journal Item 备注 |
| journal.journalNo | Journal 流水号 |
| journal.description | Journal 描述 |
| customer.customerNo | 客户编号 |
| customer.alias | 客户别名 |
| vendor.vendorNo | 供应商编号 |
| vendor.alias | 供应商别名 |
| chart.accountCode | 会计科目代码 |
| chart.accountName | 会计科目名称 |

### 新增的连表查询 (join)

| 表 | 别名 | 连接条件 | 类型 |
|----|------|----------|------|
| TransJournalEntity | journal | a.journalId = journal.id | leftJoin |
| CustomerEntity | customer | a.customerId = customer.id | leftJoin |
| VendorEntity | vendor | a.vendorId = vendor.id | leftJoin |
| AccountChartEntity | chart | a.accountChartId = chart.id | leftJoin |

### 新增的返回字段 (select)

| 字段 | 说明 |
|------|------|
| a.* | Journal Item 所有字段 |
| journal.journalNo | Journal 流水号 |
| journal.description as journalDescription | Journal 描述 |
| journal.status as journalStatus | Journal 状态 |
| journal.transManualId | 关联的 Manual ID |
| customer.customerNo | 客户编号 |
| customer.alias as customerAlias | 客户别名 |
| vendor.vendorNo | 供应商编号 |
| vendor.alias as vendorAlias | 供应商别名 |
| chart.accountCode | 会计科目代码 |
| chart.accountName | 会计科目名称 |
| chart.accountAlias | 会计科目别名 |

## 使用示例

### 按 transManualId 查询
```javascript
// 查询特定 manual 记录对应的所有 journal items
POST /admin/trans/journal_item/page
{
  "transManualId": 123
}
```

### 按客户查询
```javascript
// 查询特定客户的所有 journal items
POST /admin/trans/journal_item/page
{
  "customerId": 456
}
```

### 按会计科目查询
```javascript
// 查询特定会计科目的所有 journal items
POST /admin/trans/journal_item/page
{
  "accountChartId": 789
}
```

### 组合查询
```javascript
// 查询特定 manual 的已分类 journal items
POST /admin/trans/journal_item/page
{
  "transManualId": 123,
  "status": "已分类"
}
```

### 关键字搜索
```javascript
// 按客户编号搜索
POST /admin/trans/journal_item/page
{
  "keyWords": "CUST-001"
}

// 按会计科目代码搜索
POST /admin/trans/journal_item/page
{
  "keyWords": "1001"
}
```

## 返回数据示例

```json
{
  "code": 1000,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "journalId": 100,
        "customerId": 456,
        "vendorId": null,
        "accountChartId": 789,
        "debit": 1000.00,
        "credit": 0.00,
        "notes": "客户付款",
        "journalNo": "JNL-2025-001",
        "journalDescription": "客户付款记录",
        "journalStatus": "已分类",
        "transManualId": 123,
        "customerNo": "CUST-001",
        "customerAlias": "ABC公司",
        "vendorNo": null,
        "vendorAlias": null,
        "accountCode": "1001",
        "accountName": "银行存款",
        "accountAlias": "工商银行"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1
    }
  }
}
```

## 业务价值

1. **关联查询**：可以通过 `transManualId` 查询特定交易的所有分录明细
2. **丰富筛选**：支持按客户、供应商、会计科目等多维度筛选
3. **完整信息**：返回数据包含关联表的详细信息，减少前端额外请求
4. **搜索功能**：支持按多个字段进行关键字搜索
5. **财务分析**：便于财务人员进行账目分析和核对

## 测试建议

1. **基础查询测试**
2. **筛选功能测试**
3. **关键字搜索测试**
4. **性能测试**（确保连表查询性能良好）
5. **数据完整性测试**（验证返回的关联数据正确性）

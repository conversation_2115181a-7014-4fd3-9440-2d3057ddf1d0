# Trans Manual 新增记账状态字段

## 变更日期
2025-07-11

## 变更描述
为 `op_trans_manual` 表新增 `journalStatus` 字段，用于标记每条交易记录的记账状态，方便财务人员识别哪些账目还没有进行分类记账。

## 业务需求
- 每条 trans-manual 记录都应该有对应的 trans-journal 记录
- 财务人员需要清楚知道哪些账目还没有进行分类记账
- 前端需要根据状态进行相应的显示判断

## 变更内容

### 新增字段

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| journalStatus | varchar | 20 | 'Pending' | NO | 记账状态：Pending-待记账, Journaled-已记账, Classified-已分类 |

### 状态说明

| 状态值 | 说明 | 触发条件 |
|--------|------|----------|
| Pending | 待记账 | 新创建的 manual 记录，尚未生成 journal |
| Journaled | 已记账 | 已生成对应的 journal 记录，但尚未分类 |
| Classified | 已分类 | journal 状态已设置为"已分类" |

### SQL 语句

```sql
-- 新增字段
ALTER TABLE `op_trans_manual` 
ADD COLUMN `journalStatus` varchar(20) NOT NULL DEFAULT 'Pending' COMMENT '记账状态：Pending-待记账, Journaled-已记账, Classified-已分类' AFTER `cdWhTranId`;

-- 更新现有数据：如果已经有关联的 journal，则设置为 Journaled
UPDATE `op_trans_manual` m 
SET `journalStatus` = 'Journaled' 
WHERE EXISTS (
    SELECT 1 FROM `op_trans_journal` j 
    WHERE j.transManualId = m.id
);

-- 更新现有数据：如果 journal 状态为已分类，则设置为 Classified
UPDATE `op_trans_manual` m 
SET `journalStatus` = 'Classified' 
WHERE EXISTS (
    SELECT 1 FROM `op_trans_journal` j 
    WHERE j.transManualId = m.id 
    AND j.status = '已分类'
);
```

## 影响范围

### 代码变更
1. **实体类**: `src/modules/trans/entity/manual.ts` - 新增 journalStatus 字段定义
2. **控制器**: `src/modules/trans/controller/admin/manual.ts` - 新增 journalStatus 字段查询
3. **服务层**: `src/modules/trans/service/journal.ts` - 自动更新状态逻辑

### 自动状态更新逻辑
- **创建 journal 时**: 自动将关联的 manual 状态设置为 'Journaled'
- **更新 journal 状态为"已分类"时**: 自动将关联的 manual 状态设置为 'Classified'

### 前端使用建议
```javascript
// 筛选待记账的记录
const pendingRecords = records.filter(r => r.journalStatus === 'Pending')

// 筛选已记账但未分类的记录
const journaledRecords = records.filter(r => r.journalStatus === 'Journaled')

// 筛选已分类的记录
const classifiedRecords = records.filter(r => r.journalStatus === 'Classified')

// 状态显示
const statusDisplay = {
  'Pending': { text: '待记账', color: 'orange' },
  'Journaled': { text: '已记账', color: 'blue' },
  'Classified': { text: '已分类', color: 'green' }
}
```

## 测试建议
1. 测试新创建的 manual 记录默认状态为 'Pending'
2. 测试创建 journal 时 manual 状态自动更新为 'Journaled'
3. 测试更新 journal 状态为"已分类"时 manual 状态自动更新为 'Classified'
4. 测试前端筛选功能，确认可以按状态筛选记录
5. 验证现有数据的状态更新是否正确

## 回滚方案
如需回滚，执行以下 SQL：

```sql
-- 删除新增的字段
ALTER TABLE `op_trans_manual` 
DROP COLUMN `journalStatus`;
```

# 产品新增多选父级产品功能及产品和套餐内容字段优化的数据库表结构变更

```sql
-- 删除 op_product 和 op_product_plan 表中的 content 字段
ALTER TABLE op_product DROP COLUMN content;
ALTER TABLE op_product_plan DROP COLUMN content;

-- 在 op_product_plan 表中新增 description 字段
ALTER TABLE op_product_plan ADD COLUMN description TEXT NULL;

-- 修改 op_product 表字段 description 类型
ALTER TABLE op_product MODIFY COLUMN description TEXT NULL;

-- 创建 Product 和 Addon 映射表
CREATE TABLE `op_product_addon_map` (
  `productId` int NOT NULL,
  `addonId` int NOT NULL,
  PRIMARY KEY (`productId`,`addonId`),
  KEY `FK_fe7d87b76124517285ffb6fa7ad` (`addonId`),
  CONSTRAINT `FK_03ba28ba6dad2b4bede96c415cc` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`),
  CONSTRAINT `FK_fe7d87b76124517285ffb6fa7ad` FOREIGN KEY (`addonId`) REFERENCES `op_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

# 产品模块数据库表结构变更文档

```sql
-- 修改字段 createBy 和 updateBy，新增默认值
ALTER TABLE op_product
MODIFY COLUMN createBy VARCHAR(255) DEFAULT '',
MODIFY COLUMN updateBy VARCHAR(255) DEFAULT '';

-- 新增套餐和产品价格映射表（op_product_plan_price_map）
CREATE TABLE `op_product_plan_price_map` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `productPlanId` INT NOT NULL COMMENT '套餐ID',
  `productPriceId` INT NOT NULL COMMENT '产品价格ID',
  `productQty` INT NOT NULL DEFAULT 1 COMMENT '产品数量',
  PRIMARY KEY (`id`),
  KEY `IDX_createTime` (`createTime`),
  KEY `IDX_updateTime` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 新增套餐价格表（op_product_plan_price）
CREATE TABLE `op_product_plan_price` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `status` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '状态 (Active / Inactive)',
  `currency` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '币种',
  `unit` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '报价单位',
  `tier` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '价格等级 (T1-List/T2-Reseller/T3-Partner)',
  `tierDiscountRate` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠百分比',
  `tierPrice` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `validFrom` DATE DEFAULT NULL COMMENT '生效开始日',
  `validTo` DATE DEFAULT NULL COMMENT '生效截止日',
  `tierTerms` TEXT COMMENT '套餐等级条款',
  `createTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `productPlanId` INT NOT NULL COMMENT '产品套餐ID',
  PRIMARY KEY (`id`),
  KEY `IDX_createTime` (`createTime`),
  KEY `IDX_updateTime` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 修改套餐表字段 planName 为 name
ALTER TABLE op_product_plan
CHANGE COLUMN planName name VARCHAR(255) DEFAULT '';

-- 删除无用字段
ALTER TABLE op_product_plan
DROP COLUMN itemName,
DROP COLUMN currency,
DROP COLUMN unit,
DROP COLUMN t1DiscountRate,
DROP COLUMN t2DiscountRate,
DROP COLUMN t3DiscountRate,
DROP COLUMN t1PlanPrice,
DROP COLUMN t2PlanPrice,
DROP COLUMN t3PlanPrice,
DROP COLUMN validFrom,
DROP COLUMN validTo;

-- 新增 content 字段
ALTER TABLE op_product_plan
ADD COLUMN content TEXT NULL;

-- 删除产品价格表无用字段
ALTER TABLE op_product_price
DROP COLUMN itemName,
DROP COLUMN description,
DROP COLUMN priceGroupId;

-- 新增产品表字段 subCategory 和 region
ALTER TABLE op_product
ADD COLUMN subCategory VARCHAR(255) DEFAULT '' NULL,
ADD COLUMN region VARCHAR(255) DEFAULT '' NULL;

-- 新增产品报价项目字段 itemBasePrice
ALTER TABLE op_product_quote_item
ADD COLUMN itemBasePrice DECIMAL(10, 2) DEFAULT 0 COMMENT '项目基本价格';

```

下边是每个表的修改

## 产品选项（op_product_option）

```sql
-- 修改字段 createBy 和 updateBy，新增默认值
ALTER TABLE op_product
MODIFY COLUMN createBy VARCHAR(255) DEFAULT '',
MODIFY COLUMN updateBy VARCHAR(255) DEFAULT '';
```

## 新增套餐和产品价格映射表（op_product_plan_price_map）

```sql
CREATE TABLE `op_product_plan_price_map` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `productPlanId` INT NOT NULL COMMENT '套餐ID',
  `productPriceId` INT NOT NULL COMMENT '产品价格ID',
  `productQty` INT NOT NULL DEFAULT 1 COMMENT '产品数量',
  PRIMARY KEY (`id`),
  KEY `IDX_createTime` (`createTime`),
  KEY `IDX_updateTime` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 新增套餐价格表（op_product_plan_price）

```sql
CREATE TABLE `op_product_plan_price` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `status` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '状态 (Active / Inactive)',
  `currency` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '币种',
  `unit` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '报价单位',
  `tier` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '价格等级 (T1-List/T2-Reseller/T3-Partner)',
  `tierDiscountRate` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠百分比',
  `tierPrice` DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '等级价格',
  `validFrom` DATE DEFAULT NULL COMMENT '生效开始日',
  `validTo` DATE DEFAULT NULL COMMENT '生效截止日',
  `tierTerms` TEXT COMMENT '套餐等级条款',
  `createTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `productPlanId` INT NOT NULL COMMENT '产品套餐ID',
  PRIMARY KEY (`id`),
  KEY `IDX_createTime` (`createTime`),
  KEY `IDX_updateTime` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 套餐表（op_product_plan）

```sql
-- 修改字段 planName 为 name
ALTER TABLE op_product_plan
CHANGE COLUMN planName name VARCHAR(255) DEFAULT '';

-- 删除无用字段
ALTER TABLE op_product_plan
DROP COLUMN itemName,
DROP COLUMN currency,
DROP COLUMN unit,
DROP COLUMN t1DiscountRate,
DROP COLUMN t2DiscountRate,
DROP COLUMN t3DiscountRate,
DROP COLUMN t1PlanPrice,
DROP COLUMN t2PlanPrice,
DROP COLUMN t3PlanPrice,
DROP COLUMN validFrom,
DROP COLUMN validTo;

-- 新增 content 字段
ALTER TABLE op_product_plan
ADD COLUMN content TEXT NULL;
```

## 产品价格（op_product_price）

```sql
-- 删除无用字段
ALTER TABLE op_product_price
DROP COLUMN itemName,
DROP COLUMN description,
DROP COLUMN priceGroupId,
DROP COLUMN region,
DROP COLUMN location;
```

## 产品表（op_product）

```sql
-- 新增字段 subCategory 和 region
ALTER TABLE op_product
ADD COLUMN subCategory VARCHAR(255) DEFAULT '' NULL,
ADD COLUMN region VARCHAR(255) DEFAULT '' NULL;
ADD COLUMN content TEXT NULL;
```

## 产品报价项目表（op_product_quote_item）

```sql
-- 新增字段 itemBasePrice
ALTER TABLE op_product_quote_item
ADD COLUMN itemBasePrice DECIMAL(10, 2) DEFAULT 0 COMMENT '项目基本价格';
```

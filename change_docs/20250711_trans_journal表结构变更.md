# Trans Journal 表结构变更

## 变更日期
2025-07-11

## 变更描述
1. 为 `op_trans_journal` 表新增4个字段，用于记录交易的原始货币和金额信息
2. 移除 `op_trans_journal_item` 表中的 `currency` 和 `originalAmount` 字段

## 变更内容

### op_trans_journal 表新增字段

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| originCurrentCurrency | varchar | 10 | '' | NO | 原始当前货币 |
| originCurrentAmount | decimal | 15,2 | 0.00 | NO | 原始当前金额 |
| originCounterCurrency | varchar | 10 | '' | NO | 原始对手货币 |
| originCounterAmount | decimal | 15,2 | 0.00 | NO | 原始对手金额 |

### op_trans_journal_item 表移除字段

| 字段名 | 原类型 | 说明 |
|--------|--------|------|
| currency | varchar(255) | 币种字段，不再需要 |
| originalAmount | decimal(10,2) | 原币种金额字段，不再需要 |

### SQL 语句

```sql
-- 为 op_trans_journal 表新增字段
ALTER TABLE `op_trans_journal` 
ADD COLUMN `originCurrentCurrency` varchar(10) NOT NULL DEFAULT '' COMMENT '原始当前货币' AFTER `rateToBcy`,
ADD COLUMN `originCurrentAmount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '原始当前金额' AFTER `originCurrentCurrency`,
ADD COLUMN `originCounterCurrency` varchar(10) NOT NULL DEFAULT '' COMMENT '原始对手货币' AFTER `originCurrentAmount`,
ADD COLUMN `originCounterAmount` decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '原始对手金额' AFTER `originCounterCurrency`;

-- 移除 op_trans_journal_item 表中的字段
ALTER TABLE `op_trans_journal_item` 
DROP COLUMN `currency`,
DROP COLUMN `originalAmount`;
```

## 影响范围

### 代码变更
1. **实体类**: 
   - `src/modules/trans/entity/journal.ts` - 新增4个字段定义
   - `src/modules/trans/entity/journal_item.ts` - 移除2个字段定义
2. **控制器**: 
   - `src/modules/trans/controller/admin/journal.ts` - `/ops/add` 接口处理新字段
   - `src/modules/trans/controller/admin/journal_item.ts` - 移除 currency 字段查询
3. **服务层**: 
   - `src/modules/trans/service/journal.ts` - 更新相关方法，移除对 currency 字段的使用

### 功能影响
- 新增的 journal 记录将包含原始货币和金额信息
- journal item 不再包含 currency 和 originalAmount 字段
- 从 manual 自动生成 journal 时会自动填充 journal 的新字段
- 现有数据不受影响，新字段使用默认值

## 测试建议
1. 测试通过 `/admin/trans/journal/ops/add` 接口创建新的 journal 记录
2. 验证新字段是否正确保存到数据库
3. 测试从 manual 自动生成 journal 的功能
4. 确认现有的 journal 和 journal item 查询和更新功能正常工作
5. 验证移除字段后的 journal item 功能正常

## 回滚方案
如需回滚，执行以下 SQL：

```sql
-- 删除 journal 表的新增字段
ALTER TABLE `op_trans_journal` 
DROP COLUMN `originCurrentCurrency`,
DROP COLUMN `originCurrentAmount`,
DROP COLUMN `originCounterCurrency`,
DROP COLUMN `originCounterAmount`;

-- 恢复 journal_item 表的字段
ALTER TABLE `op_trans_journal_item` 
ADD COLUMN `currency` varchar(255) NOT NULL DEFAULT '' COMMENT '币种' AFTER `accountChartId`,
ADD COLUMN `originalAmount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '原币种金额' AFTER `currency`;
```

# 新增 Inventory 模块新建数据库表

```sql
CREATE TABLE `op_inventory` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `inventoryNo` varchar(255) NOT NULL DEFAULT '' COMMENT 'Inventory 编号',
  `assetId` int DEFAULT NULL COMMENT '关联资产 ID',
  `poId` int DEFAULT NULL COMMENT '关联采购单 ID',
  `category` varchar(255) NOT NULL DEFAULT '' COMMENT '设备类型: Server/Router/PC/Storage',
  `subCategory` varchar(255) NOT NULL DEFAULT '' COMMENT '设备子类型',
  `status` varchar(255) NOT NULL DEFAULT '' COMMENT '状态: In Use/Idle/Repair/Disposed',
  `location` varchar(255) NOT NULL DEFAULT '' COMMENT '存放地点',
  `serialNumber` varchar(255) NOT NULL DEFAULT '' COMMENT '序列号',
  `model` varchar(255) NOT NULL DEFAULT '' COMMENT '型号',
  `brand` varchar(255) NOT NULL DEFAULT '' COMMENT '品牌',
  `managedBy` varchar(255) NOT NULL DEFAULT '' COMMENT '管理团队/人',
  `usedByTeam` varchar(255) NOT NULL DEFAULT '' COMMENT '当前设备使用团队',
  `specs` json DEFAULT NULL COMMENT '规格，JSON 格式存储',
  `tags` varchar(255) NOT NULL DEFAULT '' COMMENT '标签',
  `notes` text COMMENT '备注',
  `createBy` varchar(255) NOT NULL DEFAULT '' COMMENT '创建者',
  `updateBy` varchar(255) NOT NULL DEFAULT '' COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `IDX_0a0407b6f04b9d298b64cfb671` (`createTime`),
  KEY `IDX_10b84b13c781491002fe3462e2` (`updateTime`),
  KEY `FK_e60fad4de79ff1ecb105f88a880` (`assetId`),
  KEY `FK_66b8b35cef7736ed977c5aa3015` (`poId`),
  CONSTRAINT `FK_66b8b35cef7736ed977c5aa3015` FOREIGN KEY (`poId`) REFERENCES `op_purchase_order` (`id`),
  CONSTRAINT `FK_e60fad4de79ff1ecb105f88a880` FOREIGN KEY (`assetId`) REFERENCES `op_asset` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

# 新增邮箱模块新建数据库表

```sql
-- 创建邮箱账户表 op_mail_account
CREATE TABLE `op_mail_account` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `category` varchar(255) NOT NULL DEFAULT '' COMMENT '类别',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '账户状态',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `visibleRoleId` varchar(255) NOT NULL DEFAULT '' COMMENT '可见角色ID',
  `visibleUserId` varchar(255) NOT NULL DEFAULT '' COMMENT '可见用户ID',
  `accountId` varchar(255) NOT NULL DEFAULT '' COMMENT 'Zoho 账户 ID',
  `token` json DEFAULT NULL COMMENT 'OAuth 访问令牌',
  `email` varchar(255) NOT NULL DEFAULT '' COMMENT '邮箱地址',
  PRIMARY KEY (`id`),
  KEY `IDX_ea55cf9ab6f72ebf5ecef7b543` (`createTime`),
  KEY `IDX_3c55104c37f8be2d83d729c9fe` (`updateTime`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

# 订单模块数据库表结构变更文档

```sql
-- 删除字段 op_order 的 serviceId、serviceType（可选）
ALTER TABLE `op_order`
DROP COLUMN 'serviceId',
DROP COLUMN 'serviceType',

-- 新增字段 op_order 的 totalTierPrice、totalOrderPrice
ALTER TABLE `op_order`
ADD COLUMN `totalTierPrice` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '订单项目价格总数',
ADD COLUMN `totalOrderPrice` decimal(10, 2) NOT NULL DEFAULT 0 COMMENT '订单项目自定义价格总数';

-- 新增 op_order_log 表字段 orderItemId
ALTER TABLE `op_order_log`
ADD COLUMN `orderItemId` int DEFAULT NULL COMMENT '关联的订单项目ID';

-- 新增 op_order_item 表字段 orderItemId
ALTER TABLE `op_order_item`
ADD COLUMN `orderItemId` int DEFAULT NULL COMMENT '关联的订单项目ID';

-- 新增表 `op_order_item`
CREATE TABLE `op_order_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `productPriceId` int DEFAULT NULL COMMENT '关联的产品价格ID',
  `productPlanPriceId` int DEFAULT NULL COMMENT '关联的产品套餐价格ID',
  `parentItemId` int DEFAULT NULL COMMENT '关联的父级订单项目ID',
  `orderId` int NOT NULL COMMENT '关联的订单ID',
  `serviceId` int DEFAULT NULL COMMENT '关联的服务ID',
  `qty` int NOT NULL DEFAULT '1' COMMENT '产品/套餐数量',
  `productId` int DEFAULT NULL COMMENT '关联的产品ID',
  `productPlanId` int DEFAULT NULL COMMENT '关联的产品套餐ID',
  `serviceItemId` int DEFAULT NULL COMMENT '关联的子服务ID',
  `status` varchar(255) NOT NULL DEFAULT '' COMMENT '订单项目状态',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐名称',
  `location` varchar(255) NOT NULL DEFAULT '' COMMENT '地点',
  `tier` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐等级',
  `tierPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '产品/套餐价格',
  `orderPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '自定义价格',
  `description` text COMMENT '产品/套餐内容描述',
  `currency` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐币种',
  `billType` varchar(255) NOT NULL DEFAULT '' COMMENT '计费类型',
  PRIMARY KEY (`id`),
  KEY `IDX_6b7fafc2040ae9c0c719a03de6` (`createTime`),
  KEY `IDX_03f8d610a15842bb978076e42c` (`updateTime`),
  KEY `FK_b9a48be85639f1b404aa31cf950` (`orderId`),
  KEY `FK_3707e6fdd6127b36a6661c0f28f` (`serviceId`),
  KEY `FK_ab5cf7baccd88e5faa6b25a51e1` (`productId`),
  KEY `FK_23295f1f20916db3de96bc39b98` (`productPlanId`),
  KEY `FK_844adb9ead7a13b6782e938370b` (`productPriceId`),
  KEY `FK_85ed1b02dafe7dfe8bcb96a8b5f` (`productPlanPriceId`),
  CONSTRAINT `FK_23295f1f20916db3de96bc39b98` FOREIGN KEY (`productPlanId`) REFERENCES `op_product_plan` (`id`),
  CONSTRAINT `FK_3707e6fdd6127b36a6661c0f28f` FOREIGN KEY (`serviceId`) REFERENCES `op_service` (`id`),
  CONSTRAINT `FK_844adb9ead7a13b6782e938370b` FOREIGN KEY (`productPriceId`) REFERENCES `op_product_price` (`id`),
  CONSTRAINT `FK_85ed1b02dafe7dfe8bcb96a8b5f` FOREIGN KEY (`productPlanPriceId`) REFERENCES `op_product_plan_price` (`id`),
  CONSTRAINT `FK_ab5cf7baccd88e5faa6b25a51e1` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`),
  CONSTRAINT `FK_b9a48be85639f1b404aa31cf950` FOREIGN KEY (`orderId`) REFERENCES `op_order` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=213 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 新增表 `op_service_item`
CREATE TABLE `op_service_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `serviceId` int DEFAULT NULL COMMENT '服务ID',
  `productId` int DEFAULT NULL COMMENT '商品ID',
  `productPlanId` int DEFAULT NULL COMMENT '商品ID',
  `parentItemId` int DEFAULT NULL COMMENT '父级服务项目ID',
  `orderId` int DEFAULT NULL COMMENT '订单ID',
  `orderItemId` int DEFAULT NULL COMMENT '订单项目ID',
  `status` varchar(255) NOT NULL DEFAULT '' COMMENT '状态',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐名称',
  `location` varchar(255) NOT NULL DEFAULT '' COMMENT '地点',
  `tier` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐等级',
  `description` text COMMENT '产品/套餐内容描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '产品/套餐价格',
  `currency` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐币种',
  `qty` int NOT NULL DEFAULT '1' COMMENT '产品/套餐数量',
  `burstPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预期计费',
  `serviceDetails` text COMMENT '服务详情',
  `type` varchar(255) NOT NULL DEFAULT '' COMMENT '类型',
  `billStatus` varchar(255) NOT NULL DEFAULT 'Pending' COMMENT '计费状态',
  `billCycle` varchar(255) NOT NULL DEFAULT '' COMMENT '计费周期',
  `billCurrency` varchar(255) NOT NULL DEFAULT '' COMMENT '计费币种',
  `billType` varchar(255) NOT NULL DEFAULT '' COMMENT '计费类型',
  `billAmount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '计费金额',
  `activeDate` date DEFAULT NULL COMMENT '开通日期',
  `terminateDate` date DEFAULT NULL COMMENT '终止日期',
  `billStartDate` date DEFAULT NULL COMMENT '计费开始日期',
  `billStopDate` date DEFAULT NULL COMMENT '计费结束日期',
  `notes` varchar(2550) NOT NULL DEFAULT '' COMMENT '备注',
  `createBy` varchar(255) NOT NULL DEFAULT '' COMMENT '创建用户',
  `updateBy` varchar(255) NOT NULL DEFAULT '' COMMENT '更新用户',
  PRIMARY KEY (`id`),
  UNIQUE KEY `REL_572efc319b9f166fccf7970b28` (`orderItemId`),
  KEY `IDX_a4af99d6fa97fa55077ea7a091` (`createTime`),
  KEY `IDX_2bc8586fa59ee68405b8ffbd43` (`updateTime`),
  CONSTRAINT `FK_572efc319b9f166fccf7970b288` FOREIGN KEY (`orderItemId`) REFERENCES `op_order_item` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 新增订单项目表 `op_order_item`

```sql
CREATE TABLE `op_order_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `productPriceId` int DEFAULT NULL COMMENT '关联的产品价格ID',
  `productPlanPriceId` int DEFAULT NULL COMMENT '关联的产品套餐价格ID',
  `parentItemId` int DEFAULT NULL COMMENT '关联的父级订单项目ID',
  `orderId` int NOT NULL COMMENT '关联的订单ID',
  `serviceId` int DEFAULT NULL COMMENT '关联的服务ID',
  `qty` int NOT NULL DEFAULT '1' COMMENT '产品/套餐数量',
  `productId` int DEFAULT NULL COMMENT '关联的产品ID',
  `productPlanId` int DEFAULT NULL COMMENT '关联的产品套餐ID',
  `serviceItemId` int DEFAULT NULL COMMENT '关联的子服务ID',
  `status` varchar(255) NOT NULL DEFAULT '' COMMENT '订单项目状态',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐名称',
  `location` varchar(255) NOT NULL DEFAULT '' COMMENT '地点',
  `tier` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐等级',
  `tierPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '产品/套餐价格',
  `orderPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '自定义价格',
  `description` text COMMENT '产品/套餐内容描述',
  `currency` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐币种',
  `billType` varchar(255) NOT NULL DEFAULT '' COMMENT '计费类型',
  PRIMARY KEY (`id`),
  KEY `IDX_6b7fafc2040ae9c0c719a03de6` (`createTime`),
  KEY `IDX_03f8d610a15842bb978076e42c` (`updateTime`),
  KEY `FK_b9a48be85639f1b404aa31cf950` (`orderId`),
  KEY `FK_3707e6fdd6127b36a6661c0f28f` (`serviceId`),
  KEY `FK_ab5cf7baccd88e5faa6b25a51e1` (`productId`),
  KEY `FK_23295f1f20916db3de96bc39b98` (`productPlanId`),
  KEY `FK_844adb9ead7a13b6782e938370b` (`productPriceId`),
  KEY `FK_85ed1b02dafe7dfe8bcb96a8b5f` (`productPlanPriceId`),
  CONSTRAINT `FK_23295f1f20916db3de96bc39b98` FOREIGN KEY (`productPlanId`) REFERENCES `op_product_plan` (`id`),
  CONSTRAINT `FK_3707e6fdd6127b36a6661c0f28f` FOREIGN KEY (`serviceId`) REFERENCES `op_service` (`id`),
  CONSTRAINT `FK_844adb9ead7a13b6782e938370b` FOREIGN KEY (`productPriceId`) REFERENCES `op_product_price` (`id`),
  CONSTRAINT `FK_85ed1b02dafe7dfe8bcb96a8b5f` FOREIGN KEY (`productPlanPriceId`) REFERENCES `op_product_plan_price` (`id`),
  CONSTRAINT `FK_ab5cf7baccd88e5faa6b25a51e1` FOREIGN KEY (`productId`) REFERENCES `op_product` (`id`),
  CONSTRAINT `FK_b9a48be85639f1b404aa31cf950` FOREIGN KEY (`orderId`) REFERENCES `op_order` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=213 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```

## 新增服务项目表 `op_service_item`

```sql
CREATE TABLE `op_service_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `createTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
  `updateTime` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
  `serviceId` int DEFAULT NULL COMMENT '服务ID',
  `productId` int DEFAULT NULL COMMENT '商品ID',
  `productPlanId` int DEFAULT NULL COMMENT '商品ID',
  `parentItemId` int DEFAULT NULL COMMENT '父级服务项目ID',
  `orderId` int DEFAULT NULL COMMENT '订单ID',
  `orderItemId` int DEFAULT NULL COMMENT '订单项目ID',
  `status` varchar(255) NOT NULL DEFAULT '' COMMENT '状态',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐名称',
  `location` varchar(255) NOT NULL DEFAULT '' COMMENT '地点',
  `tier` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐等级',
  `description` text COMMENT '产品/套餐内容描述',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '产品/套餐价格',
  `currency` varchar(255) NOT NULL DEFAULT '' COMMENT '产品/套餐币种',
  `qty` int NOT NULL DEFAULT '1' COMMENT '产品/套餐数量',
  `burstPrice` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '预期计费',
  `serviceDetails` text COMMENT '服务详情',
  `type` varchar(255) NOT NULL DEFAULT '' COMMENT '类型',
  `billStatus` varchar(255) NOT NULL DEFAULT 'Pending' COMMENT '计费状态',
  `billCycle` varchar(255) NOT NULL DEFAULT '' COMMENT '计费周期',
  `billCurrency` varchar(255) NOT NULL DEFAULT '' COMMENT '计费币种',
  `billType` varchar(255) NOT NULL DEFAULT '' COMMENT '计费类型',
  `billAmount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '计费金额',
  `activeDate` date DEFAULT NULL COMMENT '开通日期',
  `terminateDate` date DEFAULT NULL COMMENT '终止日期',
  `billStartDate` date DEFAULT NULL COMMENT '计费开始日期',
  `billStopDate` date DEFAULT NULL COMMENT '计费结束日期',
  `notes` varchar(2550) NOT NULL DEFAULT '' COMMENT '备注',
  `createBy` varchar(255) NOT NULL DEFAULT '' COMMENT '创建用户',
  `updateBy` varchar(255) NOT NULL DEFAULT '' COMMENT '更新用户',
  PRIMARY KEY (`id`),
  UNIQUE KEY `REL_572efc319b9f166fccf7970b28` (`orderItemId`),
  KEY `IDX_a4af99d6fa97fa55077ea7a091` (`createTime`),
  KEY `IDX_2bc8586fa59ee68405b8ffbd43` (`updateTime`),
  CONSTRAINT `FK_572efc319b9f166fccf7970b288` FOREIGN KEY (`orderItemId`) REFERENCES `op_order_item` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
```
